/**
 * @file webpack 定义变量
 * <AUTHOR> (<EMAIL>)
 */

import {DefinePlugin} from 'webpack';
import {IApi} from '@baidu/cba-preset';

export default (api: IApi) => {
  api.describe({
    key: 'preset-pagemaker-npm:definedPlugin'
  });

  api.chainWebpack(config => {
    // 定义变量
    config.plugin('preset-pagemaker-npm:define').use(DefinePlugin, [
      {
        PAGEMAKER_MOUNT_FILE: JSON.stringify('pagemaker.development.js'),
        PAGEMAKER_COMPANY: JSON.stringify(api.userConfig.pagemaker?.company || 'console'),
        PAGEMAKER_APP_ID: JSON.stringify(api.userConfig.pagemaker?.appId)
      }
    ]);
  });
};
