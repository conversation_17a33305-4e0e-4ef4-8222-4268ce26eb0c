/**
 * 预览页面，仅用于开发预览，构建时不使用此页面
 */
import React from 'react';
import ReactDOM from 'react-dom';
import {hot} from 'react-hot-loader/root';

// 在平台预览时，将组件暂时注册到平台
if ((window as any).amis?.registerExternalApp) {
  import('./index');
  import('./plugin');
}

// 要预览的模块
import InfoCard from './info-card/renderer';

const App = hot(() => {
  return (
    <>
      <InfoCard />
    </>
  );
});

ReactDOM.render(<App />, document.getElementById('main'));
