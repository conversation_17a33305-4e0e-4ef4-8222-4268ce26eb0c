import React from 'react';
import {Button} from 'amis';
import {Link} from 'react-router-dom';
{{#imports}}
{{{imports}}}
{{/imports}}
import './index.scss';

import amisLogo from '../../assets/imgs/logo_amis.jpg';
import reactLogo from '../../assets/imgs/logo_react.svg';

const PageHome = (props: any) => {
  {{#stateScript}}
  {{{stateScript}}}
  {{/stateScript}}
  return (
    <div className="home-page">
      <div className="home-content">
        <div className="logo-wrap">
          <img src={reactLogo} />
          <img src={amisLogo} />
        </div>

        <div>
          <h1>React + Amis</h1>
        </div>
        <h3>易学易用，开箱即用，适用场景丰富的脚手架。</h3>
        <hr />
        <div>
          <Link to="/react" className="home-btn">
            <Button size="lg" level="enhance">
              React示例页
            </Button>
          </Link>
          <Link to="/amis">
            <Button size="lg" level="enhance">
              Amis示例页
            </Button>
          </Link>
        </div>
        {{#stateDemo}}
        {{{stateDemo}}}
        {{/stateDemo}}
      </div>
    </div>
  );
};

{{{exportComponent}}}
