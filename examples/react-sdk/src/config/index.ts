export enum LanguageType {
  /** 中文 */
  zhCN = 'zh-cn',
  /** 英文 */
  enUS = 'en-us'
}

export interface ToolkitConfigOptions {
  /** 支持的语言类型 */
  supportedLanguageTypes: Array<LanguageType>;
  /** 默认语言类型 */
  defaultLanguageType: LanguageType;
  /** 是否启用独立国际化 */
  enableIndependentI18n: boolean;
  /** 资源请求公共路径 */
  publicPath: string;
  /** 是否启动国际化 */
  enableI18n: boolean;
  /** 是否作为第三方模块，启用嵌入模式 */
  isEmbed: boolean;
  /** 应用标题 */
  appTitle: string;
}

class ToolkitConfig {
  supportedLanguageTypes: Array<LanguageType> = [
    LanguageType.zhCN,
    LanguageType.enUS
  ];
  private defaultLanguageType: LanguageType = LanguageType.zhCN;
  private enableIndependentI18n: boolean = false;
  private publicPath: string = '';
  private enableI18n: boolean = true;
  private isEmbed: boolean = false;
  private appTitle: string = '百度智能云';

  constructor(options: Partial<ToolkitConfigOptions> = {}) {
    Object.assign(this, options);
  }

  init(options: Partial<ToolkitConfigOptions> = {}) {
    Object.assign(this, options);
  }

  getConfig(key?: keyof ToolkitConfigOptions) {
    if (!key) {
      return this;
    }
    return this[key];
  }
}

const toolkitConfig = new ToolkitConfig();

export {toolkitConfig};
