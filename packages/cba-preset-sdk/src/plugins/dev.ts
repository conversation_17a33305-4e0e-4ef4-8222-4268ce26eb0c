import path from 'path';
import fs from 'fs';
import {execSync} from 'child_process';
import fse from 'fs-extra';
import inquirer from 'inquirer';
import {logger} from '@baidu/cba-utils';
import {IApi} from '@baidu/cba-preset';
import configuration from '../configuration';

export default (api: IApi) => {
  api.registerCommand({
    name: 'dev',
    description: 'SDK 调试',
    fn: async ({config}) => {
      configuration.setConfig(config);

      const cwd = process.cwd();
      const tempConfigFilePath = configuration.getTempConfigFilePath();

      fse.ensureFileSync(tempConfigFilePath);

      let tempConfigFileContent: string = fs.readFileSync(tempConfigFilePath, 'utf-8');

      let tempConfigFileParsedContent: {
        debugProjectPath?: string;
      } = {};

      if (!tempConfigFileContent) {
        tempConfigFileParsedContent = {};
        fs.writeFileSync(tempConfigFilePath, JSON.stringify(tempConfigFileParsedContent, undefined, 2), 'utf-8');
      } else {
        tempConfigFileParsedContent = JSON.parse(tempConfigFileContent);
      }

      const response = await inquirer.prompt([
        {
          type: 'input',
          name: 'debugProjectPath',
          message: '请输入进行调试目标项目的相对路径:',
          default: tempConfigFileParsedContent.debugProjectPath || ''
        }
      ]);

      const {debugProjectPath} = response;

      if (!fs.existsSync(debugProjectPath)) {
        logger.error(`The path ${debugProjectPath} is not found.\n`);
        process.exit(1);
      }

      tempConfigFileParsedContent.debugProjectPath = debugProjectPath;

      fs.writeFileSync(tempConfigFilePath, JSON.stringify(tempConfigFileParsedContent, undefined, 2), 'utf-8');

      const reactPath = path.resolve(debugProjectPath, 'node_modules/react');
      const reactDomPath = path.resolve(debugProjectPath, 'node_modules/react-dom');
      const reactRouterDomPath = path.resolve(debugProjectPath, 'node_modules/react-router-dom');

      if (!fs.existsSync(reactPath)) {
        logger.error(`The path ${reactPath} is not found.\n`);
        process.exit(1);
      }

      if (!fs.existsSync(reactDomPath)) {
        logger.error(`The path ${reactDomPath} is not found.\n`);
        process.exit(1);
      }

      if (!fs.existsSync(reactRouterDomPath)) {
        logger.error(`The path ${reactPath} is not found.\n`);
        process.exit(1);
      }

      const linkCommands = [
        `cd ${reactPath}`,
        'yarn link',
        `cd ${reactDomPath}`,
        'yarn link',
        `cd ${reactRouterDomPath}`,
        'yarn link',
        `cd ${cwd}`,
        'yarn link react',
        `yarn link react-dom`,
        'yarn link react-router-dom',
        'yarn link'
      ];

      const linkCommand = linkCommands.join(' && ');
      logger.info(`Starting to execute ${linkCommand}\n`);
      execSync(`${linkCommand}`, {
        stdio: 'inherit',
        cwd
      });
      logger.info('Execute yarn link successfully.');

      logger.info('Starting to update package.json...');
      const packageJsonFilePath = configuration.getPath('package.json');

      const packageJson = JSON.parse(fs.readFileSync(packageJsonFilePath, 'utf-8'));
      packageJson.module = 'src/index.ts';
      packageJson.main = 'src/index.ts';
      delete packageJson.typings;
      fs.writeFileSync(packageJsonFilePath, JSON.stringify(packageJson, undefined, 2));
      logger.info('Update package.json file successfully.');

      const packageName = packageJson.name;

      execSync(`cd ${debugProjectPath} && yarn link ${packageName} && cd ${cwd}`, {
        stdio: 'inherit'
      });

      logger.info(
        `All operations were executed successfully. Please go to the project ${debugProjectPath} for debugging ${packageName}.`
      );
    }
  });
};
