import {Config} from '@baidu/cba-core';
import path from 'path';

class Configuration {
  private cwd: string = process.cwd();

  private setCwd(cwd: string) {
    this.cwd = cwd;
  }

  getCwd() {
    return this.cwd;
  }

  getPath(_path: string) {
    return path.resolve(this.cwd, _path);
  }

  setConfig(config: Config) {
    this.setCwd(config.cwd);
  }

  getTempConfigFilePath() {
    return this.getPath('temp/config.json');
  }
}

const configuration = new Configuration();

export default configuration;
