{"name": "console-dbsc", "version": "1.0.0", "description": "数据库智能驾驶舱 DBSC 前端项目", "main": "src/index.tsx", "scripts": {"dev": "cba-cli dev", "build": "cba-cli build", "i18n:extract": "cba-cli i18n:extract", "i18n:upload": "cba-cli i18n:upload"}, "repository": {"type": "git", "url": "ssh://<EMAIL>:8235/baidu/bce-console/console-dbsc"}, "keywords": ["DBSC"], "author": "<EMAIL>", "license": "ISC", "devDependencies": {"@baidu/cba-cli": "^1.1.0", "@baidu/cba-preset-console-react": "1.0.0", "@baiducloud/i18n": "1.0.0-rc.29", "@types/lodash": "^4.14.202", "@types/react": "^17.0.75", "@types/react-dom": "^17.0.25", "eslint-plugin-prettier": "^5.1.3", "prettier-eslint": "^16.3.0"}, "dependencies": {"@baidu/bce-react-toolkit": "^0.0.23-beta.0", "acud": "^1.4.37", "acud-icon": "^1.0.8", "ahooks": "^3.7.8", "axios": "^1.6.5", "classnames": "^2.5.1", "echarts": "^5.4.3", "lodash": "^4.17.21", "moment": "^2.30.1", "react": "^17.0.2", "react-dom": "^17.0.2", "react-router-dom": "^6.21.1"}, "browserslist": ["IE >= 11"]}