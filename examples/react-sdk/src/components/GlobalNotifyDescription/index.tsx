import './index.less';

import React from 'react';
import {CopyToClipboard} from 'react-copy-to-clipboard';

import CopyIcon from '../../assets/copy.svg';

interface GlobalNotifyDescriptionProps {
  /** 请求 ID */
  requestId: string;
}

export const GlobalNotifyDescription: React.FC<
  GlobalNotifyDescriptionProps
> = ({requestId}) => {
  return (
    <span className="global-notify-description-container">
      <span>错误码: {requestId}</span>{' '}
      <CopyToClipboard text={requestId}>
        <CopyIcon className="copy-icon" />
      </CopyToClipboard>
    </span>
  );
};
