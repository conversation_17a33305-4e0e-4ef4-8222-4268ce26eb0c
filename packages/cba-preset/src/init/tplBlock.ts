/**
 * 模板代码片段
 */

export const reactI18nTpl = {
  i18nImport: `
    import {
      i18nInstance,
      I18nProvider,
      I18nUtil,
      toolkitConfig,
      LanguageType
    } from '@baidu/bce-react-toolkit';
    import {
      useEffect,
      useState
    } from 'react';
    `,
  i18nCodeBlock: `
    const [i18nUtil, setI18nUtil] = useState<any>({isZhCNLanguage: () => {}});
    useEffect(() => {
      const init = async () => {
        toolkitConfig.init({
          enableI18n: true, // 是否启用国际化
          enableIndependentI18n: false, // 是否开启独立的国际化
          supportedLanguageTypes: [LanguageType.zhCN, LanguageType.enUS] // 支持的语言类型, 默认支持中文，可不在列表中维护中文，推荐全部小写
        });

        const i18nUtilInstance = new I18nUtil();
        await i18nUtilInstance.init();
        setI18nUtil(i18nUtilInstance);
      };
      init();
    }, []);`
};
