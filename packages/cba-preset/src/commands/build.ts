/**
 * @file 项目打包命令
 * <AUTHOR> (<EMAIL>)
 */
import {IApi} from '../types';
import {build as webpackBuild} from '../bundler-webpack/build';

export default (api: IApi) => {
  api.describe({
    key: 'preset:build',
    enableBy() {
      return api.name === 'build';
    }
  });

  api.registerCommand({
    name: 'build',
    description: 'build project',
    fn: async () => {
      const chainWebpack = async (config: any, args: Object) => {
        return await api.applyPlugins({
          key: 'chainWebpack',
          type: api.ApplyPluginsType.modify,
          initialValue: config,
          args
        });
      };

      const modifyWebpackConfig = async (webpackConfig: any, args: Object) => {
        return await api.applyPlugins({
          key: 'modifyWebpackConfig',
          initialValue: webpackConfig,
          args
        });
      };

      const onBeforeBundlerStart = async (args: Object) => {
        return await api.applyPlugins({
          key: 'onBeforeBundlerStart',
          args
        });
      };

      const onBundlerFinished = async (args: Object) => {
        return await api.applyPlugins({
          key: 'onBundlerFinished',
          args
        });
      };

      const options = {
        env: api.env,
        cwd: api.cwd,
        userConfig: api.userConfig,
        chainWebpack,
        modifyWebpackConfig,
        onBeforeBundlerStart,
        onBundlerFinished
      };
      await webpackBuild(options);
    }
  });
};
