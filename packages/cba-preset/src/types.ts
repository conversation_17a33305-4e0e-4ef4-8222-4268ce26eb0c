import WebpackDevServer from 'webpack-dev-server';
import type {I<PERSON><PERSON> as IB<PERSON><PERSON>pi, IUserConfig as IBaseUserConfig} from '@baidu/cba-core';
import webapck, {Configuration} from 'webpack';
import Config from 'webpack-chain';

export enum Env {
  development = 'development',
  production = 'production'
}
/**
 * 项目配置文件
 */
export interface IUserConfig extends IBaseUserConfig {
  /**
   * webpack配置
   */
  webpack?: Configuration | ((config: Configuration, merge: Function) => Configuration);

  /** 产品名称 */
  appName?: string;

  /** 产品标题 */
  appTitle?: string;

  /**
   * 功能清单ID
   */
  templateId?: string;

  /**
   * 功能清单产品list，不区分大小写，可配置多个产品
   */
  flags?: string[];

  /**
   * mock 配置
   */
  mockup?: {
    caching: boolean;
    rules?: Array<string>;
    root: string;
    simple?: boolean;
    buildFiles?: Array<string>;
  };

  /**
   * html模板路径
   */
  htmlTemplate?: string;

  /** 国际化相关配置 */
  i18n?: {
    /** 是否开启国际化 */
    enabled?: boolean;
    /** 是否启用独立国际化，语料将不上传至云桥，由项目自行维护 */
    independent?: boolean;
    /** 支持的语言类型 */
    supportedLanguages?: string[];
    /** 语料输出的路径, 可为绝对路径和相对路径，相对路径根据当前工作目录查找 */
    output?: string;
    /** sdp混合工程san page页面语料提取逻辑不同，需额外指定 */
    sdpDir?: string | string[];
  };

  /**
   * 代理地址
   */
  proxyTarget?: string;

  /**
   * PageMaker 相关配置
   * @useFor PageMaker 项目专属
   */
  pagemaker?: {
    /**
     * pagemaker 应用所属组织
     */
    company?: string;
    /**
     * pagemaker 里的应用id，见【应用配置-基本信息-短名字】
     */
    appId?: string;
  };

  /**
   * 路由模式，默认hash
   * @useFor PageMaker 项目专属
   */
  routerMode?: 'hash' | 'history';

  /**
   * 状态管理库，undefined | 'mobx'
   * @useFor PageMaker 项目专属
   */
  stateName?: string;

  /**
   * 判断是否启用ProgessPlugin，默认启用
   */
  enableWebpackProgressPlugin?: boolean;

  /**
   * babel自定义配置
   */
  babelOptions?: {
    /** 额外的babel预设 */
    presets?: [];
    /** 额外的babel插件 */
    plugins?: [];
    /**
     * 自定义babel配置会将项目中的babel配置通过参数传入，经过resolveOptions函数处理后，返回新的babel配置
     * @param options 原始的babel配置
     * @returns 新的babel配置
     */
    resolveOptions?: (options: Object) => Object;
  };
}

/**
 * 插件参数Api
 */
export type IApi = IBaseApi & {
  userConfig: IUserConfig;
  /**
   * 拼接webpack
   * @params
   * config 配置来自webpack-chain
   * 使用文档：https://github.com/Yatoo2018/webpack-chain/tree/zh-cmn-Hans
   */
  chainWebpack: (
    fn: (
      config: Config,
      args: {
        env: Env;
        webpack: any;
      }
    ) => void
  ) => void;
  modifyWebpackConfig: (
    fn: (
      webpackConfig: Configuration,
      args: {
        env: Env;
        webpack: any;
        merge: Function;
      }
    ) => Configuration
  ) => void;

  /**
   * dev启动前的事件回调
   */
  onBeforeServerStart: (fn: (args: {config: Configuration; server: WebpackDevServer; origin: string}) => void) => void;

  /**
   * dev启动后的事件回调
   */
  onServerStarted: (fn: (args: {config: Configuration; server: WebpackDevServer; origin: string}) => void) => void;

  /**
   * 打包开始前的事件回调
   */
  onBeforeBundlerStart: (fn: (args: {config: Configuration; outPath: string}) => void) => void;

  /**
   * 打包完成的事件回调
   */
  onBundlerFinished: (fn: (args: {config: Configuration; outPath: string; state: webapck.Stats}) => void) => void;

  /**
   * 更新项目功能清单
   * @returns
   */
  getFlags: () => void;
};
