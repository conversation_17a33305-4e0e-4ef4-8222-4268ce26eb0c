{"parser": "@typescript-eslint/parser", "env": {"node": true, "browser": true, "commonjs": true, "es2021": true}, "extends": ["plugin:@typescript-eslint/recommended", "plugin:react/recommended", "plugin:react-hooks/recommended", "eslint:recommended", "plugin:prettier/recommended"], "plugins": ["@typescript-eslint", "react", "react-hooks", "prettier", "simple-import-sort"], "globals": {"APP_TITLE": "readonly", "APP_ENABLE_I18N": "readonly", "APP_ENABLE_INDEPENDENT_I18N": "readonly", "APP_ALLOWED_LANGUAGE_TYPES": "readonly", "APP_IS_EMBED_MODE": "readonly", "__webpack_public_path__": "writable"}, "rules": {"object-curly-spacing": ["error", "never"], "comma-dangle": ["error", "never"], "simple-import-sort/imports": "error", "@typescript-eslint/no-explicit-any": "off", "no-unused-vars": "off", "@typescript-eslint/no-unused-vars": ["error"]}, "settings": {"react": {"createClass": "createReactClass", "pragma": "React", "fragment": "Fragment", "version": "detect", "flowVersion": "0.53"}}}