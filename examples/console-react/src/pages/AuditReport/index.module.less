.operationContainer {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 16px;


  .rightContainer {
    display: flex;
    align-items: flex-start;
    gap: 10px;
    .searchContainer {
      width: 240px;
    }
  }
}

.paginationContainer {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;

  :global {
    .acud-pagination {
      width: fit-content;
    }
  }
}

.report-list-status-container {
  .report-status {
      width: 8px;
      height: 8px;
      display: inline-block;
      border-radius: 50%;
      margin-right: 6px;
      background-color: #B8BABF;
  }
  .inEffect-report-status {
      background-color: #36B62C;
  }

  .generateFailed-report-status {
      background-color: #EF363A;
  }
}

