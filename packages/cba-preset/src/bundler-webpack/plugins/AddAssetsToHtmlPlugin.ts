/**
 * @file 在 index.html 中插入 css 和 js
 * <AUTHOR> (<EMAIL>)
 */
import {Compiler} from 'webpack';
import HtmlWebpackPlugin from 'html-webpack-plugin';

export default class AddAssetsToHtml {
  constructor(private readonly options: {css: string[]; js: string[]}) {}

  apply(compiler: Compiler) {
    compiler.hooks.compilation.tap('AddAssetsToHtml', (compilation: any) => {
      HtmlWebpackPlugin.getHooks(compilation).beforeAssetTagGeneration.tapAsync(
        'AddNodeModulesAssetsPlugin',
        (data, callback: () => void) => {
          const {css = [], js = []} = this.options;
          data.assets.css = [...css, ...data.assets.css];
          data.assets.js = [...js, ...data.assets.js];
          callback();
        }
      );
    });
  }
}
