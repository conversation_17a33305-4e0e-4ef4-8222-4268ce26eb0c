/* eslint-disable @typescript-eslint/no-unused-vars */
import {request} from '@baidu/bce-react-toolkit';

/** 查询 DBSC 的服务参数 */
export function getServiceParam(): Promise<{
  success: boolean;
  status: number;
  result: Array<{
    roleName: string;
    policyId: string;
    serviceId: string;
    /** 是否激活 */
    isActivated?: boolean;
  }>;
}> {
  // return request({
  //   url: '/api/dbsc/service/param',
  //   method: 'POST'
  // });
  return Promise.resolve({
    success: true,
    status: 200,
    result: [
      {
        roleName: 'BceServiceRole_dsc',
        policyId: 'da5633a62213468dbc810f6b1656459c',
        serviceId: 'dd4c4dd89f3d4dada4cbe7c88fb008b2'
      }
    ]
  });
}

/** 判断 DBSC 是否已经激活 */
export function queryIamStsRole(params: {roleName: string}): Promise<{
  success: boolean;
  status: number;
  result: {
    id: string;
    name: string;
    type: string;
    grantType: string;
    description: string;
    domain_id: string;
    create_time: string;
  };
}> {
  // return request({
  //   url: '/api/iam/sts/role/query',
  //   method: 'POST',
  //   data: params
  // });
  return Promise.resolve({
    success: true,
    status: 200,
    result: {
      id: '6fd5c3431757411c857d13b3d2aff80b',
      name: 'BceServiceRole_dsc',
      type: 'SERVICE',
      grantType: 'ACCOUNT',
      description: 'System created role: BceServiceRole_dsc',
      domain_id: '3aa23ba0d8734fe5a77a4399401a916b',
      create_time: '2023-12-14T08:48:43.430Z'
    }
  });
}

/** 激活产品角色 */
export function activateIamStsRole(params: {
  roleName: string;
  accountId: string;
  serviceId: string;
  policyId: string;
}): Promise<{
  status: number;
  success: boolean;
}> {
  return request({
    url: '/api/iam/sts/role/activate',
    method: 'POST',
    data: params
  });
}
