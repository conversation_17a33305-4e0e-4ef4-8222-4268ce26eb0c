/**
 * @file 命令类，用于封装命令行中的命令
 * <AUTHOR> (<EMAIL>)
 */

import {Plugin} from './plugin';

export interface ICommandOpts {
  /**
   * 命令名称
   */
  name: string;
  /**
   * 命令描述
   */
  description?: string;
  /**
   * 命令配置项
   */
  options?: string;

  /**
   * 命令详细信息，用于help展示
   */
  details?: string;
  /**
   * 命令执行函数
   */
  fn: ({args}: any) => Promise<any> | void;
  /**
   * 命令所属的插件
   */
  plugin: Plugin;
}

export class Command {
  name: string;
  description?: string;
  options?: string;
  details?: string;
  fn: ({args}: any) => Promise<any> | void;
  plugin: Plugin;

  constructor(opts: ICommandOpts) {
    this.name = opts.name;
    this.description = opts.description;
    this.options = opts.options;
    this.details = opts.details;
    this.fn = opts.fn;
    this.plugin = opts.plugin;
  }
}
