import {writeFileSync} from 'fs-extra';
import assert from 'assert';
import {
  formatFile,
  deleteFolderOrFile
} from '@baidu/cba-utils';
import {join} from 'path';
import {IApi} from './types';

import renderTemplate from './utils/renderTemplate';


export default (api: IApi) => {
  api.describe({
    key: 'cba-creator:base-plugin-api'
  });

  api.registerMethod({
    name: 'renderTemplate',
    fn: async (source: string, target: string, appData: any) => {
      await renderTemplate(source, target, appData);
    }
  });

  api.registerMethod({
    name: 'removeTemplate',
    fn: async (target: string) => {
      await deleteFolderOrFile(target);
    }
  });


};
