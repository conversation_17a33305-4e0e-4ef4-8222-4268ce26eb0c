module.exports = async function getData(appData) {
  const enableI18n = appData.enableI18n;
  const i18nImport = `
  import {
    i18nInstance,
    I18nProvider,
    I18nUtil,
    toolkitConfig,
    LanguageType
  } from '@baidu/bce-react-toolkit';
  import {
    useEffect,
    useState
  } from 'react';
  `;
  const i18nCodeBlock = `
    const [i18nUtil, setI18nUtil] = useState<any>({isZhCNLanguage: () => {}});
    useEffect(() => {
      const init = async () => {
        toolkitConfig.init({
          enableI18n: true, // 是否启用国际化
          enableIndependentI18n: true, // 是否开启独立的国际化
          supportedLanguageTypes: [LanguageType.zhCN, LanguageType.enUS] // 支持的语言类型, 默认支持中文，可不在列表中维护中文，推荐全部小写
        });

        const i18nUtilInstance = new I18nUtil();
        await i18nUtilInstance.init();
        setI18nUtil(i18nUtilInstance);
      };
      init();
    }, []);`;
  let appDomTpl = `
    <BrowserRouter>
    <div className="app">
      <Router></Router>
    </div>
  </BrowserRouter>
  `;
  if (enableI18n) {
    appDomTpl = `
      <I18nProvider
          i18n={i18nInstance}
          defaultNS="translation"
          i18nUtil={i18nUtil}
        >
          <BrowserRouter>
            <div className="app">
              <Router></Router>
            </div>
          </BrowserRouter>
        </I18nProvider>
    `;
  }

  return {
    enableI18n,
    i18nImport,
    i18nCodeBlock,
    appDomTpl
  };
};
