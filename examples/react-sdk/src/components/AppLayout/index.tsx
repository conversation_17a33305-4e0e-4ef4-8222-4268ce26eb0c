import './index.less';

import {Loading, Menu} from 'acud';
import cx from 'classnames';
import React, {Suspense, useCallback, useEffect, useMemo} from 'react';
import {useLocation, useNavigate} from 'react-router-dom';

import {queryIamStsRole} from '../../apis/auth';
import {toolkitConfig, ToolkitConfigOptions} from '../../config';
import {
  AppContextActionType,
  ServiceParam,
  useAppContext
} from '../../contexts';
import {MenuItem, recursiveMenus} from '../../utils';

const {SubMenu, MenuHead} = Menu;

interface AppLayoutProps {
  /** 渲染的子节点 */
  children: React.ReactNode;
  /** 菜单列表 */
  menus: MenuItem[];
  /** 产品开通参数 */
  serviceParams: ServiceParam[];
  /** 开通页路由 */
  activationPagePath: string;
}

export const AppLayout: React.FC<AppLayoutProps> = ({
  children,
  menus,
  serviceParams,
  activationPagePath
}) => {
  const location = useLocation();
  const {appState, appDispatch} = useAppContext();
  const navigate = useNavigate();

  const appTitle = toolkitConfig.getConfig(
    'appTitle'
  ) as ToolkitConfigOptions['appTitle'];

  const flattenedMenuList = useMemo(() => {
    return recursiveMenus(menus);
  }, [menus]);

  const currentHashPath = useMemo(() => {
    return location.pathname;
  }, [location.pathname]);

  const getMenuItemByKey = useCallback(
    (
      menuList: MenuItem[],
      key = currentHashPath
    ): {
      menuItem: MenuItem;
      headerMenuItem?: MenuItem;
    } | null => {
      for (const item of menuList) {
        if (item.key === key) {
          return {menuItem: item};
        }
        if (item.children?.length) {
          const result = getMenuItemByKey(item.children, key);
          if (result) {
            if (result.menuItem.isHeaderNav) {
              return {
                menuItem: item,
                headerMenuItem: result.menuItem
              };
            }
            return result;
          }
        }
      }
      return null;
    },
    [currentHashPath]
  );

  const [currentMenuName, currentHeaderMenu, selectedKeys, renderSubtitle] =
    useMemo(() => {
      const {menuItem, headerMenuItem} = getMenuItemByKey(menus) || {};

      return [
        menuItem?.menuName || '',
        headerMenuItem
          ? (menuItem?.children?.filter(
              (item) => item.isHeaderNav
            ) as MenuItem[])
          : [],
        headerMenuItem
          ? [menuItem?.key as string, headerMenuItem.key]
          : [menuItem?.key || ''],
        menuItem?.renderSubtitle
      ];
    }, [getMenuItemByKey, menus]);

  const onClickMenuItem = useCallback(
    ({key}: {key: string}) => {
      if (!appState.isActivated) {
        return;
      }

      if (selectedKeys[0] === key) {
        return;
      }

      const {menuItem} = getMenuItemByKey(menus, key) || {};

      let hashPath = menuItem?.key;

      if (menuItem?.hasHeaderMenu) {
        hashPath = menuItem.children?.find((item) => item.isHeaderNav)?.key;
      }

      if (hashPath) {
        window.location.hash = hashPath;
      }
    },
    [appState.isActivated, getMenuItemByKey, menus, selectedKeys]
  );

  const onClickHeaderMenuItem = useCallback(
    ({key}: {key: string}) => {
      if (selectedKeys[1] === key) {
        return;
      }
      window.location.hash = key;
    },
    [selectedKeys]
  );

  useEffect(() => {
    const fn = async () => {
      if (serviceParams?.length) {
        const promises = serviceParams.map((item) => {
          return queryIamStsRole({
            roleName: item.roleName
          });
        });

        const results = await Promise.all(promises);
        serviceParams.forEach((item, index) => {
          if (results[index]?.result?.name) {
            item.isActivated = true;
            return;
          }
          item.isActivated = false;
        });

        appDispatch({
          type: AppContextActionType.SET_SERVICE_PARAMS,
          payload: {
            serviceParams
          }
        });

        if (serviceParams.some((item) => !item.isActivated)) {
          navigate(activationPagePath);
        } else {
          appDispatch({
            type: AppContextActionType.ACTIVATE_PRODUCT
          });
        }
        appDispatch({
          type: AppContextActionType.COMPLETE_PRE_REQUEST
        });
      }
    };

    fn();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [serviceParams]);

  const renderMenuItem = useCallback(
    (menuItem: MenuItem) => {
      if (
        menuItem?.children?.length &&
        menuItem.isNavMenu &&
        !menuItem.hasHeaderMenu
      ) {
        return (
          <SubMenu key={menuItem.key} title={menuItem.menuName}>
            {menuItem.children.map(renderMenuItem)}
          </SubMenu>
        );
      }

      return menuItem.isNavMenu ? (
        <Menu.Item key={menuItem.key} onClick={onClickMenuItem}>
          {menuItem.menuName}
        </Menu.Item>
      ) : null;
    },
    [onClickMenuItem]
  );

  const defaultOpenKeys = useMemo(() => {
    const keys: string[] = [];
    const getDefaultOpenKeys = (menuItem: MenuItem) => {
      if (menuItem.isDefaultOpened) {
        keys.push(menuItem.key);
      }
      menuItem?.children?.length &&
        menuItem.children.forEach((item) => {
          if (item.key === selectedKeys[0]) {
            keys.push(menuItem.key);
          }
          getDefaultOpenKeys(item);
        });
    };

    menus.forEach(getDefaultOpenKeys);

    return keys;
  }, [menus, selectedKeys]);

  const renderMenu = useCallback(() => {
    return (
      <div className="app-menu-container">
        <Menu
          className={cx('app-menu', {
            ['hidden-app-menu']: appState.isMenuCollapsed
          })}
          mode="inline"
          selectedKeys={selectedKeys}
          defaultOpenKeys={defaultOpenKeys}
        >
          <MenuHead>{appTitle}</MenuHead>
          {menus.map(renderMenuItem)}
        </Menu>

        <div
          className={cx('menu-aside-hide-bar', {
            ['menu-aside-hide-bar-is-hidden']: appState.isMenuCollapsed
          })}
          onClick={() => {
            appDispatch({
              type: AppContextActionType.TOGGLE_MENU
            });
          }}
        ></div>
      </div>
    );
  }, [
    appDispatch,
    appState.isMenuCollapsed,
    appTitle,
    defaultOpenKeys,
    menus,
    renderMenuItem,
    selectedKeys
  ]);

  const Header = useCallback(() => {
    const HeaderContent = (
      <div className="header-content-container">
        <div className="menu-name">
          {currentMenuName}
          {renderSubtitle && renderSubtitle({})}
        </div>
      </div>
    );

    return HeaderContent;
  }, [renderSubtitle, currentMenuName]);

  const HeaderMenu = useCallback(() => {
    const HeaderMenuContent = (
      <div className="header-menu-container">
        <Menu
          className="header-menu"
          mode="horizontal"
          selectedKeys={selectedKeys.slice(-1)}
        >
          {currentHeaderMenu.map((menuItem) => (
            <Menu.Item key={menuItem.key} onClick={onClickHeaderMenuItem}>
              {menuItem.menuName}
            </Menu.Item>
          ))}
        </Menu>
      </div>
    );

    return HeaderMenuContent;
  }, [currentHeaderMenu, onClickHeaderMenuItem, selectedKeys]);

  const renderHeader = useCallback(() => {
    return (
      <div className={cx(['page-header-container', 'page-header'])}>
        <Header />
        {!!currentHeaderMenu.length && <HeaderMenu />}
      </div>
    );
  }, [Header, HeaderMenu, currentHeaderMenu.length]);

  const routes = useMemo(() => {
    const menuList = recursiveMenus(menus);
    return {
      navRoutes: menuList.filter((item) => item.Component && item.isNavMenu),
      nonNavRoutes: menuList.filter((item) => item.Component && !item.isNavMenu)
    };
  }, [menus]);

  const isNavPage = useMemo(() => {
    return (
      routes.navRoutes.findIndex((item) => item.key === currentHashPath) > -1
    );
  }, [currentHashPath, routes.navRoutes]);

  const currentMenu: MenuItem | undefined = useMemo(() => {
    const index = flattenedMenuList.findIndex(
      (item) => item.key === currentHashPath
    );
    if (index > -1) {
      return flattenedMenuList[index];
    }
  }, [currentHashPath, flattenedMenuList]);

  const renderSuspense = () => {
    return <div className="suspense-container"></div>;
  };

  const renderNavPage = useCallback(() => {
    return (
      <>
        {renderMenu()}
        {currentMenu?.isPageLayoutCustomized ? (
          <Suspense fallback={renderSuspense()}>{children}</Suspense>
        ) : (
          <div className={cx(['page-wrapper'])}>
            {renderHeader()}
            <Suspense fallback={renderSuspense()}>
              <div
                className={cx({
                  ['nav-page-content-container']: true,
                  ['nav-page-content-without-padding-container']:
                    !appState.isActivated ||
                    currentMenu?.isPageWrapperNotRequired,
                  ['page-content']: true
                })}
              >
                {children}
              </div>
            </Suspense>
          </div>
        )}
      </>
    );
  }, [
    appState.isActivated,
    children,
    currentMenu?.isPageLayoutCustomized,
    currentMenu?.isPageWrapperNotRequired,
    renderHeader,
    renderMenu
  ]);

  const renderNonNavPage = useCallback(() => {
    return (
      <Suspense fallback={<Loading size="small"></Loading>}>
        <div className="non-nav-page-content-container">{children}</div>
      </Suspense>
    );
  }, [children]);

  return (
    <div className="app-layout-container">
      {!appState.isPreRequestCompleted ? (
        <Loading loading={true} size="small"></Loading>
      ) : isNavPage ? (
        renderNavPage()
      ) : (
        renderNonNavPage()
      )}
    </div>
  );
};
