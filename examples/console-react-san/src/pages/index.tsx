import {MenuItem, recursiveMenus} from '@baidu/bce-react-toolkit';
import React from 'react';
import urls from '@/utils/urls';

import './sanPages/utils/decorators';

const Activation = React.lazy(
  () => import(/* webpackChunkName: "Activation" */ '@/pages/Activation')
);

const AuditReport = React.lazy(
  () => import(/* webpackChunkName: "AuditReport" */ '@/pages/AuditReport')
);

const ReactMixSan = React.lazy(
  () => import(/* webpackChunkName: "AuditReport" */ '@/pages/ReactMixSan')
);

const SanList = React.lazy(
  () => import(/* webpackChunkName: "SanList" */ '@/pages/sanPages/pages/list')
);

/** 菜单定义 */
const menus: MenuItem[] = [
  {
    menuName: '开通页',
    key: urls.activation,
    isNavMenu: true,
    Component: Activation,
    isPageWrapperNotRequired: true
  },
  {
    menuName: '二级菜单',
    key: '/secure/audit',
    isNavMenu: true,
    isDefaultOpened: false,
    children: [
      {
        menuName: 'react列表',
        key: urls.auditReport,
        isNavMenu: true,
        Component: AuditReport
      },
      {
        menuName: 'react mix san cmpt',
        key: '/reactMixSan',
        isNavMenu: true,
        Component: ReactMixSan
      },
      {
        menuName: 'san列表',
        key: '/sanList',
        isPageLayoutCustomized: true,
        isNavMenu: true,
        Component: SanList
      }
    ]
  }
];

/** 打平之后的菜单列表 */
export const flattenedMenuList = recursiveMenus(menus);

export default menus;
