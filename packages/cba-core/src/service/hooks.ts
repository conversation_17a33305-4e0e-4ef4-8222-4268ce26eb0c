/**
 * @file 钩子函数
 * <AUTHOR> (<EMAIL>)
 */

import assert from 'assert';
import {Plugin} from './plugin';

export type HookFn = (...args: any[]) => Promise<any>;

export interface IHookOpts {
  plugin: Plugin;
  key: string;
  fn: HookFn;
  /**
   * 在哪个hook之前执行，值为某个hook的key
   */
  before?: string;
  /**
   * 这个属性的类型是数字，数字越大事件回调执行的越晚，支持传入负数，不传时默认为0
   */
  stage?: number;
}

export class Hook {
  plugin: Plugin;
  key: string;
  fn: HookFn;
  before?: string;
  stage?: number;

  constructor(opts: IHookOpts) {
    assert(opts.key && opts.fn, `Invalid hook ${opts}, key and fn must supplied.`);
    this.plugin = opts.plugin;
    this.key = opts.key;
    this.fn = opts.fn;
    this.before = opts.before;
    this.stage = opts.stage || 0;
  }
}
