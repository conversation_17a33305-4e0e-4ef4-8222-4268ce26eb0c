<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <script src="/ecom/esl/2.2.0-rc.3/esl.js"></script>
    <script src="/fe-framework/loadjs.js"></script>
    <script src="/combo/san-router@1.2.2/dist/san-router.js"></script>
    <title>百度智能云</title>
  </head>
  <body>
    <div id="root-app-container"></div>
    <script type="text/javascript">
      const depProcess = (depConfig) => {
        const res = {};
        for (const key in depConfig) {
          res[key] = depConfig[key]
            .replace('https://code.bdstatic.com', '.')
            .replace('https://bce.bdstatic.com', '.')
            .replace(/\.js$/, '');
        }
        return res;
      };
      const isPrivateStandard330 = '<%= htmlWebpackPlugin.options.isPrivateStandard330 %>' === 'true';
      const flags = '<%= htmlWebpackPlugin.options.flags %>'.split(',');
      const i18nAddress = '/console/static/i18n/console.%s';
      const kModuleName = '<%= htmlWebpackPlugin.options.moduleName %>';
      const kAppName = '<%= htmlWebpackPlugin.options.appName %>';
      const KCustomDeps = '<%= htmlWebpackPlugin.options.sdpDeps %>';
      const kModuleConf = {
        baseUrl: '.',
        paths: {
          'lodash': '/combo/lodash@3.10.1/lodash.min',
          'moment': '/combo/moment@2.24.0/min/moment.min',
          'jquery': '/combo/jquery@3.4.1/dist/jquery.min',
          'big.js': '/combo/big.js@5.2.2/big.min',
          'echarts': '/console/dist/affaebd/dep/echarts/3.7.0/echarts+zrender.min',
          'san': '/combo/san@3.7.7/dist/san.spa.min',
          'san-router': '/combo/san-router@1.2.2/dist/san-router',
          '@baiducloud/runtime': '/lib/@baiducloud/runtime/dist/runtime',
          'ace-builds': '/ace-builds/src-min-noconflict/',
          'webuploader': '/console/dep/05cfee93/webuploader/WebUploader',
          'webuploader/webuploader': '/console/dep/05cfee93/webuploader/webuploader',
          ...depProcess(JSON.parse(KCustomDeps || '{}'))
        },
        bundles: {
          echarts: [
            'echarts',
            'zrender/vml/vml',
            'echarts/chart/pie',
            'echarts/chart/bar',
            'echarts/chart/line',
            'echarts/chart/lines',
            'echarts/chart/map',
            'echarts/chart/scatter',
            'echarts/chart/gauge',
            'echarts/component/legendScroll',
            'echarts/component/legend',
            'echarts/component/dataZoom',
            'echarts/component/tooltip',
            'echarts/component/title',
            'echarts/component/grid',
            'echarts/component/toolbox',
            'echarts/component/markPoint',
            'echarts/component/markLine'
          ]
        },
        map: {
          '*': {
            '@baiducloud/bce-ui/san': '@baiducloud/bce-ui'
          }
        }
      };
      require.config(kModuleConf);
      function waitLoadJS(list, callback) {
        var countOfAction = list && list.length !== 0;
        if (Array.isArray(list)) {
          list.forEach(function (scriptNode) {
            if (scriptNode._loadStatus === '1') {
              if (--countOfAction === 0) {
                callback();
              }
            } else {
              scriptNode.onload = function () {
                if (--countOfAction === 0) {
                  callback();
                }
              };
            }
          });
        }
      }
      let scriptList = [];
      function sanPreBootstrap(i18n, framework) {
        // 国际化初始化
        i18n.init({
          sourceType: 'amd',
          url: i18nAddress
        });
        const locale = framework.i18n.getCurrentLanguage();
        return i18n.activate(locale);
      }
      function reactPreBootstrap() {
        if (!Array.isArray) {
          Array.isArray = function (arg) {
            return Object.prototype.toString.call(arg) === '[object Array]';
          };
        }

        function loadCss(url) {
          var cssLink = document.createElement('link');
          cssLink.setAttribute('rel', 'stylesheet');
          cssLink.setAttribute('href', url);
          document.head.appendChild(cssLink);
        }

        function loadJS(url) {
          var scriptNode = document.createElement('script');
          scriptNode.setAttribute('type', 'text/javascript');
          scriptNode.setAttribute('src', url);
          scriptNode.onload = function () {
            this._loadStatus = '1';
          };
          document.head.appendChild(scriptNode);
          return scriptNode;
        }

        const jsList = ['/static/js/main.js'];
        const cssList = ['/main.css'];
        let scriptNodeList = [];

        function concatUrl(filePath) {
          var baseUrl = location.origin;
          window.appPublicPath = `${baseUrl}/${kAppName}/`;
          return `${baseUrl}/${kAppName}${filePath}`;
        }

        function loadRes() {
          cssList.forEach(function (filePath) {
            loadCss(concatUrl(filePath));
          });
          scriptNodeList = jsList.map(function (filePath) {
            return loadJS(concatUrl(filePath));
          });

          return scriptNodeList;
        }
        scriptList = loadRes();
      }
      function requestFlag(flag) {
        function checkStatus(response) {
          if (response.status >= 200 && response.status < 300) {
            return response
          } else {
            var error = new Error(response.statusText)
            error.response = response
            throw error
          }
        }

        function parseJSON(response) {
          return response.json()
        }
        return fetch(`/v1/settings/acl/features?productName=${flag}`).then(checkStatus)
          .then(parseJSON);
      }
      function reactMixSanBootstrap() {
       try {
        const flagsPromise = flags.map(flagName => requestFlag(flagName));
        if (isPrivateStandard330) {
          Promise.all(flagsPromise).then(res => {
            const flagsObj = {};
            res.forEach(flagList => flagList.forEach(flag => flagsObj[flag] = true));
            console.log('功能清单获取成功🏅', flagsObj);
            sessionStorage.setItem('_flags', JSON.stringify(flagsObj));
          }).catch(error => {
            console.error(error);
          });
        } else {
          require.config({flag: './flag'});
          require(['flag'], function (flag) {
            let flagsObj;

            try {
              flagsObj = Array.isArray(flag)
                ? flag.reduce((cur, next) => {
                    cur[next] = true;
                    return cur;
                  }, {})
                : {};
            } catch (error) {}

            sessionStorage.setItem('_flags', JSON.stringify(flagsObj));
          });
        }
       } catch (error) {
        console.error(error);
       } finally {
          window.require(['framework', '@baiducloud/i18n'], function (framework, i18n) {
            window.$framework = framework;
            const frameDefer = framework.boot({isActiveComponent: false});
            reactPreBootstrap();
            const i18nDefer = sanPreBootstrap(i18n, framework);
            const defer = i18nDefer.then(() => frameDefer);
            window.require(['@baiducloud/runtime'], runtime => {
              waitLoadJS(scriptList, function () {
                window.require([kModuleName], app => {
                  const $context = runtime.ServiceFactory.resolve('$context');
                  if ($context) {
                    defer.then($context.init);
                  }
                  return runtime.initialize
                    ? defer.then(runtime.initialize).then(app.bootstrap).then(framework.componentActive)
                    : defer.then(app.bootstrap).then(framework.componentActive);
                });
              });
            });
          });
       }
      }
      if (/(MSIE|Trident)/gi.test(navigator.userAgent)) {
        var script = document.createElement('script');
        var appName = '<%= htmlWebpackPlugin.options.appName %>';
        script.type = 'text/javascript';
        script.async = true;
        script.src = `/npm/core-js-bundle@3.6.2/minified.js`;
        script.onload = reactMixSanBootstrap;
        document.body.appendChild(script);
      } else {
        window.onload = reactMixSanBootstrap;
      }
    </script>
  </body>
</html>
