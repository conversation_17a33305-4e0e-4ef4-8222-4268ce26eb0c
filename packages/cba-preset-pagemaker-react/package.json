{"name": "@baidu/cba-preset-pagemaker-react", "version": "1.2.8-beta.14", "license": "MIT", "main": "lib/index.js", "files": ["lib", "template"], "scripts": {"build": "cba-scripts build", "dev": "cba-scripts dev"}, "dependencies": {"@babel/runtime-corejs3": "^7.23.9", "@baidu/cba-preset": "workspace:*", "@baidu/cba-utils": "workspace:*", "fs-extra": "^11.2.0", "html-webpack-plugin": "^5.5.3", "null-loader": "^4.0.1", "react-hot-loader": "^4.13.1", "shelljs": "^0.8.4", "webpack-virtual-modules": "^0.5.0", "write-file-webpack-plugin": "^4.5.1"}, "devDependencies": {"@types/fs-extra": "^11.0.4", "@types/shelljs": "^0.8.15", "@types/write-file-webpack-plugin": "^4.5.4"}}