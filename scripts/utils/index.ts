import {SpawnSyncOptions} from 'child_process';
import {existsSync, readdirSync} from 'fs';
import {join} from 'path';
import {sync} from 'cross-spawn';
import {PATHS} from './constants';
import logger from './logger';

/**
 * 执行命令
 * @param cmd
 * @param opts
 * @returns
 */
export function spawnSync(cmd: string, opts?: SpawnSyncOptions) {
  const result = sync(cmd, {
    shell: true,
    stdio: 'inherit',
    ...opts
  });
  return result;
}

/**
 * 获取所有的包
 * @param packagesPath
 * @returns
 */
export function getPkgs(packagesPath: string = PATHS.PACKAGES, pkgType?: string): string[] {
  const config = require(PATHS.CONFIG);
  if (pkgType) {
    return (config[pkgType]?.packages || []).filter(Boolean);
  }
  return readdirSync(packagesPath).filter(dir => {
    return existsSync(join(packagesPath, dir, 'package.json'));
  });
}

/**
 * 日期格式化
 * @param date
 * @param format
 * @returns
 */
export function formatDate(date?: Date, format: string = 'YYYY-MM-dd') {
  date = date || new Date();

  const o = {
    'M+': date.getMonth() + 1,
    'd+': date.getDate(),
    'h+': date.getHours(),
    'm+': date.getMinutes(),
    's+': date.getSeconds(),
    'q+': Math.floor((date.getMonth() + 3) / 3),
    'S': date.getMilliseconds()
  };

  if (/(Y+)/.test(format)) {
    format = format.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length));
  }

  for (const k in o) {
    if (new RegExp('(' + k + ')').test(format)) {
      format = format.replace(RegExp.$1, RegExp.$1.length === 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length));
    }
  }
  return format;
}

/**
 * 获取某类型包当前package中的版本号
 * @param pkgType 包类型
 * @returns
 */
export function getCurrentVersion(pkgType: string) {
  if (!pkgType) {
    logger.warn('请配置 --pkg-type');
    process.exit(1);
  }
  let pkgs = getPkgs(PATHS.PACKAGES, pkgType);

  if (!pkgs.length) {
    logger.warn(`${pkgType}缺失要发布的包`);
    process.exit(1);
  }
  const pkgTypeConfig = require(PATHS.CONFIG)[pkgType];
  if (!pkgTypeConfig) {
    logger.error(`${pkgType || 'pkgType'} 在 config.json 配置中缺失`);
    process.exit(1);
  }

  return require(join(PATHS.PACKAGES, pkgs[0], 'package.json'))?.version;
}

