/**
 * @file webpack Dev配置
 * <AUTHOR> (<EMAIL>)
 */

import path from 'path';
import shell from 'shelljs';
import HtmlWebpackPlugin from 'html-webpack-plugin';
import {IApi, Env} from '@baidu/cba-preset';
import {logger} from '@baidu/cba-utils';

function autoOpenPage(api: any, defaultUrl: string) {
  let url = defaultUrl;
  const userConfig = api.userConfig || {};
  if (!api.args.forceLocal) {
    const {company = 'console', appId} = userConfig.pagemaker || {};
    if (appId) {
      url = `https://pagemaker.baidu-int.com/company/${company}/app/${appId}-dev/design/page/`;
    }
  }

  shell.exec(`open ${url}`);
}

export default (api: IApi) => {
  api.describe({
    key: 'preset-pagemaker-react:webpack-dev',
    enableBy: () => api.env === Env.development
  });

  api.modifyWebpackConfig((config: any, {merge}) => {
    const {host, port} = config.devServer || {
      host: 'localhost',
      port: '8899'
    };
    config = merge(config, {
      entry: './hidden-bootstrap.tsx',
      output: {
        path: path.resolve(api.cwd, 'dist'),
        filename: 'pagemaker.development.js'
      },
      devtool: 'eval-source-map',

      devServer: {
        https: true,
        // 解决pageMaker中websocket连接失败导致的热更新失效问题
        // 1. 指定 client.webSocketURL: 'ws://host:port/ws' 否则pageMaker中会自动请求 ws://pagemaker-int.baidu.com:port/ws
        // 2. 设置 allowedHosts，允许 跨域连接 websocket
        // 3. 设置 headers.Access-Control-Allow-Origin , 允许热更新后跨域访问 xxx.update.json 文件
        //    顺便解决 pageMaker中请求静态资源失败的问题
        client: {
          overlay: false,
          progress: true,
          reconnect: 5,
          webSocketURL: `auto://${host}:${port}/ws`
        },
        allowedHosts: 'all',
        headers: {
          'Access-Control-Allow-Origin': 'https://pagemaker.baidu-int.com',
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, PATCH, OPTIONS',
          'Access-Control-Allow-Headers': 'X-Requested-With, content-type, Authorization, x-request-by',
          'Access-Control-Allow-Credentials': 'true'
        }
      },

      plugins: [
        new HtmlWebpackPlugin({
          scriptLoading: 'blocking',
          publicPath: '/',
          templateContent: `
              <!DOCTYPE html>
              <html>
              <head>
                  <meta charset="UTF-8" >
                  <title>百度智能云</title>
                  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" >
                  <link rel="shortcut icon" href="https://bce.bdstatic.com/img/favicon.ico" type="image/x-icon" />
                  <link rel="dns-prefetch" href="//code.bdstatic.com" />
                  <link rel="dns-prefetch" href="//bce.bdstatic.com" />
              </head>
              <body>
                  <div id="main"></div>
              </body>
              </html>
            `
        })
      ]
    });

    return config;
  });

  api.onBeforeServerStart(({config, origin}: {config: any; origin: string}) => {
    logger.info(`开发调试说明： ${origin}`);
    logger.info('pagemaker 平台开发调试【应用设置 -> 临时JS/CSS -> 外链JS】地址：');
    logger.info(`${origin}/${config.output?.filename}`);
    logger.info(`开发调试： ${origin}`);
  });

  api.onServerStarted(({config, origin}: {config: any; origin: string}) => {
    if (config.devServer && config.devServer.open) {
      autoOpenPage(api, origin);
    }
  });
};
