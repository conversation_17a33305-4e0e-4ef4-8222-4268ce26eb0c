import path from 'path';
import fs from 'fs';
import {pathToRegexp} from 'path-to-regexp';
import {gunzipSync} from 'zlib';

import logger from './logger';

// 默认mock配置
const DEFAULT_MOCK_CONFIG = {
  // mock文件地址
  root: '.mockup',
  // 是否自动缓存
  caching: true,
  // 说明哪些接口需求本地mock
  rules: ['/api/*']
};

export default function mockHelper(bceConfig: Record<string, any>, proxy: Record<string, any>) {
  const handle = function (config: Record<string, any> = {}) {
    const mockrc = Object.assign(DEFAULT_MOCK_CONFIG, bceConfig?.mockup || {});
    const mockRoot = path.resolve(process.cwd(), mockrc.root);

    // 需要将数据缓存到本地时，在请求后，将数据写入本地
    if (mockrc.caching) {
      config.onProxyRes = (proxyRes: any, req: any, res: any) => {
        const {pathname} = req._parsedUrl;
        const cacheFilePath = path.resolve(mockRoot, `./${pathname}`, `${req.method}.js`.toLowerCase());
        let buffers: any = [];
        proxyRes.on('data', (data: any) => {
          if (proxyRes.headers['transfer-encoding'] === 'chunked') {
            buffers.push(data);
          } else {
            buffers = data;
          }
        });

        proxyRes.on('end', () => {
          try {
            if (!buffers.length) {
              return;
            }
            if (proxyRes.headers['content-encoding'] === 'gzip') {
              if (proxyRes.headers['transfer-encoding'] === 'chunked') {
                buffers = Buffer.concat(buffers);
              }
              buffers = gunzipSync(buffers).toString('utf-8');
            }
            const response = JSON.stringify(JSON.parse(buffers), null, 4);
            const resJson = JSON.parse(response);
            const redirect = !resJson.success && resJson.message && resJson.message.redirect;
            if (!redirect && !fs.existsSync(cacheFilePath)) {
              fs.mkdirSync(path.resolve(cacheFilePath, '..'), {recursive: true});
              const content = `module.exports = () => {\r\n    return ${response}\r\n}`;
              fs.writeFileSync(cacheFilePath, content, {flag: 'a+'});
            }
          } catch (error) {
            logger.error(`caching ${pathname} error: create mock file ${cacheFilePath} error`);
          }
        });
      };
    }

    // 对请求进行拦截，读取本地路径文件数据，作为mock返回
    const onProxyReq = config?.onProxyReq;
    config.onProxyReq = function (proxyReq: any, req: any, res: any) {
      onProxyReq?.(proxyReq, req, res);
      if (req.headers?.accept?.indexOf('html') === -1) {
        const {pathname} = req._parsedUrl;
        const cacheFilePath = path.resolve(mockRoot, `./${pathname}`, `${req.method}.js`.toLowerCase());
        const isNeedMock = (mockrc.rules ?? []).some((rule: string) => pathToRegexp(rule).exec(pathname));
        // 需要mock则读取对应路径文件
        if (isNeedMock) {
          if (fs.existsSync(cacheFilePath)) {
            res.set({'X-Bce-Mock-By': 'cba-cli'});
            delete require.cache[cacheFilePath];
            const moduleFunction = require(cacheFilePath);
            if (typeof moduleFunction === 'function') {
              const response = moduleFunction(req);
              try {
                JSON.stringify(response) ? res.json(response) : res.end(response);
              } catch (err) {}
            } else {
              logger.error(`mock ${pathname} error: ${cacheFilePath} module.export require return function!`);
            }
          }
        }
      }
    };
  };

  Object.keys(proxy).forEach(key => {
    handle(proxy[key]);
  });

  return proxy;
}
