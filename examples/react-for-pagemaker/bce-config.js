/**
 * @file 项目配置
 */

module.exports = {
  // 模块相关配置
  appName: 'test', // 表明访问的path，默认代码库后几位字母
  pagemaker: {
    // pagemaker 应用所属组织
    company: 'testorg',
    // pagemaker 里的应用id，见【应用配置-基本信息-短名字】
    appId: '443bc7c38dae'
  },
  // 自定义webpack，可选，支持配置 config 或 (config) => config
  webpack: {
    // devServer: {
    //   port: 8899,
    //   open: true,
    //   allowedHosts: 'all',
    //   headers: {
    //     'Access-Control-Allow-Origin': 'https://pagemaker.baidu-int.com',
    //     'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, PATCH, OPTIONS',
    //     'Access-Control-Allow-Headers': 'X-Requested-With, content-type, Authorization, x-request-by',
    //     'Access-Control-Allow-Credentials': 'true'
    //   }
    // },
    // devtool: 'eval-source-map'
  },
  stateName: 'mobx',
  presets: ['@baidu/cba-preset-pagemaker-react']
};
