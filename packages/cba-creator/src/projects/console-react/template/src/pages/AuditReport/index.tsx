import {
  Button,
  DatePicker,
  Form,
  Input,
  Link,
  Modal,
  Pagination,
  Search,
  Select,
  Table,
  toast
} from 'acud';
import locale from 'acud/es/date-picker/locale/zh_CN';
import {OutlinedPlusNew} from 'acud-icon';
import React, {useCallback, useEffect, useMemo, useRef, useState} from 'react';

import RefreshButton from '@/components/RefreshButton';
const {RangePicker} = DatePicker;

import {useTranslation} from '@baidu/bce-react-toolkit';
import {useRequest} from 'ahooks';
import cx from 'classnames';
import {isUndefined} from 'lodash';
import moment from 'moment';

import {
  createAuditReport,
  CreateAuditReportParams,
  downloadReport,
  queryReportList,
  QueryReportListParams,
  queryReportType,
  Report
} from '@/apis/report';
import {OrderType} from '@/utils/enums';
import {genRandomReportName, getFileNameFromPath} from '@/utils/utils';

import styles from './index.module.less';

const reportStatusMap = {
  1: 'awaitSchedule',
  2: 'generating',
  3: 'inEffect',
  4: 'generateFailed',
  5: 'expired'
};

const AuditReport: React.FC = () => {
  const [dataSource, setDataSource] = useState<Array<Report>>([]);

  const [isGenerateReportVisible, setIsGenerateReportVisible] = useState(false);

  const [keyword, setKeyword] = useState('');

  const [form] = Form.useForm();

  const [reportTypeFilterValue, setReportTypeFilterValue] = useState(null);

  const [invalidTimeSortValue, setInvalidTimeSortValue] = useState(null);

  const [reportTypeList, setReportTypeList] = useState<
    Array<{
      text: string;
      value: string;
    }>
  >([]);

  const formatTime = (time: string) => {
    if (!time || time === '0001-01-01T00:00:00Z') {
      return '-';
    }
    return (
      <span>{moment.utc(time).utcOffset(8).format('YYYY-MM-DD HH:mm:ss')}</span>
    );
  };

  /** 控制下载报表按钮重复点击 */
  const downloadReportBtnLocked = useRef(false);

  /** 监听点击下载报表按钮 */
  const onClickDownloadReportBtn = useCallback((report: Report) => {
    if (downloadReportBtnLocked.current) {
      return;
    }

    downloadReportBtnLocked.current = true;

    downloadReport({
      reports: [report.reportId]
    })
      .then((res) => {
        if (res?.result?.links?.length) {
          res.result.links.forEach((item) => {
            const downloadLink = document.createElement('a');
            downloadLink.href = item;
            downloadLink.download = getFileNameFromPath(item);
            downloadLink.click();
          });
        }
      })
      .finally(() => {
        // 延迟 2 秒解锁
        setTimeout(() => {
          downloadReportBtnLocked.current = false;
        }, 2000);
      });
  }, []);

  const columns = useMemo(() => {
    return [
      {
        title: '报表名称',
        dataIndex: 'reportName',
        key: 'reportName',
        width: 180
      },
      {
        title: '生成时间',
        dataIndex: 'createTime',
        key: 'createTime',
        width: 240,
        render: formatTime
      },
      {
        title: '报表类型',
        dataIndex: 'reportTypeDesc',
        key: 'reportTypeDesc',
        filters: reportTypeList,
        filterMultiple: false,
        width: 180
      },
      {
        title: '报表状态',
        dataIndex: 'status',
        key: 'status',
        width: 240,
        render: (status: 1 | 2 | 3 | 4 | 5, record: Report) => {
          return (
            <span className={styles['report-list-status-container']}>
              <span
                className={cx(
                  styles['report-status'],
                  styles[
                    `${reportStatusMap[status] || 'invalid'}-report-status`
                  ]
                )}
              ></span>
              <span>{record.statusDesc}</span>
            </span>
          );
        }
      },
      {
        title: '失效时间',
        dataIndex: 'invalidTime',
        key: 'invalidTime',
        width: 240,
        sorter: true,
        render: formatTime
      },
      {
        title: '操作',
        width: 240,
        render: (record: Report) => {
          return (
            <Link
              disabled={record.status !== 3}
              onClick={() => onClickDownloadReportBtn(record)}
            >
              下载
            </Link>
          );
        }
      }
    ];
  }, [onClickDownloadReportBtn, reportTypeList]);

  /** 监听点击生成报表按钮 */
  const onClickGenerateReportBtn = useCallback(() => {
    setIsGenerateReportVisible(true);
  }, []);

  const [generateReportLocked, setGenerateReportLocked] = useState(false);

  const [pageNo, setPageNo] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [total, setTotal] = useState(0);

  const {run: runQueryReportList, loading: queryReportListLoading} = useRequest(
    queryReportList,
    {
      manual: true,
      onSuccess: (res) => {
        setDataSource(res?.result?.reports || []);
        setTotal(res?.result?.totalCount || 0);
      }
    }
  );

  const getReportList = useCallback(
    (params: QueryReportListParams = {}) => {
      runQueryReportList({
        page: pageNo,
        pageSize,
        search: keyword,
        reportType: reportTypeFilterValue,
        ...(invalidTimeSortValue
          ? {
              order: invalidTimeSortValue,
              orderBy: 'invalidTime'
            }
          : {}),
        ...params
      });
    },
    [
      runQueryReportList,
      pageNo,
      pageSize,
      keyword,
      reportTypeFilterValue,
      invalidTimeSortValue
    ]
  );

  /** 监听点击确认生成报表 */
  const handleConfirmGenerateReport = useCallback(() => {
    form
      .validateFields()
      .then(() => {
        const data = form.getFieldsValue();
        const params: CreateAuditReportParams = {};
        params.reportName = data.reportName;
        params.reportType = data.reportType;

        const convertTimestampToISO8601 = (timestamp: number) => {
          const date = new Date(timestamp);
          return date.toISOString();
        };

        params.start = convertTimestampToISO8601(
          data.dateTimeRange[0].startOf('days').utc().utcOffset(8).valueOf()
        );

        params.end = convertTimestampToISO8601(
          data.dateTimeRange[1].endOf('days').utc().utcOffset(8).valueOf()
        );

        if (generateReportLocked) {
          return;
        }

        setGenerateReportLocked(true);

        createAuditReport(params)
          .then((res) => {
            // 创建成功
            if (!isUndefined(res?.result?.id)) {
              toast.success({
                message: '创建成功',
                duration: 3
              });
              setIsGenerateReportVisible(false);
              getReportList();
            }
          })
          .finally(() => {
            setGenerateReportLocked(false);
          });

        // TODO: 创建报表，加锁
      })
      .catch((err) => {
        console.log('==> err', err);
      });
  }, [form, generateReportLocked, getReportList]);

  /** 监听点击取消生成报表 */
  const handleCancelGenerateReport = useCallback(() => {
    setIsGenerateReportVisible(false);
    form.resetFields();
  }, [form]);

  const {run: runQueryReportType} = useRequest(queryReportType, {
    manual: true,
    onSuccess: (res) => {
      setReportTypeList(
        (res?.result?.types || []).map((item) => {
          return {
            text: item.desc,
            value: item.name
          };
        })
      );
    }
  });

  useEffect(() => {
    runQueryReportType();
    getReportList();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const showTotal = useCallback(() => {
    return total > 0 ? `共 ${total} 条` : '';
  }, [total]);

  /** 监听点击刷新按钮 */
  const onClickRefreshBtn = useCallback(() => {
    getReportList();
  }, [getReportList]);

  const onConfirmSearch = useCallback(
    (value: string) => {
      setKeyword(value);
      setPageNo(1);
      getReportList({
        search: value,
        page: 1
      });
    },
    [getReportList]
  );

  /** 监听表格发生变化 */
  const onTableChange = useCallback(
    (...args: any) => {
      // 筛选
      const reportTypeFilterValue = args?.[1]?.reportTypeDesc?.[0] || null;
      setReportTypeFilterValue(reportTypeFilterValue);

      let invalidTimeSortValue = args?.[2]?.order;
      invalidTimeSortValue =
        invalidTimeSortValue === 'ascend'
          ? OrderType.asc
          : invalidTimeSortValue === 'descend'
          ? OrderType.desc
          : null;
      setInvalidTimeSortValue(invalidTimeSortValue);

      setPageNo(1);
      getReportList({
        reportType: reportTypeFilterValue,
        page: 1,
        order: invalidTimeSortValue,
        orderBy: 'invalidTime'
      });
    },
    [getReportList]
  );

  const {t} = useTranslation();

  return (
    <div>
      <div className={styles.operationContainer}>
        <div className={styles.leftBtnContainer}>
          <Button
            type="primary"
            icon={<OutlinedPlusNew />}
            onClick={onClickGenerateReportBtn}
          >
            {t('生成报表')}
          </Button>
        </div>
        <div className={styles.rightContainer}>
          <Search
            placeholder="请输入报表名称进行模糊搜索"
            className={styles.searchContainer}
            allowClear
            onSearch={onConfirmSearch}
          />
          <RefreshButton onClick={onClickRefreshBtn}></RefreshButton>
        </div>
      </div>

      <Table
        dataSource={dataSource}
        columns={columns}
        rowKey="reportId"
        loading={{
          loading: queryReportListLoading,
          size: 'small'
        }}
        pagination={false}
        onChange={onTableChange}
      />

      <div className={styles.paginationContainer}>
        <Pagination
          showSizeChanger={true}
          showQuickJumper={true}
          current={pageNo}
          showTotal={showTotal}
          total={total}
          onChange={(page, pageSize) => {
            setPageNo(page);
            setPageSize(pageSize!);
            getReportList({
              page,
              pageSize
            });
          }}
        />
      </div>

      {isGenerateReportVisible && (
        <Modal
          closable={true}
          title="创建报表"
          visible={isGenerateReportVisible}
          onOk={handleConfirmGenerateReport}
          onCancel={handleCancelGenerateReport}
        >
          <Form
            name="generateReport"
            labelAlign="left"
            labelWidth={80}
            initialValues={{
              reportName: genRandomReportName(),
              dateTimeRange: [moment().subtract(2, 'weeks'), moment()],
              reportType: reportTypeList?.[0]?.value || ''
            }}
            className={cx('common-dialog-form-container')}
            form={form}
          >
            <Form.Item
              label="报表名称："
              name="reportName"
              rules={[{required: true, message: '请输入报表名称'}]}
            >
              <Input
                placeholder="请输入报表名称"
                id="reportName"
                allowClear
                maxLength={60}
              />
            </Form.Item>

            <Form.Item
              label="报表类型："
              name="reportType"
              rules={[
                {
                  required: true,
                  message: '请选择报表类型'
                }
              ]}
            >
              <Select
                placeholder="请选择报表类型"
                id="reportType"
                options={reportTypeList.map((item) => {
                  return {
                    label: item.text,
                    value: item.value
                  };
                })}
              ></Select>
            </Form.Item>

            <Form.Item
              label="日志时间范围"
              name="dateTimeRange"
              rules={[
                {
                  required: true,
                  message: '请选择日志时间范围'
                }
              ]}
            >
              <RangePicker
                locale={locale}
                id="dateTimeRange"
                ranges={{
                  近一周: [moment().subtract(1, 'weeks'), moment()],
                  近两周: [moment().subtract(2, 'weeks'), moment()]
                }}
              />
            </Form.Item>
          </Form>
        </Modal>
      )}
    </div>
  );
};

export default AuditReport;
