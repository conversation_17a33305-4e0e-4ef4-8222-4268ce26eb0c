import {Compiler, Compilation, sources} from 'webpack';
import http from 'http';
import https from 'https';
import fs from 'fs';
import path from 'path';

interface Options {
  sdpDeps: Record<string, string>;
  sdpMaps: Record<string, string>;
  isPrivate: boolean;
}

const getResource = (url: string): Promise<string> => {
  const api = /^https/.test(url) ? https : http;

  return new Promise((resolve, reject) => {
    const req = api.get(url, res => {
      const {statusCode} = res;

      let data = '';
      res.setEncoding('utf8');
      res.on('data', chunk => (data += chunk)); // eslint-disable-line no-return-assign
      res.on('end', () => {
        if (statusCode === 200) {
          resolve(data);
          return;
        }
        reject(data);
      });
    });

    req.on('error', reject);
  });
};
const depProcess = (depConfig: Record<string, string>) => {
  const res: Record<string, string> = {};
  for (const key in depConfig) {
    res[key] = depConfig[key]
      .replace('https://code.bdstatic.com', '.')
      .replace('https://bce.bdstatic.com', '.')
      .replace(/\.js$/, '');
  }
  return res;
};

const depDownload = async (depConfig: Record<string, string>) => {
  for (const key in depConfig) {
    const depPath = depConfig[key] + '.js';
    const jsSource = await getResource(depPath.replace(/^./, 'https://bj.bcebos.com/v1/bce-cdn'));
    const absolutePath = path.resolve(process.cwd(), './dist', depPath);
    const absoluteDir = absolutePath.slice(0, absolutePath.lastIndexOf('/'));
    if (!fs.existsSync(absoluteDir)) {
      fs.mkdirSync(absoluteDir, {recursive: true});
    }
    fs.writeFileSync(absolutePath, jsSource, 'utf8');
  }
};
export default class SdpEslConfig {
  options: Options;

  constructor(options: Options = {sdpDeps: {}, sdpMaps: {}, isPrivate: false}) {
    this.options = options;
  }
  apply(compiler: Compiler) {
    compiler.hooks.emit.tapAsync('SdpEslConfig', async (compilation: Compilation, callback: () => void) => {
      if (!Object.keys(this.options.sdpDeps).length && !Object.keys(this.options.sdpMaps).length) {
        return callback();
      }
      if (this.options.isPrivate && Object.keys(this.options.sdpDeps).length) {
        this.options.sdpDeps = depProcess(this.options.sdpDeps);
      }
      Object.keys(compilation.assets).forEach(fileName => {
        // 在main.js 头部添加内容
        if (fileName.includes('main.js')) {
          const asset = compilation.assets[fileName];
          const originalSource = asset.source(); // 获取原文件内容

          const modifiedSource = `${this.getEslConfig()}\n${originalSource}`;

          // 更新文件内容
          compilation.assets[fileName] = new sources.RawSource(modifiedSource);
        }
      });

      callback();
    });
    compiler.hooks.done.tap('SdpEslConfig', async () => {
      if (this.options.isPrivate && Object.keys(this.options.sdpDeps).length) {
        depDownload(this.options.sdpDeps);
      }
    });
  }

  getEslConfig() {
    return `(function () {
              require.config({
                ${Object.keys(this.options.sdpDeps).length ? 'paths:' + JSON.stringify(this.options.sdpDeps) : ''}
                ${Object.keys(this.options.sdpMaps).length ? ',map:' + JSON.stringify({'*': this.options.sdpMaps}) : ''}
              });
            })();`.replace(/\s+/g, ' ');
  }
}
