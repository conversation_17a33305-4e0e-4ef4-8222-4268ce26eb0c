.acud-menu-inline-header-item-text {
  font-size: 14px;
  font-weight: 600;
  color: #151b26;
}

.global-toast-error-container,
.global-toast-success-container {
  width: 420px !important;
}

.common-dialog-form-container {
  .acud-select,
  .acud-picker {
    width: 100%;
  }
}

.acud-modal-body {
  margin-bottom: 0;
}

.acud-picker-cell-inner {
  text-align: center;
}

.acud-table-tbody > tr > td.action-cell {
  padding: 0;
}

.table-status {
  padding-left: 0;
  vertical-align: middle;
}

.table-empty-container {
  padding: 80px 0;
}

.acud-table-cell {
  .acud-link {
    user-select: none;
  }
  .acud-link-disabled {
    cursor: not-allowed !important;
    color: #b8babf !important;

    &:hover {
      cursor: not-allowed !important;
      color: #b8babf !important;
    }
  }
}
