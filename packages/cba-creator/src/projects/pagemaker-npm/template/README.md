# amis 自定义组件模板（react 技术栈）

## 目录说明

- `src`: 自定义组件源码
- `src/index.ts`: 用于注册一个 amis 渲染器，注册成功后编辑器画布区中才会正常展示自定义组件内容；
- `src/plugin.ts`: 用于注册一个 amis-editor 插件，注册成功后编辑器左侧组件面板中会展示；
- `src/info-card`: (自定义组件目录，这里以组件 `info-card` 为例)
- `src/info-card/renderer.tsx`: 自定义组件 amis 渲染器
- `src/info-card/plugin`: 自定义组件 amis-editor 插件

## 安装环境

- 安装依赖
  > 指定`registry`为`http://registry.npm.baidu-int.com`
  > 推荐使用 `nrm` 管理 `registry`

```shell
    npm install --legacy-peer-deps
```

## 本地开发调试(带热更新)

```shell
    npm run dev
```

## Pagemaker 中调试

- 在 Pagemaker 平台中【应用设置】-【外部 js/css】中添加 http://localhost:8899/amis.widget.development.js
- 在 Pagemaker 应用使用自定义组件
- 由于组件是临时注册，暂不支持热更新（待优化）

## build: 构建自定义组件输出产物

```shell
    npm run build
```

## package.json 添加自定义组件信息，导入组件扩展包时需要

> package.json 中添加 amis-widgets 字段，用于放置当前自定义组件信息，
> 有这个 amis-widgets 字段才能被识别为 amis 组件扩展包。

```
  ...
    "amis-widgets": [
    {
      "name": "info-card", // 自定义组件名称，必填项
      "usage": "renderer", // 渲染器类型，非必填项，默认为 renderer
      "type": "info-card", // 自定义组件类型，必填项，同一应用下不允许有重复的自定义组件类型
      "entry": "/dist/index", // 自定义组件入口文件路径，必填项
      "editorPlugin": {  // amis-editor自定义插件信息
        "name": "信息展示卡片", // 自定义插件名称，在编辑器左侧组件面板作为title展示，必填项
        "description": "信息展示卡片", // 自定义插件描述，在编辑器左侧组件面板作为描述信息展示，必填项
        "pluginEntry": "/dist/plugin", // 自定义插件的入口文件，必填项
        "tag": [  // 自定义插件的分类，必填项
          "展示"
        ],
        "sort": 100 // 自定义插件的排序，非必填项
      }
    }
  ],
  ...
```

## 发布一个 NPM 组件扩展包

- 需要确保 package.json 中的 name 值唯一，version 值不重复。
- 发布内网需要确保包名以 ‘@fex’前缀开头，例如：@fex/cba-npm-widget-test

```
npm publish
```

## 发布到制定的 NPM 仓库

> 打开 NPM 配置文件（src/.npmrc），配置为制定仓库地址即可
