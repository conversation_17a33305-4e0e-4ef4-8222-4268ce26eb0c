{"name": "@baidu/cba-preset-console-react", "version": "1.2.8-beta.14", "license": "MIT", "main": "lib/index.js", "files": ["lib", "ssl", "template"], "scripts": {"build": "cba-scripts build", "dev": "cba-scripts dev"}, "dependencies": {"@babel/core": "^7.23.9", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-decorators": "^7.23.9", "@babel/plugin-proposal-private-methods": "^7.18.6", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-private-property-in-object": "^7.25.9", "@babel/plugin-transform-runtime": "^7.22.5", "@babel/plugin-transform-typescript": "^7.23.6", "@babel/preset-env": "^7.23.9", "@babel/preset-react": "^7.23.3", "@babel/preset-typescript": "^7.23.3", "@babel/runtime": "^7.23.9", "@baidu/cba-i18n": "workspace:*", "@baidu/cba-preset": "workspace:*", "@baidu/cba-utils": "workspace:*", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.11", "@svgr/webpack": "^8.1.0", "css-minimizer-webpack-plugin": "^5.0.1", "fs-extra": "^11.2.0", "html-webpack-plugin": "^5.5.3", "mini-css-extract-plugin": "2.7.7", "react-refresh": "^0.14.0", "shelljs": "^0.8.4", "thread-loader": "^4.0.2"}, "devDependencies": {"@types/fs-extra": "^11.0.4", "@types/shelljs": "^0.8.15"}}