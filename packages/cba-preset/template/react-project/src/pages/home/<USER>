import React from 'react';
import {But<PERSON>, Tag} from 'acud';
{{#enableI18n}}
import {useTranslation} from '@baidu/bce-react-toolkit';
{{/enableI18n}}
import './index.less';

const PageHome = (props: any) => {
  {{#enableI18n}}
  const {t} = useTranslation();

  {{/enableI18n}}
  return (
    <div className="home-page">
      <div className="home-content">
        <div>
          <h1>create-cba-app React 应用</h1>
        </div>
        <h3>易学易用，开箱即用，适用场景丰富的脚手架。</h3>
        <hr />
        <div>
          <a
            href="https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/tQvrZcAEPS/4yidKCir54/Yl586IZSCNxzLl"
            target="_blank"
          >
            <Button type="primary" size="large">
              {{^enableI18n}}使用文档 {{/enableI18n}}{{#enableI18n}}{t('使用文档')}{{/enableI18n}}
            </Button>
          </a>
        </div>
      </div>
    </div>
  );
};

export default PageHome;
