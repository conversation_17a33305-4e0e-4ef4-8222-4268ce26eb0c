import {IApi} from '@baidu/cba-preset';

import HtmlWebpackPlugin from 'html-webpack-plugin';
import CopyPlugin from 'copy-webpack-plugin';
import path from 'path';
import CssMinimizerPlugin from 'css-minimizer-webpack-plugin';
import {getFlags} from '@baidu/cba-utils';

export default (api: IApi) => {
  api.describe({
    key: 'preset-react:webpack-build',
    enableBy: () => api.env === 'production'
  });

  api.modifyWebpackConfig((webpackConfig, {merge}) => {
    const publicPath = api.userConfig.publicPath || '';

    const moduleName = `${api.userConfig.appName}AppModule`;
    const templatePath = api.userConfig.htmlTemplate
      ? path.isAbsolute(api.userConfig.htmlTemplate)
        ? api.userConfig.htmlTemplate
        : path.join(api.cwd, api.userConfig.htmlTemplate)
      : 'index.html';
    return merge(webpackConfig, {
      performance: false,
      output: {
        filename: 'static/js/[name].[contenthash:8].js',
        chunkFilename: 'static/js/[name].chunk.[contenthash:8].js',
        assetModuleFilename: 'static/images/[hash][ext][query]',
        library: moduleName,
        publicPath,
        clean: true
      },
      plugins: [
        new CssMinimizerPlugin(),
        new CopyPlugin({
          patterns: [
            {
              from: path.resolve('public'),
              to: path.resolve('dist'),
              toType: 'dir',
              noErrorOnMissing: true,
              globOptions: {
                ignore: ['**/*.html']
              },
              info: {
                minimized: true
              }
            }
          ]
        }),
        new HtmlWebpackPlugin({
          moduleName,
          appName: api.userConfig.appName,
          template: templatePath,
          inject: true,
          env: 'production'
        })
      ]
    });
  });

  api.onBeforeBundlerStart(async webpackConfig => {
    // 根据命令行参数中是否包含 hasFlags 来决定是否调用 getFlags 方法
    const hasFlags = api.args.hasFlags;

    if (hasFlags) {
      const flagPath = path.join(api.cwd, './src/flags.ts');
      await getFlags(
        {
          templateId: api.userConfig.templateId,
          flags: api.userConfig.flags
        },
        flagPath
      );
    }
  });
};
