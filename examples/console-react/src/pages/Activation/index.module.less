.introModule {
  padding: 24px;
  background-color: #fff;
  border-radius: 6px;
  margin-top: 16px;

  .title {
    font-size: 16px;
    color: #151b26;
    line-height: 24px;
    font-weight: 500;
    margin-bottom: 24px;
  }

  .introItemContainer {
    display: flex;
    align-items: stretch;
    gap: 24px;
  }

  .introItem {
    flex: 1;
    .header {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      .icon {
        width: 28px;
        margin-right: 12px;
      }

      .headerTitle {
        font-size: 14px;
        color: #151b26;
        letter-spacing: 0;
        text-align: center;
        line-height: 24px;
        font-weight: 500;
      }
    }

    .desc {
      font-size: 12px;
      color: #5c5f66;
      line-height: 22px;
      font-weight: 400;
    }
  }

  .introItemWithBorder {
    border: 1px solid #f2f2f2;
    border-radius: 6px;
    padding: 16px;
  }
}

.alertContainer {
  margin-bottom: 16px;

  :global {
    .acudicon-filled-warn {
      margin-top: 3px;
    }
  }

  .alertContentContainer {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}
