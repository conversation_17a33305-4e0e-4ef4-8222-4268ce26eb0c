import {join} from 'path';

import {
  IApi,
  ProjectType
} from '../../types';

export default (api: IApi) => {
  api.describe({
    key: 'cba-creator:base-react-project',
    enableBy: () => {
      return [
        ProjectType.reactProject,
        ProjectType.consoleReact,
        ProjectType.consoleReactEmbed
      ].includes(api.appData.projectType);
    }
  });

  api.addFileFromTemplate(async () => {
    const source = join(__dirname, 'template');
    const target = join(api.cwd, api.appData.projectName);
    await api.renderTemplate(source, target, api.appData);
  });

};
