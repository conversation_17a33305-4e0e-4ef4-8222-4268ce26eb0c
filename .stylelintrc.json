{"rules": {"function-name-case": "lower", "no-descending-specificity": null, "no-invalid-double-slash-comments": null, "value-keyword-case": ["lower", {"ignoreKeywords": ["dummy<PERSON><PERSON><PERSON>"]}], "font-family-no-missing-generic-family-keyword": [true, {"ignoreFontFamilies": ["PingFangSC-Regular", "PingFangSC-Medium"]}], "at-rule-empty-line-before": null, "comment-empty-line-before": null, "custom-property-empty-line-before": "never", "declaration-empty-line-before": "never", "length-zero-no-unit": null, "no-missing-end-of-source-newline": null, "number-leading-zero": null, "number-no-trailing-zeros": null}, "ignoreFiles": ["node_modules/**/*", "src/assets/**/*", "dist/**/*", "**/typings/**/*", "**/*.tsx"]}