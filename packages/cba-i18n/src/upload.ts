import fs from 'fs';
import path from 'path';
import {execSync} from 'child_process';
import {logger, secureReadJsonFile} from '@baidu/cba-utils';
import {Config} from '@baidu/cba-core';
import {IUserConfig} from '@baidu/cba-preset';

// eslint-disable-next-line import/no-commonjs
const json2po = require('json2po');

interface UploadParams {
  config: Config;
}

export default function upload({config}: UploadParams) {
  const {cwd} = config;
  const {i18n, appName} = config.getUserConfig() as IUserConfig;

  let {enabled, independent, output = './public/locales', sdpDir} = i18n || {};

  if (!enabled) {
    logger.error('Please enable i18n configuration in app config file.');
    return;
  }

  if (independent) {
    logger.error('Please disable independent i18n in app config file.');
    return;
  }

  const outputDir = path.isAbsolute(output) ? output : path.resolve(cwd, output);
  const poDir = path.resolve(outputDir, 'po');

  if (!outputDir.startsWith(cwd)) {
    logger.error(
      'The output path for i18n is incorrect. Please specify a subdirectory under the current working directory as the output path for i18n.'
    );
    return;
  }

  const corpusFilePath = path.resolve(outputDir, 'corpus.json');
  function ensurePath(filePath: string) {
    const dirname = path.dirname(filePath);
    if (!fs.existsSync(dirname)) {
      ensurePath(dirname);
      fs.mkdirSync(dirname);
    }
    return filePath;
  }

  if (!fs.existsSync(corpusFilePath)) {
    logger.error(`The ${corpusFilePath} is not found.`);
  } else {
    const {fileContent: corpus, err, isEmpty} = secureReadJsonFile(corpusFilePath);
    if (err || isEmpty) {
      logger.error(`There is no pending corpus data to be uploaded.`);
    } else {
      const po = json2po(corpus);
      fs.writeFileSync(ensurePath(path.resolve(poDir, `${appName}.i18n.zh-cn.po`)), po);
    }
  }
  fs.readdir(poDir, (err, files) => {
    if (files.length > 0) {
      execSync(`npx bce-i18n upload -d ${poDir} --module ${appName} && rm -rf ${poDir}`, {
        cwd,
        encoding: 'utf8',
        stdio: 'inherit'
      });
    }
  });
}
