/**
 * @file 定义虚拟文件插件
 * <AUTHOR> (<EMAIL>)
 */

import path from 'path';
import fs from 'fs';
import VirtualModulesPlugin from 'webpack-virtual-modules';
import {IApi} from '@baidu/cba-preset';

const getTpl = (name: string) => {
  return fs.readFileSync(path.join(__dirname, `../../template/runtime/${name}`), 'utf-8');
};

export default (api: IApi) => {
  api.modifyWebpackConfig((webpackConfig, {merge}) => {
    return merge(webpackConfig, {
      resolve: {
        alias: {
          // 作为 bce-cli-react 的内部模块
          '@baidu/cba-cli/runtime': '/src/cba-cli-helper.tsx'
        }
      },
      plugins: [
        // 虚拟文件，对用户不可见，隐藏在脚手架里
        new VirtualModulesPlugin({
          'src/cba-cli-helper.tsx': getTpl('cba-cli-helper.tsx'),
          'src/hidden-provider.tsx': getTpl(`providers/${api.userConfig?.stateName || 'empty'}.tsx`),
          'src/hidden-router.tsx': getTpl('hidden-router.tsx'),
          'hidden-bootstrap.tsx': getTpl('hidden-bootstrap.tsx'),
          'hot-app.tsx': getTpl('hot-app.tsx'),
          'go-pagemaker.tsx': getTpl('go-pagemaker.tsx'),
          'hidden-request.ts': getTpl('hidden-request.ts'),
          'flags.ts': getTpl('flags.ts')
        })
      ]
    });
  });
};
