import {IApi, Env, IUserConfig} from './types';
import AddAssetsToHtml from './bundler-webpack/plugins/AddAssetsToHtmlPlugin';

export {Env, AddAssetsToHtml};
export type {IApi, IUserConfig};

export default (api: IApi) => {
  api.describe({
    key: 'preset'
  });

  return {
    plugins: [
      require.resolve('./registerMethods'),
      require.resolve('./checkVersion'),
      require.resolve('./init/index'),

      require.resolve('./commands/build'),
      require.resolve('./commands/dev'),
      require.resolve('./commands/help'),
      require.resolve('./commands/migrate'),
      require.resolve('./commands/version')
    ]
  };
};
