/**
 * @file dev & build 公共配置
 * <AUTHOR> (<EMAIL>)
 */
import webpack from 'webpack';
import {IApi} from '@baidu/cba-preset';
import {getBabelConfig} from './babelConfig';

export default (api: IApi) => {
  api.describe({
    key: 'preset-console-react-embed:webpack-common'
  });

  api.modifyWebpackConfig((webpackConfig, {merge}) => {
    webpackConfig.module ||= {};
    webpackConfig.module.rules = [
      {
        oneOf: [
          {
            oneOf: [
              {
                test: /\.css$/,
                use: [
                  'style-loader',
                  'css-loader',
                  {
                    loader: 'postcss-loader',
                    options: {
                      postcssOptions: {
                        plugins: ['postcss-preset-env']
                      }
                    }
                  }
                ]
              },
              {
                test: /\.module\.less$/,
                use: [
                  'style-loader',
                  {
                    loader: 'css-loader',
                    options: {
                      modules: {
                        localIdentName: '[local]_[hash:base64:5]'
                      }
                    }
                  },
                  {
                    loader: 'postcss-loader',
                    options: {
                      postcssOptions: {
                        plugins: ['postcss-preset-env']
                      }
                    }
                  },
                  'less-loader'
                ]
              },
              {
                test: /\.less$/,
                use: [
                  'style-loader',
                  'css-loader',
                  {
                    loader: 'postcss-loader',
                    options: {
                      postcssOptions: {
                        plugins: ['postcss-preset-env']
                      }
                    }
                  },
                  'less-loader'
                ]
              },
              {
                test: /\.(png|jpg|gif)$/i,
                use: [
                  {
                    loader: 'url-loader',
                    options: {
                      limit: 20480 // 最大支持20kb图片转base64
                    }
                  }
                ]
              },
              {
                test: /\.svg$/,
                use: ['@svgr/webpack']
              },
              {
                test: /\.(jsx|js)$/,
                use: [
                  {
                    loader: 'babel-loader',
                    options: getBabelConfig(api.userConfig)
                  }
                ],
                exclude: /node_modules/
              },
              {
                test: /\.tsx?$/,
                use: [
                  {
                    loader: 'babel-loader',
                    options: getBabelConfig(api.userConfig)
                  }
                ],
                exclude: /node_modules/
              }
            ]
          }
        ]
      }
    ];
    return merge(webpackConfig, {
      entry: './src/embed.tsx',
      plugins: [
        new webpack.DefinePlugin({
          APP_TITLE: JSON.stringify(api.userConfig.appTitle || '百度智能云'),
          APP_ENABLE_I18N: JSON.stringify(api.userConfig?.i18n?.enabled),
          APP_ENABLE_INDEPENDENT_I18N: JSON.stringify(api.userConfig?.i18n?.independent),
          APP_ALLOWED_LANGUAGE_TYPES: JSON.stringify(api.userConfig?.i18n?.supportedLanguages),
          APP_IS_EMBED_MODE: true
        })
      ].filter(Boolean)
    });
  });
};
