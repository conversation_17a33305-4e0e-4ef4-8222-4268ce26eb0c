import {
  AppContextActionType,
  ServiceParam,
  useAppContext,
  useDocumentTitle
} from '@baidu/bce-react-toolkit';
import {AppLayout} from '@baidu/bce-react-toolkit';
import React, {useEffect, useMemo, useState} from 'react';
import {HashRouter, Navigate, Route, Routes} from 'react-router-dom';

import menus, {flattenedMenuList} from '@/pages';

import {getServiceParam} from './apis/auth';
import Activation from './pages/Activation';
import urls from './utils/urls';
export default function App() {
  const routes = useMemo(() => {
    return flattenedMenuList.filter((item) => item.Component);
  }, []);

  const {appState, appDispatch} = useAppContext();

  useDocumentTitle();
  const [serviceParams, setServiceParams] = useState<ServiceParam[]>([]);

  useEffect(() => {
    getServiceParam().then((res) => {
      setServiceParams(res.result);
      // TODO: 实际产品开发时，去除下面的 appDispatch 代码
      appDispatch({
        type: AppContextActionType.ACTIVATE_PRODUCT
      });
    });
  }, [appDispatch]);
  return (
    <HashRouter>
      <AppLayout
        menus={menus}
        activationPagePath={urls.activation}
        serviceParams={serviceParams}
      >
        <Routes>
          {appState.isActivated ? (
            routes.map(({key, Component}) => {
              const Element = Component!;
              return <Route path={key} key={key} element={<Element />}></Route>;
            })
          ) : (
            <Route
              path={urls.activation}
              key={urls.activation}
              element={<Activation />}
            ></Route>
          )}
          <Route path="*" element={<Navigate to={urls.activation} replace />} />
        </Routes>
      </AppLayout>
    </HashRouter>
  );
}
