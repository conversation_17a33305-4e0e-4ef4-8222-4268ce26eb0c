import {existsSync} from 'fs-extra';
import path from 'path';
import Generator from '../../Generator';
import {IApi} from '../../types';
import {CUSTOM_TEMPLATE_ROOT} from '../../constants';

export default (api: IApi) => {
  api.describe({
    key: 'generate:basePage'
  });

  api.onGenerateFilesEnd(async args => {});

  api.registerGenerator({
    name: 'basePage',
    description: '基础页面',
    fn: async (generateArgs: GenerateArgs) => {
      const {fallback, eject} = generateArgs;

      const innerPath = path.join(__dirname, './template');
      const customTargetPath = path.join(api.cwd, CUSTOM_TEMPLATE_ROOT, 'base-page');
      let templatePath = fallback ? innerPath : customTargetPath;
      // 处理eject文件被删除情况
      if (!existsSync(templatePath) || eject) {
        templatePath = innerPath;
      }

      const options = {
        ...generateArgs,
        templatePath,
        targetPath: path.join(api.cwd, 'src/pages'),
        customTargetPath
      };

      const generator = new Generator(options);
      await generator.run();
    }
  });
};
