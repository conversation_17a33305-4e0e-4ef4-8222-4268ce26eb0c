.home-page {
  height: calc(100vh - 50px);
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: 'Inter var', 'Inter', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
    'Helvetica Neue', Helvetica, Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji',
    'Segoe UI Symbol', 'Noto Color Emoji';

  .home-content {
    text-align: center;
    width: 80%;
    .logo-wrap {
      img {
        width: 120px;
        height: auto;
        &:not(:first-child) {
          margin-left: 40px;
        }
      }
    }
  }
  h1 {
    font-size: 56px;
    font-weight: 500;
    margin: 24px 0;
  }
  h3 {
    font-size: 24px;
    font-weight: 500;
    color: #333;
  }
  hr {
    margin: 30px 0;
    transform: scaleY(0.2);
  }
  .home-btn {
    margin-right: 24px;
  }
  .touch-me {
    user-select: none;
    background-image: -webkit-linear-gradient(to bottom, #f4f1ee, #fff);
    background-image: linear-gradient(to bottom, #f4f1ee, #fff);
    border-radius: 50%;
    box-shadow: 0 8px 10px 0 rgba(0, 0, 0, 0.3), inset 0 4px 1px 1px white,
      inset 0 -3px 1px 1px rgba(204, 198, 197, 0.5);
    margin: 0 30px 30px 0;
    position: relative;
    transition: all 0.1s linear;
    &:hover {
      background-image: -webkit-linear-gradient(to bottom, #fff, #f4f1ee);
      background-image: linear-gradient(to bottom, #fff, #f4f1ee);
      div {
        transition: all 0.4s linear;
        color: #83d244;
        text-shadow: 0 0 6px #83d244;
      }
    }
    &:active {
      background-image: -webkit-linear-gradient(top, #efedec, #f7f4f4);
      background-image: linear-gradient(to bottom, #efedec, #f7f4f4);
      box-shadow: 0 3px 5px 0 rgba(0, 0, 0, 0.4), inset 0 -3px 1px 1px rgba(204, 198, 197, 0.5);
      &:after {
        color: #dbd2d2;
        text-shadow: 0 -1px 1px #bdb5b4, 0px 1px 1px white;
      }
    }

    width: 120px;
    height: 120px;
    margin: 30px auto 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    cursor: pointer;

    div {
      padding: 0;
      color: #e9e6e4;
      font-weight: bold;
      text-shadow: 0 -1px 1px #bdb5b4, 1px 1px 1px white;
    }
    .label {
      font-size: 20px;
      margin-top: 20px;
    }
    .count {
      font-size: 28px;
    }
  }
}
