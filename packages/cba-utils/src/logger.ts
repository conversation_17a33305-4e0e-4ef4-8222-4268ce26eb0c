/**
 * @file 日志输出
 */
import logger from 'debug';
import {success, warning, error} from 'log-symbols';
import chalk from 'chalk';

logger.enable('cba-cli:*');

export interface ILogger {
  /**
   * 打印信息
   * @param formater 输出信息
   * @param args 其他信息
   * @returns
   */
  info: (formater: string, ...args: any) => void;
  /**
   * 打印警告信息
   * @param formater 输出信息
   * @param args 其他信息
   * @returns
   */
  warn: (formater: string, ...args: any) => void;
  /**
   * 打印错误信息
   * @param formater 输出信息
   * @param args 其他信息
   * @returns
   */
  error: (formater: string, ...args: any) => void;
}

export default {
  info(formater: string, ...args: any) {
    logger(`cba-cli:info  ${success}`)(chalk.greenBright(formater), ...args);
  },
  warn(formater: string, ...args: any) {
    logger(`cba-cli:warn  ${warning}`)(chalk.yellowBright(formater), ...args);
  },
  error(formater: string, ...args: any) {
    logger(`cba-cli:error ${error}`)(chalk.redBright(formater), ...args);
  }
};
