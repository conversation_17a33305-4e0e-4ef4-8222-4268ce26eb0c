import {Service} from '@baidu/cba-core';
import yargsParser from 'yargs-parser';
import {Env} from './types';
import {DEFAULT_CONFIG_FILES} from './constants';

export async function run() {
  const args = yargsParser(process.argv.slice(2), {
    alias: {
      version: ['v', '--version'],
      help: ['h'],
      mock: ['--mock']
    },
    boolean: ['version']
  });
  let command = args._[0];
  if (!command) {
    if (args['version']) {
      command = 'version';
    } else if (args['help']) {
      command = 'help';
    }
  }

  let additionalEnv: Record<string, any> = {};

  if (command === 'dev') {
    process.env.NODE_ENV = 'development';
  } else if (command === 'build') {
    process.env.NODE_ENV = 'production';
  }

  if (args.forceLocal) {
    additionalEnv = {
      BCE_APP_FORCE_LOCAL: true
    };
  }

  new Service({
    cwd: process.cwd(),
    env: process.env.NODE_ENV as Env,
    presets: ['@baidu/cba-preset'], // 预设插件
    plugins: [],
    defaultConfigFile: DEFAULT_CONFIG_FILES
  }).run({
    name: command as string,
    args,
    additionalEnv
  });
}
