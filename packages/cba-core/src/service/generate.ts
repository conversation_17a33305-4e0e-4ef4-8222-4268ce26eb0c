/**
 * @file 生成器类，用于封装命令行中的命令
 * <AUTHOR> (<EMAIL>)
 */


export interface IGenerateOpts {
  /**
   * 生成器类型
   */
  name: string;
  /**
   * 命令描述
   */
  description: string;
  /**
   * 命令执行函数
   */
  fn: ({args}: any) => Promise<any> | void;
}

export class Generate {
  name: string;
  description?: string;
  details?: string;
  fn: ({args}: any) => Promise<any> | void;

  constructor(opts: IGenerateOpts) {
    this.name = opts.name;
    this.description = opts.description;
    this.fn = opts.fn;
  }
}
