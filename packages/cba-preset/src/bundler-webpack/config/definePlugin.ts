/**
 * @file 添加环境变量
 * <AUTHOR> (<EMAIL>)
 */

import {DefinePlugin} from 'webpack';
import Config from 'webpack-chain';
import {getProvideEnv} from '@baidu/cba-utils';

interface IOpts {
  env: string;
  config: Config;
  userConfig: any;
  additionDefine?: {
    [key: string]: any;
  };
}

export function addDefinePlugin(opts: IOpts) {
  const {env, config, userConfig, additionDefine} = opts;
  config.plugin('preset:DefinePlugin').use(DefinePlugin, [
    {
      'APP_NAME': JSON.stringify(userConfig.appName),
      'process.env': JSON.stringify(getProvideEnv()),
      ...additionDefine
    }
  ]);
}
