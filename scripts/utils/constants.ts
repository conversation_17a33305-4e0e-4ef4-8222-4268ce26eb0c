import {join} from 'path';

export const ROOT = join(__dirname, '../../');

export const PATHS = {
  ROOT,
  PACKAGES: join(ROOT, './packages'),
  LERNA_CONFIG: join(ROOT, './lerna.json'),
  CHANGELOG: join(ROOT, './changelog.md'),
  TEMPLATE_PROJECT: join(ROOT, './packages/cba-preset/template/project'),
  CONFIG: join(ROOT, 'config.json')
} as const;

export const CHANGELOG_PREFIX = '# 版本变更信息';

// 包名前缀
export const CLI_PACKAGES_REG = /^@baidu\/cba-/;
