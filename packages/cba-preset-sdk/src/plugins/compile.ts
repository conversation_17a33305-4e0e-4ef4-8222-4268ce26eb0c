import {IApi} from '@baidu/cba-preset';
import {rimrafSync} from 'rimraf';
import gulp from 'gulp';
import through2 from 'through2';
import {logger} from '@baidu/cba-utils';
import ts from 'gulp-typescript';
import tts from 'ttypescript';
import concat from 'gulp-concat';
import babel from 'gulp-babel';
import merge2 from 'merge2';
import getTsConfig from '../utils/getTsConfig';
import cssInjection from '../utils/cssInjection';
import getBabelConfig from '../utils/getBabelConfig';
import compileLess from '../utils/compileLess';
import configuration from '../configuration';

const tsDefaultReporter = ts.reporter.defaultReporter();

export default (api: IApi) => {
  api.registerCommand({
    name: 'compile',
    description: 'SDK 编译',
    fn: async ({config}) => {
      configuration.setConfig(config);

      const {type = 'es'} = config.args;
      const isESModule = type === 'es';

      const getPath = configuration.getPath.bind(configuration);
      const getCwd = configuration.getCwd.bind(configuration);

      const esDir = getPath('es');
      const libDir = getPath('lib');

      const targetDir = isESModule ? esDir : libDir;
      rimrafSync(targetDir);

      const srcOptions = {
        root: getCwd(),
        cwd: getCwd()
      };

      const lessOutput = gulp
        .src('src/**/*.less', srcOptions)
        .pipe(
          through2.obj(async function (file, encoding, next) {
            try {
              const css = await compileLess(file.path);
              file.contents = Buffer.from(css);
              file.path = file.path.replace(/\.less$/, '.css');
              this.push(file);
            } catch (e: any) {
              logger.error(e.message);
            }
            next();
          })
        )
        .pipe(gulp.dest(targetDir));

      const assets = gulp.src('src/**/*.@(png|svg)', srcOptions).pipe(gulp.dest(targetDir));

      let error = 0;
      const source = ['src/**/*.tsx', 'src/**/*.ts'];
      const tsConfig = getTsConfig();
      if (tsConfig.paths) {
        const transformAliasPath2RelativePathPlugins = [
          {transform: 'typescript-transform-paths' },
          {transform: 'typescript-transform-paths', 'afterDeclarations': true }
        ]
        tsConfig.typescript = tts;
        if (!tsConfig.plugins) {
          tsConfig.plugins = transformAliasPath2RelativePathPlugins
        } else if (Array.isArray(tsConfig.plugins)) {
          tsConfig.plugins = [...tsConfig.plugins, ...transformAliasPath2RelativePathPlugins]
        }
      }
      const tsCompileOutput = gulp.src(source, srcOptions).pipe(
        ts(tsConfig, {
          error(e, _ts) {
            tsDefaultReporter.error?.(e, _ts);
            error = 1;
          },
          finish: tsDefaultReporter.finish
        })
      );

      function checkTsCompile() {
        if (error) {
          logger.error('Typescript file compile failed');
          process.exit(1);
        }
      }

      tsCompileOutput.on('finish', checkTsCompile);
      tsCompileOutput.on('end', checkTsCompile);

      const dts = tsCompileOutput.dts.pipe(gulp.dest(targetDir));
      const js = tsCompileOutput.js
        .pipe(
          through2.obj(function z(file, encoding, next) {
            const content = file.contents.toString(encoding);
            file.contents = Buffer.from(cssInjection(content));
            this.push(file);
            next();
          })
        )
        .pipe(babel(getBabelConfig()))
        .pipe(gulp.dest(targetDir));

      const globalDts = gulp
        .src('src/types/*.d.ts', srcOptions)
        .pipe(concat('types/global.d.ts'))
        .pipe(gulp.dest(targetDir));

      return merge2([lessOutput, assets, js, dts, globalDts]);
    }
  });
};
