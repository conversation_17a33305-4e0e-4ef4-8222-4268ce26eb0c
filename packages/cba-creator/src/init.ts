import {
  logger,
  isCliPresetPackage,
  getPackageLatestVersion,
  formatFile
} from '@baidu/cba-utils';
import {
  existsSync,
  writeFileSync
} from 'fs-extra';
import ora from 'ora';
import shell from 'shelljs';
import yargsParser from 'yargs-parser';
import inquirer from 'inquirer';
import {join} from 'path';

import {
  IApi,
  IProject
} from './types';

export default (api: IApi) => {
  api.describe({
    key: 'creator'
  });

  api.registerCommand({
    name: 'init',
    description: 'init bce-cli project',
    fn: async ({args}: yargsParser.Arguments) => {
      create(api, args);
    }
  });
};

async function create(api: IApi, args: yargsParser.Arguments) {
  const projectName = await getProjectName(args);
  // 1、收集项目类型
  const projects = await api.applyPlugins({
    key: 'addProject'
  });
  let {projectType} = await inquirer.prompt([
    {
      type: 'list',
      message: '请选择初始化项目类型:',
      name: 'projectType',
      default: '',
      choices: projects
    }
  ]);
  const project = projects.find((project: IProject) => project.value === projectType) || {};
  if (project.children && project.children.length) {
      const childProject = await inquirer.prompt([
      {
        type: 'list',
        message: '请选择初始化项目类型:',
        name: 'projectType',
        default: '',
        choices: project.children
      }
    ]);
    projectType = childProject.projectType;
  }

  api.updateAppData((appData) => {
    return {
      ...appData,
      projectType
    };
  });

  // 2、获取提示词
  const prompts = await api.applyPlugins({
    key: 'addPrompts',
    initialValue: []
  });
  let answers = {
    projectName,
    projectType
  };
  if (prompts.length) {
    answers = {
      ...answers,
      ...await inquirer.prompt(prompts)
    }
  }

  api.updateAppData((appData) => {
    return {
      ...appData,
      ...answers
    };
  });

  // 3、通过模板生成工程文件
  await api.applyPlugins({
    key: 'addFileFromTemplate'
  });

  // 4、修改生成后的工程文件
  await api.applyPlugins({
    key: 'modifyFile'
  });
  // 5、修改package.json文件
  const pkgPath = join(api.cwd, api.appData.projectName, 'package.json');
  let pkg = require(pkgPath);
  pkg = await api.applyPlugins({
    key: 'modifyPkgJson',
    initialValue: pkg
  });

  for (const key in (pkg.dependencies || {})) {
    if (isCliPresetPackage(key)) {
      const pkgVersion = await getPackageLatestVersion(key);
      pkg.dependencies[key] = `${pkgVersion}`;
    }
  }

  for (const key in (pkg.devDependencies || {})) {
    if (isCliPresetPackage(key)) {
      const pkgVersion = await getPackageLatestVersion(key);
      pkg.devDependencies[key] = `${pkgVersion}`;
    }
  }
  pkg.name = api.appData.projectName;
  writeFileSync(pkgPath, JSON.stringify(pkg, null, 2), 'utf-8');

  // 6、修改bce-config配置文件
  const configPath = join(api.cwd, api.appData.projectName, 'bce-config.js');
  let config = require(configPath);
  config = await api.applyPlugins({
    key: 'modifyConfig',
    initialValue: config
  });
  let configConent = `const {defineConfig} = require('@baidu/cba-cli');\n\n`;
  configConent += `module.exports = defineConfig(
    ${JSON.stringify(config, null, 2)}
  );\n`;
  writeFileSync(configPath, configConent, 'utf-8');
  await formatFile(configPath);
  // 7、安装一些依赖
  afterCreate(projectName);

}

async function getProjectName(args: yargsParser.Arguments) {
  let projectName = args._[0] as string;
  const nameAnswer = await inquirer.prompt([
    {
      type: 'input',
      message: '请输入项目名称: (默认当前目录)',
      name: 'projectName',
      when: () => {
        return !projectName;
      }
    }
  ]);
  projectName = nameAnswer.projectName || projectName || './';

  if (projectName && existsSync(projectName)) {
    if (args.force) {
      shell.rm('-rf', projectName);
    }
    else {
      const {overwrite} = await inquirer.prompt([
        {
          type: 'confirm',
          name: 'overwrite',
          message: projectName === './' ? '是否在当前文件夹创建' : `是否覆盖已存在文件夹【${projectName}】`
        }
      ]);
      if (!overwrite) {
        process.exit(1);
      }
    }
  }
  projectName && shell.exec(`mkdir ${projectName}`);
  return projectName;
}
function afterCreate(projectName: string) {
  projectName && shell.cd(projectName);
  const spinner = ora();
  spinner.prefixText = '已执行命令：npm install --legacy-peer-deps， 自动为您安装依赖中';
  spinner.start();
  shell.exec('npm install --legacy-peer-deps', (code, stdout, stderr) => {
    projectName && shell.cd('..');
    spinner.prefixText = '';
    spinner.text = '依赖安装完成！';
    spinner.succeed();
  });
}
