import {spawnSync} from '../utils';
import {PATHS} from '../utils/constants';

(async () => {
  const args = process.argv.slice(2);

  // no cache
  if (args.includes('--no-cache')) {
    args.unshift('--force');
  }

  // filter
  if (!args.includes('--filter')) {
    args.unshift('--filter', `"./packages/*"`);
  }

  const isDev = args.includes('dev');
  if (isDev) {
    // build一遍，生成lib文件
    spawnSync(`pnpm run build --parallel`);
  }

  const command = `turbo run ${args.join(' ')}`;
  spawnSync(command, {
    cwd: PATHS.ROOT
  });
})();
