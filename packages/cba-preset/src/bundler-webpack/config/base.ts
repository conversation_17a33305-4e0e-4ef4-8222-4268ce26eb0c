/**
 * @file webpack 基础配置
 * 说明：仅包含了基础配置，部分配置在config中通过chainWebpack进行了加载
 * 如：definePlugin、ProgressPlugin、devServer等
 *
 * <AUTHOR> (<EMAIL>)
 */

import path from 'path';
import TerserPlugin from 'terser-webpack-plugin';
import {CleanWebpackPlugin} from 'clean-webpack-plugin';
import {Configuration} from 'webpack';

const terserPlugin: any = new TerserPlugin({
  parallel: true
});

/**
 * 获取 webpack 基础配置
 * @returns
 */
export function getBaseConfig(opts: {cwd: string}): Configuration {
  return {
    entry: './src/index',
    output: {
      path: path.resolve(opts.cwd, './dist'),
      filename: '[name].[chunkhash:8].js'
    },
    resolve: {
      // 按顺序解析后缀名
      extensions: ['.tsx', '.ts', '.jsx', '.js', '.json'],
      // 解析别名
      alias: {
        '@': path.resolve(opts.cwd, './src')
      }
    },
    module: {
      rules: []
    },
    plugins: [new CleanWebpackPlugin()],
    optimization: {
      minimizer: [terserPlugin]
    }
  };
}
