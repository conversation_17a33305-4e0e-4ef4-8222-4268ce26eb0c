/*
 * @description: acl列表页
 * @file: network/acl/pages/List.js
 * @author: pian<PERSON><EMAIL>
 */
import u from 'lodash';
import {Component} from 'san';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {Message} from '@baidu/sui';
import {OutlinedEditingSquare} from '@baidu/sui-icon';
import {San2React} from '@baidu/bce-react-toolkit';

import './list.less';

const {invokeSUI, invokeSUIBIZ, template} = decorators;
const tpl = html` <template>
  <s-biz-page class="{{klass}}">
    <div class="vpc-acl-header" slot="header">
      <span class="title">{{context.title}}</span>
      <vpc-select class="vpc-select" on-change="vpcChange" on-int="vpcInt" />
      <a
        href="{{DocService.acl_helpFile}}"
        target="_blank"
        class="help-file"
        s-if="{{!FLAG.NetworkSupportXS}}"
      >
        <s-icon name="warning-new" />帮助文档
      </a>
    </div>
    <s-table
      columns="{{table.columns}}"
      loading="{{table.loading}}"
      error="{{table.error}}"
      datasource="{{table.datasource}}"
      on-selected-change="tableSelected($event)"
      on-filter="onFilter"
      on-sort="onSort"
      selection="{=table.selection=}"
    >
      <div slot="empty">
        <s-empty>
          <div slot="action"></div>
        </s-empty>
      </div>
      <div slot="error">
        啊呀，出错了
        <a href="javascript:;" on-click="refresh">重新加载</a>
      </div>
      <div slot="c-shortId">
        <span class="truncated" title="{{row.name}}">
          <a
            href="#/vpc/acl/manage?vpcId={{row.vpcId}}&id={{row.id}}&vpcShortId={{row.vpcShortId}}"
            data-track-id="ti_vpc_dcgw_detail"
            data-track-name="详情"
          >
            {{row.name}}
          </a>
        </span>
        <s-popover
          s-ref="{{'instanceNameEdit'+rowIndex}}"
          placement="right"
          trigger="click"
          class="edit-popover-class"
        >
          <div class="edit-wrap" slot="content">
            <s-input
              value="{=instanceName.value=}"
              width="320"
              placeholder="请输入名称"
              on-input="onNameInput($event, rowIndex)"
            />
            <div class="edit-tip">
              支持大小写字母，数字，中文和"-_/.”以字母或者中文开头，不超过65个字
            </div>
            <s-button
              skin="primary"
              s-ref="{{'editNameBtn'+rowIndex}}"
              disabled="{{true}}"
              on-click="editConfirm(row, rowIndex)"
              >确定</s-button
            >
            <s-button on-click="editNameCancel(row, rowIndex)">取消</s-button>
          </div>
          <outlined-editing-square
            s-if="row.name!=='默认安全组'&&row.NetworkAclOpt"
            class="name-icon"
            on-click="editName(row)"
          />
        </s-popover>
        <br />
        <span class="truncated" title="{{row.shortId}}"
          >{{row.shortId||row.id}}</span
        >
        <s-clip-board
          s-if="row.shortId||row.id"
          class="name-icon"
          text="{{row.shortId||row.id}}"
        />
      </div>
      <div slot="c-opt">
        <span class="operations">
          <s-button skin="stringfy" on-click="manageAcl(row)">管理</s-button>
        </span>
      </div>
    </s-table>
    <!--企业版版本不支持重构版本-->
    <s-pagination
      s-if="{{flag.NetworkAclOpt && pager.total}}"
      slot="footer"
      layout="{{'total, pageSize, pager, go'}}"
      size="{{pager.size}}"
      total="{{pager.total}}"
      page="{{pager.page}}"
      on-pagerChange="onPagerChange"
      on-pagerSizeChange="onPagerSizeChange"
    />
  </s-biz-page>
</template>`;

@template(tpl)
@invokeSUI
@invokeSUIBIZ
class AclTable extends Component {
  static components = {
    'outlined-editing-square': OutlinedEditingSquare
  };
  initData() {
    return {
      flag: {},
      klass: ['main-wrap-new', 'vpc-acl-list'],
      title: 'ACL',
      vpcId: '',
      vpcList: [
        {
          text: '所在网络：全部私有网络',
          value: ''
        }
      ],
      table: {
        loading: false,
        selection: {
          selectedIndex: []
        },
        columns: [
          {
            name: 'shortId',
            label: 'ACL名称/ID'
          },
          {
            name: 'vpc_id',
            label: '所在网络',
            render(item) {
              let vpcName = u.escape(item.vpcName) || '-';
              let vpcShortId = u.escape(item.vpcShortId || item.vpcId) || '-';
              let vpcId = u.escape(item.vpcId);
              return `
                                <span class="truncated" title="${vpcName}">
                                    <a href="#/vpc/instance/detail?vpcId=${vpcId}" class="text-hidden">${vpcName}</a>
                                </span>
                                <br>
                                <span class="truncated" title="${vpcShortId}">${vpcShortId}</span>`;
            }
          },
          {
            name: 'opt',
            label: '操作'
          }
        ],
        datasource: []
      },
      pager: {
        size: 10,
        page: 1,
        total: 0
      },
      instanceName: {
        value: '',
        error: true,
        visible: false
      },
      DocService: {}
    };
  }

  vpcInt() {
    // this.loadAclList();
  }

  loadAclList() {
    this.data.set('table.loading', true);
    // todo:20201203版本acl企业版版本还不支持分页及重构四期

    let url = this.getPayload();
    return this.$http
      .getAclList(url)
      .then((data) => {
        this.data.set('table.loading', false);
        this.data.set('table.datasource', data.result);
        this.data.set('pager.total', data.totalCount);
      })
      .catch((err) => this.data.set('table.loading', false));
  }

  getPayload() {
    const {pager} = this.data.get('');
    let payload = {
      vpcId: window.$storage.get('vpcId'),
      pageNo: pager.page,
      pageSize: pager.size
    };
    return payload;
  }

  manageAcl(item) {
    redirect('#/vpc/acl/manage?vpcId=' + item.vpcId + '&id=' + item.id);
  }

  vpcChange() {
    this.loadAclList();
  }

  attached() {
    // 企业版版本不支持重构版本
    // this.loadAclList();
  }

  // 点击修改名称icon
  editName(row) {
    this.data.set('instanceName.value', row.name);
    this.data.set('instanceName.error', false);
  }

  // 修改名称确认
  editConfirm(row, rowIndex) {
    let instanceName = this.data.get('instanceName');
    if (instanceName.error) {
      return;
    }
    this.$http
      .updateAclName({
        name: instanceName.value,
        id: row.id
      })
      .then(() => {
        this.editNameCancel(row, rowIndex);
        this.loadAclList();
        Message.success({
          content: '修改成功'
        });
      });
  }

  // 输入名称
  onNameInput(e, rowIndex) {
    let result = false;
    if (
      e.value === '' ||
      !/^[\u4e00-\u9fa5a-zA-Z][\u4e00-\u9fa5\w\-\_\/\.]{0,64}$/.test(e.value)
    ) {
      result = true;
    } else {
      result = false;
    }
    this.data.set('instanceName.error', result);
    this.data.set('instanceName.value', e.value);
    this.ref('editNameBtn' + rowIndex).data.set('disabled', result);
  }

  // 修改名称取消
  editNameCancel(row, rowIndex) {
    this.ref('editNameBtn' + rowIndex).data.set('disabled', true);
    this.ref('instanceNameEdit' + rowIndex).data.set('visible', false);
  }

  // 改变页数
  onPagerChange(e) {
    this.data.set('pager.page', e.value.page);
    this.loadAclList();
  }

  // 改变每页显示个数
  onPagerSizeChange(e) {
    this.data.set('pager.size', e.value.pageSize);
    this.data.set('pager.page', 1);
    this.loadAclList();
  }
  onRegionChange() {
    location.reload();
  }
}

export default San2React(Processor.autowireUnCheckCmpt(AclTable));
