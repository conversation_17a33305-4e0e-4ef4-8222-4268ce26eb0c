/**
 * @file amis相关的方法
 * <AUTHOR> (<EMAIL>)
 */
import fs from 'fs';

export function getLocalAmisVersion() {
  const amisSdkPathPrefix = fs.existsSync('node_modules/amis/') ? '' : '../../';
  try {
    const data = fs.readFileSync(amisSdkPathPrefix + 'node_modules/amis/package.json', 'utf8');
    const packageData = JSON.parse(data);
    const version = packageData.version;
    return version;
  } catch (err) {
    console.error('无法读取或解析 package.json 文件:', err);
    process.exit();
  }
}