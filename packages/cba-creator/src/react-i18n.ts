import {join} from 'path';
import {getPackageLatestVersion} from '@baidu/cba-utils';
import type {IUserConfig} from '@baidu/cba-core';

import {
  IApi,
  ProjectType
} from './types';

export default (api: IApi) => {
  api.describe({
    key: 'cba-creator:react-i18n',
    enableBy: () => {
      return api.appData.projectType === ProjectType.reactProject;
    }
  });

  api.addPrompts(() => {
    return [
      {
        type: 'list',
        message: '是否开启国际化',
        name: 'enableI18n',
        default: false,
        choices: [
          {
            name: '不开启国际化',
            value: false
          },
          {
            name: '开启国际化',
            value: true
          }
        ]
      }
    ];
  });

  api.modifyFile(async() => {
    if (!api.appData.enableI18n) {
      return;
    }
  });

  api.modifyPkgJson(async (pkg) => {
    if (api.appData.enableI18n) {
      const toolkitVersion = await getPackageLatestVersion('@baidu/bce-react-toolkit');
      const bdI18nVersion = await getPackageLatestVersion('@baiducloud/i18n');
      const i18nVersion = await getPackageLatestVersion('@baidu/cba-i18n');
      pkg.dependencies['@baidu/bce-react-toolkit'] = `^${toolkitVersion}`;
      pkg.dependencies['@baiducloud/i18n'] = `^${bdI18nVersion}`;
      pkg.devDependencies['@baidu/cba-i18n'] = `${i18nVersion}`;
      pkg.scripts['i18n:extract'] = 'npx cba-cli i18n:extract';
      pkg.scripts['i18n:upload'] = 'npx cba-cli i18n:upload';
    }
    return pkg;
  });

  api.modifyConfig(async (config: IUserConfig) => {
    if (api.appData.enableI18n) {
      config.presets?.push('@baidu/cba-i18n');
      config.i18n = {
        enabled: true,
        independent: true
      };
    }
    return config;
  });

};
