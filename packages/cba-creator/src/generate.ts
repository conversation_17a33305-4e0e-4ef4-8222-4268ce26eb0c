import {logger} from '@baidu/cba-utils';
import {existsSync, ensureDirSync} from 'fs-extra';
import yargsParser from 'yargs-parser';
import inquirer from 'inquirer';
import capitalize from 'lodash/capitalize';
import renderTemplate from './utils/renderTemplate';

import {IApi} from './types';

export default (api: IApi) => {
  api.describe({
    key: 'generate'
  });

  api.registerCommand({
    name: 'generate',
    alias: 'g',
    description: 'generate files from template',
    fn: async ({args}: yargsParser.Arguments) => {
      generate(api, args);
    }
  });
};

async function generate(api: IApi, args: yargsParser.Arguments) {
  const generateArgs = await handleArgs(api, args);
  if (!generateArgs) {
    return;
  }
  const {template, fallback, sourceTemplate, target} = generateArgs;
  if (sourceTemplate && target && !fallback) {
    // 自定义模板
    await handleCustomTemplateRender(generateArgs);

    await api.applyPlugins({
      key: 'onGenerateFilesEnd',
      args
    });
    return;
  }

  const generate = api.service.generators[template];
  if (!generate) {
    logger.error(`${template}生成器不可用`);
    process.exit(1);
  }
  (await generate.fn) && generate.fn(generateArgs);

  await api.applyPlugins({
    key: 'onGenerateFilesEnd',
    args: generateArgs
  });
}

const handleArgs = async (api: IApi, args: yargsParser.Arguments): Promise<GenerateArgs | undefined> => {
  let {template, fileName, name, eject, fallback, fileMode, sourceTemplate, target} = args;

  if (!template) {
    const generators = Object.entries(api.service.generators).map(item => ({
      name: `${item[0]}：${item[1].description}`,
      value: item[0]
    }));
    if (!generators.length) {
      logger.warn('无可用生成器');
      return;
    }
    const answer = await inquirer.prompt({
      type: 'list',
      message: '请选择模板:',
      name: 'template',
      default: '',
      choices: generators
    });
    template = answer.template;
  }

  const isCustomTemplate = sourceTemplate && target && !fallback;

  if (!fileName && !isCustomTemplate) {
    const answer = await inquirer.prompt({
      type: 'input',
      message: `请输入${fileMode ? '文件' : '文件夹'}名称:`,
      name: 'fileName',
      validate: value => {
        if (!value) {
          return '请输入文件名称';
        }
        return true;
      }
    });
    fileName = answer.fileName;
  }

  return {
    template,
    fileName,
    name: capitalize(name || fileName),
    eject,
    fallback,
    fileMode,
    sourceTemplate,
    target
  };
};

const handleCustomTemplateRender = async (args: GenerateArgs) => {
  try {
    const {sourceTemplate = '', target = '.'} = args;
    if (!existsSync(sourceTemplate)) {
      logger.error(`${sourceTemplate} 路径不存在`);
      process.exit(1);
    }
    ensureDirSync(target);
    await renderTemplate(sourceTemplate, target, args);
  } catch (err) {
    throw new Error('generate files failed');
  }
};
