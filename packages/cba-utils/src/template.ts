/**
 * 从模板库中下载模板
 * @param projectName
 * @param templateProject
 */
import axios from 'axios';
import shell from 'shelljs';
import logger from './logger';

export async function downloadTemplate(projectName?: string, templateProject = 'console-react') {
  const token = 'cad1325d-00b7-4f12-869a-34b1d33970b1';
  const moduleName = 'baidu/baiducloud/bce-react-template';
  const url = `http://agile.baidu.com/api/agile/getReleaseInfoOutputUrl?module=${moduleName}`;
  try {
    projectName && shell.exec(`mkdir ${projectName}`);
    const res = await axios.get(url);
    const outputHttpUrl = res.data.outputHttpUrl;
    // download
    shell.exec(`
      curl -L -H IREPO-TOKEN:${token} ${outputHttpUrl} -o output.tar.gz
      tar -xzvf output.tar.gz
    `, {silent: true});
    shell.mv(`output/${templateProject}/{.,}*`, projectName ? `./${projectName}` : './');
    shell.rm('-rf', 'output.tar.gz');
    shell.rm('-rf', 'output');
  } catch (err) {
    logger.error('init react project failed');
    process.exit(1);
  }
}
