import React, {lazy, Suspense, ReactNode} from 'react';
import {Loading} from 'acud';
import {BrowserRouter, useRoutes} from 'react-router-dom';

const Home = lazy(() => import(/* webpackChunkName: "Home" */ '@/pages/home'));

const routes = [
  {
    path: '/home',
    element: <Home />
  },
  {
    path: '/*',
    element: <Home />
  }
];

export default function Router() {
  return <Suspense fallback={<Loading />}>{useRoutes(routes)}</Suspense>;
}
