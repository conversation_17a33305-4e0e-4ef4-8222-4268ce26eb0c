/// <reference types="react" />
export interface IRequestOptions {
  url: string; // 接口地址
  method?: 'get' | 'post' | 'put' | 'delete'; //请求方法
  data?: object, // 请求数据
  responseType?: string;
  config?: object; // 其他配置
  headers?: object; // 请求头
}
export declare function Provider(props: any): JSX.Element;
export declare function Router(props: any): JSX.Element;
export declare function amisRequest(options: IRequestOptions, proxy?: {
  path: string;
  target: string;
}): any;
export declare function pagemakerProxy(proxyPathPrefix: string, targetOrigin: string): string;
