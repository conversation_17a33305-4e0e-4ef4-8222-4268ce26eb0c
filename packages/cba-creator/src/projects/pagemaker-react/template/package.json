{"name": "console-react-demo", "version": "1.0.0", "description": "", "main": "src/App.tsx", "scripts": {"dev": "npx cba-cli dev", "dev:local": "npx cba-cli dev --forceLocal", "build": "npx cba-cli build"}, "author": "author", "license": "MIT", "dependencies": {"mobx": "^4.5.0", "mobx-react": "^6.3.1", "react-router-dom": "^6.14.1"}, "devDependencies": {"@baidu/cba-cli": "^1.0.1-beta.1", "@baidu/cba-preset-pagemaker-react": "^1.0.1-beta.1", "@babel/core": "^7.23.9", "@babel/preset-env": "^7.23.9", "@babel/preset-react": "^7.23.3", "@babel/preset-typescript": "^7.23.3", "@babel/plugin-transform-runtime": "^7.23.9"}}