declare interface Window {
  $framework: any;
  appPublicPath?: string;
  require: any;
}

declare module '*.less' {
  const resource: {[key: string]: string};
  export = resource;
}

declare module '*.svg' {
  import React from 'react';
  const svg: React.FC<{
    className?: any;
  }>;
  export default svg;
}

declare module '*.png';

declare let APP_TITLE: string;
declare const APP_ENABLE_I18N: boolean;
declare const APP_ENABLE_INDEPENDENT_I18N: boolean;
declare const APP_ALLOWED_LANGUAGE_TYPES: any[];
declare const APP_IS_EMBED_MODE: boolean;

declare let __webpack_public_path__: string;
