import {omit} from 'lodash';
import React from 'react';
import {I18nextProvider, I18nextProviderProps} from 'react-i18next';

import {I18nUtil} from '../utils';

interface I18nProviderProps extends I18nextProviderProps {
  i18nUtil: I18nUtil;
  children?: React.ReactNode;
}

export const I18nProvider: React.FC<I18nProviderProps> = (props) => {
  const {i18nUtil, children} = props;
  if (i18nUtil.isZhCNLanguage()) {
    return children;
  }

  return (
    <I18nextProvider {...omit(props, ['i18nUtil', 'children'])}>
      {children}
    </I18nextProvider>
  );
};
