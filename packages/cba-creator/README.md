
# 插件开发指南 

## 介绍
  本工程基于插件机制，可通过开发插件的方式扩展所需要的功能。

## 插件核心API

**API插件提供上下文数据，可通过api.appData获取**

| 属性 | 说明 |
| ------------ | ------------------- |
| projectName| 项目名称 |
| projectType | 项目类型 |
| 其它 | 其它通过addPrompts添加的prompt答案 |


**API插件提供核心的方法，可通过api.xxx的方式调用，具体如下：**

```javascript
  /**
   * 生成项目模版
   * source 模板的绝对路径
   * target 生成文件的目标绝对路径
   */
  renderTemplate(source: string, target: string);
```

```javascript
  /**
   * 移除模板文件
   * target 要移除的文件或文件夹绝对路径
   */
  removeTemplate(target: string);
```

```javascript
  /**
   * 添加项目类型的钩子函数
   * 回调函数， 返回project
   */
  addProject(fn: () => project);
```

```javascript
  /**
   * 添加额外Prompt的钩子函数
   * 回调函数， 返回问题列表
   */
  addPrompts(fn: () => Question[]);
```

```javascript
  /**
   * 添加工程文件钩子函数
   * 回调函数：在此函数中可以决定添加哪些文件到生成的项目中
   */
  addFileFromTemplate(fn: (answers: IPromptAnswers) => void);
```

```javascript
  /**
   * 修改普通文件的钩子函数
   * 回调函数：修改一下已经生成的项目文件
   */
  modifyFile(fn: (answers: IPromptAnswers) => void);
```

```javascript
  /**
   * 修改bce-config文件配置的钩子函数
   * 回调函数：可以获取到上下文配置，修改后返回。
   */
  modifyConfig(fn: (userConfig: IUserConfig) => IUserConfig);
```

```javascript
  /**
   * 修改package.json文件的钩子函数
   * 回调函数：可以获取到上下文packageJson对象，修改后返回。
   */
  modifyPkgJson(fn: (pkg: Record<string, any>) => Record<string, any>);
```


## 模板文件
- 模板文件需要统一放在template文件夹中
- 模板文件层级结构需和目标项目的文件层级结构保持一致
- 有些文件需要根据prompt动态生成，这就需要将文件后缀按如下规则处理：

    | 后缀 | 说明 |
    | ------------ | ------------------- |
    | .tpl| 有动态数据的需要在目标文件名后缀基础上添加.tpl，如：index.tsx -> index.tsx.tpl |
    | .data.js | 有动态数据的需要在目标文件名后缀基础上添加.data.js，提供处理槽位数据函数；如： index.tsx -> index.tsx.data.js|

- ⚠️ 注意 由于.npmrc和.gitignore文件发包后会丢失，需要做处理，文件名称需要修改为：
    
    .npmrc -> _npmrc

    .gitignore -> _gitignore

## 插件注册

- 将插件在src/index下通过require.resolve方法进行注册。
- 插件内部通过api.describe的enableBy方法决定是否执行该插件。

