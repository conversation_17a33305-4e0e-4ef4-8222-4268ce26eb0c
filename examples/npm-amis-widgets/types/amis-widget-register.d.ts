declare module 'register-amis-renderer' {
  export interface AmisRendererOption {
    /**
     * 渲染器名称
     * 备注：渲染过程中用于查找对应的渲染器
     */
    type: string;

    /**
     * 要注册的amis渲染器类型:
     * amis普通渲染器 - renderer
     * amis表单渲染器 - formitem
     * amis表单控件渲染器 - options
     * 备注：默认为amis普通渲染器
     */
    usage: string;

    /**
     * 自定义组件权重
     * 备注：值越低越优先命中
     */
    weight?: number;
    framework: 'react';
    storeType?: any;
  }
  export default function (
    newRenderer: any,
    rendererOption: string | AmisRendererOption
  ): void;
}

declare module 'register-amis-editor-plugin' {
  /**
   * 自定义editor插件配置项
   */
  export interface PluginOption {
    /**
     * 关联的渲染器
     * 备注：可以关联当前的自定义组件，也可以关联平台预置组件和其他自定义组件
     */
    rendererName: string;

    /**
     * 关联的渲染器
     * 备注：type 和 rendererName 为同一个字段，rendererName 和 type 不能同时存在
     * 目的：兼容用户的错误写法
     */
    type: string;

    /**
     * 自定义组件名称
     * 在「页面设计器」自定义组件面板中显示
     */
    name: string;

    /**
     * 自定义组件描述
     * hover自定义组件时展示
     */
    description?: string;

    /**
     * 自定义组件分类
     * 指定当前自定义插件在「页面设计器」自定义组件面板中哪个分类下展示
     */
    tags?: string | Array<string>;

    /**
     * 自定义组件排序
     * 指定当前自定义插件在「页面设计器」自定义组件面板中的展示次序
     */
    order?: number;

    /**
     * 自定义组件icon
     */
    icon?: string;

    /**
     * 属性配置面板Title
     */
    panelTitle?: string;

    /**
     * 自定义组件显隐
     * 备注：设置为true时则不展示
     */
    disabledRendererPlugin?: boolean;
  }

  export default function (
    _EditorPlugin: any,
    pluginOption?: PluginOption
  ): void;
}
