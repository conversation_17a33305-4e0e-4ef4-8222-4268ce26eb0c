import path from 'path';
import fs from 'fs';
import {execSync} from 'child_process';
import tmp from 'tmp';
import ejs from 'ejs';
import fse from 'fs-extra';
import {secureReadJsonFile, logger, isJSON} from '@baidu/cba-utils';
import {Config} from '@baidu/cba-core';
import {IUserConfig} from '@baidu/cba-preset';
import extractSan from './extractSan';

interface ExtractParams {
  config: Config;
}

const checkFilePath = (parentDir: string, currentPath: string, dirType: string) => {
  if (!currentPath.startsWith(parentDir)) {
    logger.error(
      `The ${dirType} path for i18n is incorrect. Please specify a subdirectory under the current working directory as the ${dirType} path for i18n.`
    );
    return false;
  }
  return true;
};

export default function extract({config}: ExtractParams) {
  const {cwd} = config;
  const {i18n} = config.getUserConfig() as IUserConfig;
  let {enabled, independent, supportedLanguages = ['en-us'], output = './public/locales', sdpDir} = i18n || {};

  supportedLanguages = supportedLanguages.map((item: string) => item.toLowerCase());

  if (!enabled) {
    logger.error('Please enable i18n configuration in app config file.');
    return;
  }

  const i18nParserFile = tmp.fileSync({
    postfix: '.js'
  });

  const inputPath = path.resolve(cwd, './src/**/*.{js,jsx,ts,tsx}');

  const outputDir = path.isAbsolute(output) ? output : path.resolve(cwd, output);

  if (!checkFilePath(cwd, outputDir, 'output')) {
    return;
  }

  const outputFilePath = path.resolve(outputDir, 'corpus.json');

  fse.ensureFileSync(outputFilePath);

  // 文件没有内容时，先进行初始化，防止首次提取时报错
  const corpusContent = fs.readFileSync(outputFilePath, 'utf-8');
  if (!isJSON(corpusContent)) {
    fs.writeFileSync(outputFilePath, '{}', 'utf-8');
  }

  const fileData = ejs.render(
    `const path = require('path');
     const cwd = process.cwd();
     module.exports = {
       input: ['<%= inputPath %>'],
       output: '<%= outputFilePath %>',
       options: {}
     };`,
    {
      inputPath,
      outputFilePath
    }
  );

  fs.writeFileSync(i18nParserFile.name, fileData);

  execSync(`npx i18next-parser -c ${i18nParserFile.name}`, {
    cwd,
    encoding: 'utf8',
    stdio: 'inherit'
  });

  i18nParserFile.removeCallback();

  if (sdpDir) {
    if (typeof sdpDir === 'string') {
      const sdpAbsoluteDir = path.isAbsolute(sdpDir) ? sdpDir : path.resolve(cwd, sdpDir);
      if (checkFilePath(cwd, sdpAbsoluteDir, 'sdpDir')) {
        extractSan(sdpAbsoluteDir, path.resolve(outputDir, 'po'));
      }
    }
    if (Array.isArray(sdpDir)) {
      sdpDir.forEach(item => {
        const sdpAbsoluteDir = path.isAbsolute(item) ? item : path.resolve(cwd, item);
        if (checkFilePath(cwd, sdpAbsoluteDir, 'sdpDir')) {
          extractSan(sdpAbsoluteDir, path.resolve(outputDir, 'po'));
        }
      });
    }
  }

  const {err, isEmpty} = secureReadJsonFile(outputFilePath);
  if (!sdpDir && (err?.name === 'Invalid' || isEmpty)) {
    fse.removeSync(outputFilePath);
    logger.error('\nThere is currently no corpus content to be translated.');
    return;
  } else if (err) {
    fse.removeSync(outputFilePath);
    logger.error(err.message);
    return;
  }
  // SDP混合工程不支持维护独立语料库
  if (!sdpDir && independent) {
    const corpus = JSON.parse(fs.readFileSync(outputFilePath, 'utf-8'));
    supportedLanguages.forEach((type: string) => {
      const targetPath = path.resolve(outputDir, `${type}.json`);
      if (fs.existsSync(targetPath)) {
        const translatedData = JSON.parse(fs.readFileSync(targetPath, 'utf-8'));
        const dataToTranslate: Record<string, string> = {};
        Object.keys(corpus).forEach(key => {
          if (!translatedData[key] && translatedData[key] !== '') {
            dataToTranslate[key] = '';
          }
        });
        if (!Object.keys(dataToTranslate).length) {
          logger.info('\nThere is no new corpus content to be translated.');
          return;
        }
        fs.writeFileSync(
          targetPath,
          JSON.stringify(
            Object.fromEntries([...Object.entries(translatedData), ...Object.entries(dataToTranslate)]),
            undefined,
            2
          ),
          'utf-8'
        );
        logger.info(`The i18n language file ${path.relative(cwd, targetPath)} has been successfully updated.`);
        return;
      }
      fse.ensureFileSync(targetPath);
      logger.info(`The i18n language file ${path.relative(cwd, targetPath)} has been successfully created.`);
      fs.writeFileSync(targetPath, JSON.stringify(corpus, undefined, 2));
      logger.info(`The i18n language file ${path.relative(cwd, targetPath)} has been successfully written.`);
    });

    fse.removeSync(outputFilePath);
  }
}
