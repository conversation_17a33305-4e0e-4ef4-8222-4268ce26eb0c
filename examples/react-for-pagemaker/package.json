{"name": "console-react-demo", "version": "1.0.0", "description": "百度智能云 | ", "main": "src/App.tsx", "scripts": {"dev": "cba-cli dev", "dev:local": "cba-cli dev --forceLocal", "build": "cba-cli build", "preview": "cba-cli preview", "version": "cba-cli version"}, "author": "author", "license": "MIT", "dependencies": {"mobx": "^4.5.0", "mobx-react": "^6.3.1", "react-router-dom": "^6.14.1"}, "devDependencies": {"@baidu/cba-cli": "workspace:*", "@baidu/cba-preset-pagemaker-react": "workspace:*", "@babel/preset-env": "^7.23.9", "@babel/preset-react": "^7.23.3", "@babel/preset-typescript": "^7.23.3", "@babel/plugin-transform-runtime": "^7.23.9"}}