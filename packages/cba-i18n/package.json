{"name": "@baidu/cba-i18n", "version": "1.2.8-beta.14", "description": "BaiduCloud Console-FE CLI i18n utils", "main": "lib/index.js", "scripts": {"build": "cba-scripts build", "dev": "cba-scripts dev"}, "files": ["lib"], "author": "<EMAIL>", "license": "MIT", "dependencies": {"@babel/core": "^7.23.9", "@baidu/cba-core": "workspace:*", "@baidu/cba-preset": "workspace:*", "@baidu/cba-utils": "workspace:*", "@baiducloud/i18n": "1.0.0-rc.29", "chalk": "^2.4.2", "ejs": "^3.1.9", "fs-extra": "^11.2.0", "fs-walk": "^0.0.2", "i18next-parser": "9.0.2", "json2po": "^1.0.5", "tmp": "^0.2.1"}, "devDependencies": {"@types/ejs": "^3.1.5", "@types/fs-extra": "^11.0.4", "@types/tmp": "^0.2.6"}}