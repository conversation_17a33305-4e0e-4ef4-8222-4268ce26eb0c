import React from 'react';
import {Button, Tag} from 'acud';
import RefreshIcon from '../../assets/svg/refresh.svg';
import './index.less';

const PageHome = (props: any) => {
  return (
    <div className="home-page">
      <div className="home-content">
        <div>
          <h1>create-cba-app React 应用2</h1>
        </div>
        <h3>易学易用，开箱即用，适用场景丰富的脚手架。</h3>
        <hr />
        <div style={{textAlign: 'left'}}>
          <h4>加载svg</h4>
          <br />
          <div style={{display: 'flex', alignItems: 'center'}}>
            URL加载svg：<div className="refresh"></div>
          </div>
          <div style={{display: 'flex', alignItems: 'center'}}>
            React组件加载svg：
            <RefreshIcon />
          </div>
        </div>
        <hr />
        <div>
          <a
            href="https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/tQvrZcAEPS/4yidKCir54/Yl586IZSCNxzLl"
            target="_blank"
          >
            <Button type="primary" size="large">
              使用文档
            </Button>
          </a>
        </div>
      </div>
    </div>
  );
};

export default PageHome;
