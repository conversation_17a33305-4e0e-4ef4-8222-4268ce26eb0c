{"compilerOptions": {"moduleResolution": "Node", "baseUrl": "./", "paths": {"@/*": ["src/*"], "react": ["./node_modules/@types/react"]}, "target": "ES5", "lib": ["DOM", "ES6", "ES2017", "ESNext"], "module": "ESNext", "jsx": "react", "strict": true, "esModuleInterop": true, "allowUmdGlobalAccess": true, "noImplicitAny": false, "experimentalDecorators": true}, "include": ["src/**/*", "src/types/*.d.ts"]}