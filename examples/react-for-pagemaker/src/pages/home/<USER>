import React from 'react';
import {Button} from 'amis';
import {Link} from 'react-router-dom';
import './index.scss';
import {inject, observer} from 'mobx-react';

import amisLogo from '../../assets/imgs/logo_amis.jpg';
import reactLogo from '../../assets/imgs/logo_react.svg';

const PageHome = (props: any) => {
  return (
    <div className="home-page">
      <div className="home-content">
        <div className="logo-wrap">
          <img src={reactLogo} />
          <img src={amisLogo} />
        </div>
        <div>
          <h1>React + Amis</h1>
        </div>
        <h3>易学易用，开箱即用，适用场景丰富的脚手架。</h3>
        <hr />
        <div>
          <Link to="/react" className="home-btn">
            <Button size="lg" level="enhance">
              React示例页
            </Button>
          </Link>
          <Link to="/amis">
            <Button size="lg" level="enhance">
              Amis示例页
            </Button>
          </Link>
        </div>
        <div
          className="touch-me"
          onClick={() => {
            props?.demoStore?.setNumber();
          }}
        >
          <div className="label">Touch Me</div>
          <div className="count">{props?.demoStore.number}</div>
        </div>
      </div>
    </div>
  );
};

export default inject('demoStore')(observer(PageHome));
