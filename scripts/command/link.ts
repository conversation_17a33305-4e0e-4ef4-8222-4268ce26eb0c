import inquirer from 'inquirer';
import {getPkgs} from '../utils';
import logger from '../utils/logger';
import 'zx/globals';

(async () => {
  const pkgs = getPkgs();
  const {linkDir} = await inquirer.prompt([
    {
      type: 'input',
      message: '请输入需要link的项目路径',
      name: 'linkDir'
    }
  ]);

  await Promise.all(
    pkgs.map(async pkg => {
      await $`cd packages/${pkg} && pnpm link --dir ${linkDir}`;
    })
  );
  logger.info('link sucess');
})();
