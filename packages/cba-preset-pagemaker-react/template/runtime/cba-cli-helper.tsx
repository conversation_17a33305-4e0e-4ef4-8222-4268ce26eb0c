import React from 'react';
import featureList from '/flags';
import HiddenRouter from './hidden-router';
import RenderProvider from './hidden-provider';

export {amisRequest, pagemakerProxy} from '/hidden-request';

export const Router = (props: any) => {
  const {app} = props;
  const routerPrefix = app?.isConsoleApp ? '' : props.routerPrefix;
  return <HiddenRouter routerPrefix={routerPrefix} routerMode={ROUTER_MODE} routes={props.routes || []} />;
};

export const Provider = RenderProvider;

export const useFlags = () => {
  return featureList;
};
