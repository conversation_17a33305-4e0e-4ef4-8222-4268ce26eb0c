<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <title></title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta name="viewport" content="width=device-width,minimum-scale=1.0,maximum-scale=1.0" />
  </head>

  <body>
    <div id="root-app-container"></div>
    <script src="/ecom/esl/2.2.0-rc.3/esl.js"></script>
    <script src="/fe-framework/loadjs.js"></script>
    <script>
      (function () {
        function boot() {
          const isPrivateStandard330 = '<%= htmlWebpackPlugin.options.isPrivateStandard330 %>' === 'true';
          const flags = '<%= htmlWebpackPlugin.options.flags %>'.split(',');
          if (!Array.isArray) {
            Array.isArray = function(arg) {
              return Object.prototype.toString.call(arg) === '[object Array]';
            };
          }

          function loadCss(url) {
            var cssLink = document.createElement('link');
            cssLink.setAttribute('rel', 'stylesheet');
            cssLink.setAttribute('href', url);
            document.head.appendChild(cssLink);
          }

          function loadJS(url) {
            var scriptNode = document.createElement('script');
            scriptNode.setAttribute('type', 'text/javascript');
            scriptNode.setAttribute('src', url);
            scriptNode.onload = function () {
              this._loadStatus = '1';
            };
            document.head.appendChild(scriptNode);
            return scriptNode;
          }

          const jsList = ['/static/js/main.js'];
          const cssList = ['/main.css'];
          let scriptNodeList = [];

          function concatUrl(filePath) {
            var baseUrl = location.origin;
            var appName = '<%= htmlWebpackPlugin.options.appName %>';

            window.appPublicPath = `${baseUrl}/${appName}/`;
            return `${baseUrl}/${appName}${filePath}`;
          }

          function loadRes() {
            cssList.forEach(function (filePath) {
              loadCss(concatUrl(filePath));
            });
            scriptNodeList = jsList.map(function (filePath) {
              return loadJS(concatUrl(filePath));
            });

            return scriptNodeList;
          }

          function waitLoadJS(list, callback) {
            var countOfAction = list && list.length !== 0;

            if (Array.isArray(list)) {
              list.forEach(function (scriptNode) {
                if (scriptNode._loadStatus === '1') {
                  if (--countOfAction === 0) {
                    callback();
                  }
                } else {
                  scriptNode.onload = function () {
                    if (--countOfAction === 0) {
                      callback();
                    }
                  };
                }
              });
            }
          }

          const scriptList = loadRes();

          require(['framework'], function (framework) {
            let flagsObj;
            window.$framework = framework;
            function requestFlag(flag) {
              function checkStatus(response) {
                if (response.status >= 200 && response.status < 300) {
                  return response
                } else {
                  var error = new Error(response.statusText)
                  error.response = response
                  throw error
                }
              }

              function parseJSON(response) {
                return response.json()
              }
              return fetch(`/v1/settings/acl/features?productName=${flag}`).then(checkStatus)
                .then(parseJSON);
            }
            try {
              if (isPrivateStandard330) {
                const flagsPromise = flags.map(flagName => requestFlag(flagName));
                Promise.all(flagsPromise).then(res => {
                  const flagsObj = {};
                  res.forEach(flagList => flagList.forEach(flag => flagsObj[flag] = true));
                  console.log('功能清单获取成功🏅', flagsObj);
                  sessionStorage.setItem('_flags', JSON.stringify(flagsObj));
                }).catch(error => {
                  console.error('功能清单获取失败☹️', error);
                });
              } else {
                require.config({flag: './flag'});
                require(['flag'], function (flag) {
                  let flagsObj;

                  try {
                    flagsObj = Array.isArray(flag)
                      ? flag.reduce((cur, next) => {
                          cur[next] = true;
                          return cur;
                        }, {})
                      : {};
                  } catch (error) {}

                  sessionStorage.setItem('_flags', JSON.stringify(flagsObj));
                });
              }
            } catch (error) {
              console.error(error);
            } finally {
              framework.boot().then(function (initData) {
              waitLoadJS(scriptList, function () {
                require(['<%= htmlWebpackPlugin.options.moduleName %>'], function (app) {
                  app.bootstrap(initData);
                  });
                });
              });
            }

          });
        }
        if (/(MSIE|Trident)/gi.test(navigator.userAgent)) {
          var script = document.createElement('script');
          var appName = '<%= htmlWebpackPlugin.options.appName %>';
          script.type = 'text/javascript';
          script.async = true;
          script.src = `/npm/core-js-bundle@3.6.2/minified.js`;
          script.onload = boot;
          document.body.appendChild(script);
        } else {
          window.onload = boot;
        }
      })();
    </script>
  </body>
</html>
