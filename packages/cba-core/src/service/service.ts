/**
 * @file 核心服务
 * <AUTHOR> (<EMAIL>)
 */
import {join} from 'path';
import assert from 'assert';
import yargsParser from 'yargs-parser';
import {AsyncSeriesWaterfallHook} from 'tapable';
import {logger, ILogger} from '@baidu/cba-utils';
import {ApplyPluginsType, Env, IUserConfig, ServiceStage} from '../types';
import {Plugin} from './plugin';
import {PluginAPI} from './pluginAPI';
import {loadEnv} from './env';
import {Config} from './config';
import {Command} from './command';
import {Generate} from './generate';
import {Hook} from './hooks';

/**
 * Service 配置项
 */
interface IOpts {
  /**
   * 当前工作目录
   */
  cwd: string;
  /**
   * 当前环境 'development' | 'production'
   */
  env: Env;
  /**
   * 插件列表
   */
  plugins?: string[];
  /**
   * 预设列表
   */
  presets?: string[];
  /**
   * 默认配置文件路径（相对路径）
   */
  defaultConfigFile?: string;
}

export enum PluginType {
  preset = 'preset',
  plugin = 'plugin'
}

export class Service {
  cwd: string;
  env: Env = Env.development;
  rawEnv: Record<string, string | undefined> = {};
  args: yargsParser.Arguments = {_: [], $0: ''};
  name: string = '';
  commands: Record<string, Command> = {}; // 命令
  plugins: Record<string, Plugin> = {}; // 插件
  generators: Record<string, Generate> = {}; // 生成器
  /**
   * 插件key到插件的映射
   */
  keyToPluginMap: Record<string, Plugin> = {};
  hooks: Record<string, Hook[]> = {};
  stage: ServiceStage = ServiceStage.uninitialized;
  pluginMethods: Record<string, {plugin: Plugin; fn: Function}> = {};
  pkg: {
    [key: string]: any;
    name?: string;
    version?: string;
    dependencies?: Record<string, string>;
    devDependencies?: Record<string, string>;
  } = {};
  pkgPath: string = '';
  userConfig: IUserConfig = {};
  configManager: Config | null = null;
  logger: ILogger;
  appData: Record<string, any> = {};

  private readonly opts: IOpts;

  constructor(opts: IOpts) {
    this.cwd = opts.cwd;
    this.env = opts.env;
    this.opts = opts;

    this.logger = logger;
  }

  /**
   * 执行cli命令
   * @param opts
   * @returns
   */
  async run(opts: {name: string; args?: any; additionalEnv?: Record<string, any>;}) {
    const {name, args = {}} = opts;
    args._ = args._ || [];
    if (args._[0] === name) args._.shift();
    this.args = args;
    this.name = name;

    // 初始化
    this.stage = ServiceStage.init;
    // 加载package.json
    const {pkg, pkgPath} = this.loadPkg();
    this.pkg = pkg;
    this.pkgPath = pkgPath || join(this.cwd, 'package.json');

    // 加载bce-config.js
    const configManager = new Config({
      cwd: this.cwd,
      args: this.args,
      pkg: this.pkg,
      defaultConfigFile: this.opts.defaultConfigFile
    });
    this.configManager = configManager;
    this.userConfig = configManager.getUserConfig();

    // 加载环境变量文件
    this.rawEnv = loadEnv({
      cwd: this.cwd,
      env: this.env,
      envFile: '.env',
      additionalEnv: opts.additionalEnv
    });

    const {plugins, presets} = Plugin.getPluginsAndPresets({
      cwd: this.cwd,
      plugins: [...(this.opts.plugins || []), ...(this.userConfig?.plugins || [])],
      presets: [
        require.resolve('./servicePlugin'),
        ...(this.opts.presets || []),
        // 没有 presets 配置的时候，默认为  pagemaker-react 项目，用于兼容之前初始化 presets 配置
        ...(this.userConfig?.presets || [])
      ]
    });

    // 初始化预设
    this.stage = ServiceStage.initPresets;
    const presetPlugins: Plugin[] = [];
    while (presets.length) {
      await this.initPreset({
        preset: presets.shift()!,
        presets,
        plugins: presetPlugins
      });
    }
    plugins.unshift(...presetPlugins);

    // 初始化插件
    this.stage = ServiceStage.initPlugins;
    while (plugins.length) {
      await this.initPlugin({plugin: plugins.shift()!, plugins});
    }

    this.stage = ServiceStage.collectData;
    this.userConfig = await this.applyPlugins({
      key: 'modifyUserConfig',
      initialValue: this.userConfig
    });

    this.stage = ServiceStage.onStart;
    await this.applyPlugins({
      key: 'onStart'
    });

    // 执行命令
    this.stage = ServiceStage.runCommand;
    const command = this.commands[name];
    if (!command) {
      logger.error(`Invalid command 「${name}」, it's not registered.`);
      process.exit(1);
    }
    return await command.fn({args, config: configManager});
  }

  /**
   * 初始化预设
   */
  async initPreset(opts: {preset: Plugin; presets: Plugin[]; plugins: Plugin[]}) {
    const {presets, plugins} = await this.initPlugin({
      plugin: opts.preset,
      presets: opts.presets,
      plugins: opts.plugins
    });
    opts.presets.unshift(...(presets || []));
    opts.plugins.push(...(plugins || []));
  }

  /**
   * 执行插件通过register() 注册的 hooks
   * @param opts
   * @returns
   */
  async applyPlugins<T>(opts: {key: string; type?: ApplyPluginsType; initialValue?: any; args?: any}): Promise<any> {
    const hooks = this.hooks[opts.key] || [];
    let type = opts.type;

    if (!type) {
      if (opts.key.startsWith('on')) {
        type = ApplyPluginsType.event;
      } else if (opts.key.startsWith('modify')) {
        type = ApplyPluginsType.modify;
      } else if (opts.key.startsWith('add')) {
        type = ApplyPluginsType.add;
      } else {
        throw new Error(`Invalid applyPlugins key: ${opts.key}`);
      }
    }

    switch (type) {
      case ApplyPluginsType.add:
        assert(
          !('initialValue' in opts) || Array.isArray(opts.initialValue),
          `applyPlugins failed, opts.initialValue must be Array if opts.type is add.`
        );
        const adds = new AsyncSeriesWaterfallHook(['data']);
        for (const hook of hooks) {
          if (!this.isPluginEnable(hook)) continue;
          adds.tapPromise(
            {
              name: hook.plugin.key,
              stage: hook.stage || 0,
              before: hook.before
            },
            async (data: any) => {
              const items = await hook.fn(opts.args);
              return data.concat(items);
            }
          );
        }
        return adds.promise(opts.initialValue || []) as Promise<T>;
      case ApplyPluginsType.modify:
        const modifys = new AsyncSeriesWaterfallHook(['data']);
        for (const hook of hooks) {
          if (!this.isPluginEnable(hook)) continue;
          modifys.tapPromise(
            {
              name: hook.plugin.key,
              stage: hook.stage || 0,
              before: hook.before
            },
            async (data: any) => {
              const result = await hook.fn(data, opts.args);
              return result;
            }
          );
        }
        return modifys.promise(opts.initialValue) as Promise<T>;
      case ApplyPluginsType.event:
        const events = new AsyncSeriesWaterfallHook(['_']);
        for (const hook of hooks) {
          if (!this.isPluginEnable(hook)) continue;
          events.tapPromise(
            {
              name: hook.plugin.key,
              stage: hook.stage || 0,
              before: hook.before
            },
            async () => {
              await hook.fn(opts.args);
            }
          );
        }
        return events.promise(1) as Promise<T>;
      default:
        throw new Error(`Invalid applyPlugins type: ${opts.type}`);
    }
  }

  /**
   * 判断插件是否可用
   * @param hook Hook | string
   * @returns
   */
  isPluginEnable(hook: Hook | string) {
    let plugin: Plugin;
    if ((hook as Hook).plugin) {
      plugin = (hook as Hook).plugin;
    } else {
      plugin = this.keyToPluginMap[hook as string];
      if (!plugin) return false;
    }

    const {id, key, enableBy} = plugin;
    if (typeof enableBy === 'function') {
      return enableBy({
        userConfig: this.userConfig,
        env: this.env
      });
    }
    return enableBy;
  }

  /**
   * 初始化插件
   */
  async initPlugin(opts: {plugin: Plugin; presets?: Plugin[]; plugins: Plugin[]}) {
    this.plugins[opts.plugin.id] = opts.plugin;

    const pluginAPI = new PluginAPI({
      plugin: opts.plugin,
      service: this
    });

    pluginAPI.registerPresets = pluginAPI.registerPresets.bind(pluginAPI, opts.presets || []);

    pluginAPI.registerPlugins = pluginAPI.registerPlugins.bind(pluginAPI, opts.plugins || []);

    const proxyPluginAPI = PluginAPI.proxyPluginAPI({
      service: this,
      pluginAPI,
      serviceProps: [
        'args',
        'cwd',
        'pkg',
        'pkgPath',
        'name',
        'env',
        'stage',
        'userConfig',
        'logger',
        'rawEnv',
        'applyPlugins',
        'appData',
        'updateAppData'
      ],
      staticProps: {
        ApplyPluginsType,
        ServiceStage
      }
    });
    let ret = await opts.plugin.apply()(proxyPluginAPI);
    if (opts.plugin.type === 'plugin') {
      assert(!ret, `plugin should return nothing`);
    }

    assert(
      !this.keyToPluginMap[opts.plugin.key],
      `key ${opts.plugin.key} is already registered by ${this.keyToPluginMap[opts.plugin.key]?.path}, ${
        opts.plugin.type
      } from ${opts.plugin.path} register failed.`
    );
    this.keyToPluginMap[opts.plugin.key] = opts.plugin;

    if (ret?.presets) {
      ret.presets = ret.presets.map(
        (preset: string) =>
          new Plugin({
            path: preset,
            type: PluginType.preset,
            cwd: this.cwd
          })
      );
    }
    if (ret?.plugins) {
      ret.plugins = ret.plugins.map(
        (plugin: string) =>
          new Plugin({
            path: plugin,
            type: PluginType.plugin,
            cwd: this.cwd
          })
      );
    }
    return ret || {};
  }

  /**
   * 获取package.json文件信息
   * @returns
   */
  loadPkg() {
    let pkg: Record<string, string | Record<string, any>> = {};
    let pkgPath: string = '';
    try {
      pkg = require(join(this.cwd, 'package.json'));
      pkgPath = join(this.cwd, 'package.json');
    } catch (e) {
      if (this.cwd !== process.cwd()) {
        try {
          pkg = require(join(process.cwd(), 'package.json'));
          pkgPath = join(process.cwd(), 'package.json');
        } catch (_e) {}
      }
    }

    return {
      pkg,
      pkgPath: pkgPath || join(this.cwd, 'package.json')
    };
  }
  updateAppData(fn: (appData: Record<string, any>) => Record<string, any>) {
    this.appData = fn(this.appData);
  }
}
