import path from 'path';
import fs from 'fs';
import url, {type UrlObject} from 'url';
import {Configuration as WebpackConfig} from 'webpack';
import {IApi} from '@baidu/cba-preset';
import {getFlags, logger, mockHelper} from '@baidu/cba-utils';
import ReactRefreshWebpackPlugin from '@pmmmwh/react-refresh-webpack-plugin';
import HtmlWebpackPlugin from 'html-webpack-plugin';

const isEmbed = process.env.BUILD_TYPE === 'embed';
const isPrivate = process.env.BUILD_TYPE === 'private';
const CONSOLE_TYPE = process.env.CONSOLE_TYPE;

export default (api: IApi) => {
  api.describe({
    key: 'preset-console-react:webpack-dev',
    enableBy: () => api.env === 'development'
  });

  api.modifyWebpackConfig((webpackConfig: any, {merge}: any) => {
    const needMock = api.args?.mock;
    const userConfig = api.userConfig;
    const mixSdp = userConfig.mixSdp;
    const indexName = mixSdp ? (isPrivate ? 'private_react_sdp' : 'react_sdp') : isPrivate ? 'private_react' : 'react';

    const port = userConfig.port || 8899;
    const pathname = `/${userConfig.appName}/`;
    const moduleName = `${userConfig.appName}AppModule`;
    const proxyTarget = userConfig.proxyTarget;
    const proxyTargetUrl = url.parse(proxyTarget || '');

    const userWebpackConfig:
      | WebpackConfig
      | ((systemConfig: WebpackConfig, mergeFn: (webpackConfig: WebpackConfig) => WebpackConfig) => WebpackConfig) =
      userConfig?.webpack || {};
    const finalUserConfig =
      typeof userWebpackConfig === 'function'
        ? userWebpackConfig(webpackConfig, merge)
        : merge(webpackConfig, userWebpackConfig);

    // 将默认的作为兜底的代理规则
    const defalutProxy = {
      ...(finalUserConfig.devServer.proxy || {}),
      ...{
        '/api': {
          target: proxyTarget || 'http://qasandbox.bcetest.baidu.com:80/',
          changeOrigin: true,
          secure: false,
          onProxyReq(proxyReq: any) {
            // 某些接口会验证origin
            proxyReq.setHeader(
              'origin',
              proxyTarget
                ? `${proxyTargetUrl.protocol}//${proxyTargetUrl.hostname}`
                : 'https://qasandbox.bcetest.baidu.com'
            );
          }
        }
      }
    };
    let host = 'localhost';
    let proxy = {
      ...(webpackConfig.devServer?.proxy || {}),
      ...defalutProxy
    };

    if (proxyTarget) {
      const proxyUrl: UrlObject = url.parse(proxyTarget);
      host = `localhost.${proxyUrl.hostname}`;
    }

    if (needMock) {
      proxy = mockHelper(userConfig, proxy);
    }

    const templatePath = api.userConfig.htmlTemplate
      ? path.isAbsolute(api.userConfig.htmlTemplate)
        ? api.userConfig.htmlTemplate
        : path.join(api.cwd, api.userConfig.htmlTemplate)
      : path.resolve(__dirname, `../../template/${indexName}.html`);
    webpackConfig = merge(webpackConfig, {
      mode: 'development',
      devtool: 'eval-source-map',
      entry: './src/index.tsx',
      plugins: [
        new ReactRefreshWebpackPlugin(),
        new HtmlWebpackPlugin({
          moduleName: moduleName,
          appName: api.userConfig.appName,
          sdpDeps: JSON.stringify(api.userConfig.sdpDependencies || {}),
          template: templatePath,
          env: 'development',
          consoleType: CONSOLE_TYPE === 'xs-console' ? CONSOLE_TYPE : 'console',
          isPrivateStandard330: api.userConfig.isPrivateStandard330,
          flags: api.userConfig.flags
        })
      ],
      output: {
        filename: 'static/js/[name].js',
        chunkFilename: 'static/js/[name].chunk.js',
        assetModuleFilename: 'static/js/[hash:10][ext][query]',
        publicPath: pathname,
        libraryTarget: 'amd',
        library: `${userConfig.appName}AppModule`
      },
      devServer: {
        allowedHosts: 'all',
        open: isEmbed ? false : [pathname],
        host,
        historyApiFallback: !isEmbed,
        port,
        hot: true,
        compress: true,
        https: {
          key: fs.readFileSync(path.join(__dirname, '../../ssl/server.key')),
          cert: fs.readFileSync(path.join(__dirname, '../../ssl/server.crt'))
        },
        client: {
          progress: false,
          reconnect: 5,
          overlay: {
            runtimeErrors: (error: Error) => {
              if (error.message === 'ResizeObserver loop completed with undelivered notifications.') {
                return false;
              }
              return true;
            }
          }
        },
        proxy
      }
    });
    return webpackConfig;
  });

  api.onBeforeServerStart(async ({origin}: {origin: string}) => {
    const flagPath = path.join(api.cwd, './src/flags.ts');
    await getFlags(
      {
        templateId: api.userConfig.templateId,
        flags: api.userConfig.flags
      },
      flagPath
    );
    logger.info(`开发调试： ${origin}/${api.userConfig.appName}/`);
  });
};
