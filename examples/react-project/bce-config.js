/**
 * @file 项目配置
 */
const {defineConfig} = require('@baidu/cba-cli');

/**
 * 根据命令行参数中添加 --privateDeliver，调整构建配置
 * 例如下：可以根据构建参数设置不同的 appTitle
 * 亦或者：使用不同的构建预设， 实现公有云产品「可实现单产品私有化」打包
 * presets: [hasPrivateDeliver? '@baidu/cba-preset-react': '@baidu/cba-preset-console-react']
 * */
const hasPrivateDeliver = process.argv.includes('--privateDeliver');

module.exports = defineConfig({
  appName: 'reactProject',
  htmlTemplate: 'public/index.html', // 自定义 html 模板路径，不指定的话，默认使用根路径下的 index.html
  appTitle: hasPrivateDeliver ? '私有化前端工程' : '百度智能云前端工程',
  presets: ['@baidu/cba-preset-react'],
  port: 8889, // 添加自定义端口配置
  host: 'localhost' // 添加自定义 host 配置
});
