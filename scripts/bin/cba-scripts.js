#!/usr/bin/env node
const {join} = require('path');
const {existsSync} = require('fs');
const assert = require('assert');
const {sync} = require('cross-spawn');
const chalk = require('chalk');

const argv = process.argv.slice(2);
const [name, ...throughArgs] = argv;
const scriptsPath = join(__dirname, `../command/${name}.ts`);

assert(existsSync(scriptsPath), `Executed script does not exist: ${chalk.red(scriptsPath)}`);

console.log(chalk.cyan(`cba-scripts: ${name} \n`));
const scriptPathString = JSON.stringify(scriptsPath);

const spawn = sync('tsx', [scriptPathString, ...throughArgs], {
  env: process.env,
  cwd: process.cwd(),
  stdio: 'inherit',
  shell: true
});

if (spawn.status !== 0) {
  console.log(chalk.red(`cba-scripts: 「${name}」 execute failed`));
}
