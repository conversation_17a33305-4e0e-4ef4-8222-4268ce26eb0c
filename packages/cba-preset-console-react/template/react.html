<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <title>百度智能云</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <link rel="shortcut icon" href="https://bce.bdstatic.com/img/favicon.ico" type="image/x-icon" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <link rel="dns-prefetch" href="//code.bdstatic.com" />
    <link rel="dns-prefetch" href="//bce.bdstatic.com" />
  </head>

  <body>
    <div id="root-app-container"></div>
    <script src="//bce.bdstatic.com/lib/@baiducloud/bce-track/@latest/monitorLoader.js"></script>
    <script type="text/javascript" src="https://bce.bdstatic.com/ecom/esl/2.2.0-rc.3/esl.js"></script>
    <script
      type="text/javascript"
      src="https://bce.bdstatic.com/<%= htmlWebpackPlugin.options.consoleType %>/fe-framework/loadjs.js"
    ></script>
    <script type="text/javascript">
      window.ISXSCONSOLE = '<%= htmlWebpackPlugin.options.consoleType %>' === 'xs-console';
      if (/console\.vcp\.baidu\.com/.test(location.host)) {
        window.ISXSCONSOLE = true;
      }

      var isOnline =
        /^console\.bce\.baidu\.com/.test(window.location.hostname) ||
        /^console\.vcp\.baidu\.com/.test(window.location.hostname);
      const useAlphaRelease = '<%= htmlWebpackPlugin.options.useAlphaRelease %>';
      var appName = '<%= htmlWebpackPlugin.options.appName %>';
      var kEnv = '<%= htmlWebpackPlugin.options.env %>';
      var pagemakerAppName = window.location.pathname.replace(/^\//, '').replace(/\//g, '_').toUpperCase();
      sessionStorage.removeItem('PAGEMAKER_RELEASE_STATE_' + pagemakerAppName);
      var baseUrl = 'https://bce.bdstatic.com/console/static/';
      var versionConf = isOnline ? '<%= htmlWebpackPlugin.options.onlineConfigFile %>' : 'qasandbox-config';
      var jsList = ['/static/js/main.js'];
      var cssList = ['/main.css'];
      var scriptNodeList = null;
      var isQasandbox = /^qasandbox\.bcetest\.baidu\.com/.test(window.location.hostname);
      var kModuleName = '<%= htmlWebpackPlugin.options.moduleName %>';

      function loadCss(url) {
        var cssLink = document.createElement('link');
        cssLink.setAttribute('rel', 'stylesheet');
        cssLink.setAttribute('href', url);
        document.head.appendChild(cssLink);
      }

      function loadJS(url) {
        var scriptNode = document.createElement('script');
        scriptNode.setAttribute('type', 'text/javascript');
        scriptNode.setAttribute('src', url);
        scriptNode.onload = function () {
          this._loadStatus = '1';
        };
        document.head.appendChild(scriptNode);
        return scriptNode;
      }

      function getCookie(name) {
        var matches = String(document.cookie).match(new RegExp('(?:^|)' + name + '(?:(?:=([^;]*))|;|$)'));
        if (matches && matches[1]) {
          return matches[1];
        }
        return '';
      }
      function getRegion() {
        // 创建一个XMLHttpRequest对象
        if (XMLHttpRequest && Promise) {
          return new Promise((resolve, reject) => {
            var token = getCookie('bce-user-info') || '';
            var xhr = new XMLHttpRequest();
            xhr.timeout = 10 * 1000;
            xhr.open('POST', '/api/region/get', true);
            xhr.setRequestHeader('Content-Type', 'application/json; charset=UTF-8');
            xhr.setRequestHeader('csrftoken', token.replace(/"/g, ''));
            xhr.setRequestHeader('X-Region', 'bj');
            xhr.send();
            // 监听请求完成的事件
            xhr.onreadystatechange = function () {
              // 请求完成且响应已就绪
              if (xhr.readyState === 4 && xhr.status === 200) {
                var responseData = {};
                try {
                  responseData = JSON.parse(xhr.responseText);
                } finally {
                  if (responseData.success === 'true' || responseData.success === true) {
                    if (responseData.result && responseData.result.regionId) {
                      resolve(responseData.result.regionId);
                    }
                  } else {
                    resolve('');
                  }
                }
              }
            };
            xhr.onerror = () => resolve('');
            xhr.ontimeout = () => resolve('');
          });
        } else {
          return '';
        }
      }

      function waitLoadJS(scriptNodeList, callback) {
        var countOfAction = scriptNodeList.length;
        scriptNodeList.forEach(function (scriptNode) {
          if (scriptNode._loadStatus === '1') {
            if (--countOfAction === 0) {
              callback();
            }
          } else {
            scriptNode.onload = function () {
              if (--countOfAction === 0) {
                callback();
              }
            };
          }
        });
      }
      if (window.ISXSCONSOLE) {
        if (document.referrer !== '' && !/^https:\/\/console(-|\.)vcp\.baidu(-int)?\.com/.test(document.referrer)) {
          sessionStorage.setItem('XS_DOCUMENT_REFERRER', document.referrer);
        }
        window.XSREFERRER = sessionStorage.getItem(`XS_DOCUMENT_REFERRER`);
      }

      require.config({
        baseUrl: baseUrl
      });

      function loadAppRes(versions) {
        const appVersionMap = versions[appName];
        const alphaVersion = appVersionMap.alpha_version;
        const enableAlphaVersion = !!(useAlphaRelease === 'true' && alphaVersion);
        const defaultKeys = ['version', 'xs_version', 'path', enableAlphaVersion ? 'alpha_version' : undefined].filter(Boolean);
        const moduleKeys = Object.keys(appVersionMap);

        let releaseVersion = '';
        let specifyVersion = false;
        var grayVersion = sessionStorage.getItem('_' + appName + (window.ISXSCONSOLE ? '_xs' : '') + '_version_');

        if (grayVersion) {
          grayVersion = grayVersion.trim();
          console.warn(
            'The browser is currently in a gray environment, and the gray version number is %c ' + grayVersion,
            'color: red; font-weight: bold;'
          );
        }

        // 是否为某个region指定版本
        specifyVersion = !grayVersion && !!moduleKeys.some(key => defaultKeys.indexOf(key) === -1);
        // 默认版本
        releaseVersion = appVersionMap &&
          (window.ISXSCONSOLE
            ? appVersionMap.xs_version
            : (enableAlphaVersion
              ? alphaVersion
              : appVersionMap.version)
          );

        if (!grayVersion && !window.ISXSCONSOLE && !specifyVersion && enableAlphaVersion) {
          console.warn(
            'The browser is currently in a alpha test environment, and the alpha version number is ' + alphaVersion + '.'
          );
        }

        // 工具函数
        function concatUrl(filePath) {
          // 判断是否启用覆盖率打包沙盒测试
          var isCoverage = isQasandbox && localStorage.getItem('_USE_ISTANBUL_COV_') === appName;
          var coveragePrefix = isQasandbox && isCoverage ? '/coverage' : '';
          var targetVersion = grayVersion || releaseVersion;
          window.appPublicPath = baseUrl + appName + '/' + targetVersion + coveragePrefix + '/';
          return baseUrl + appName + '/' + targetVersion + coveragePrefix + filePath;
        }
        // 加载app资源
        function loadRes() {
          cssList.forEach(function (filePath) {
            loadCss(concatUrl(filePath));
          });
          scriptNodeList = jsList.map(function (filePath) {
            return loadJS(concatUrl(filePath));
          });
        }

        // 如果为某个region指定版本，则先获取region信息再加载app资源，否则直接加载app资源
        if (specifyVersion) {
          const regionDefer = getRegion();
          if (regionDefer && regionDefer.then) {
            return regionDefer
              .then(regionId => {
                const appConfig = versions[appName];
                let versionKey = 'version';
                if (window.ISXSCONSOLE) {
                  versionKey = 'xs_version';
                }
                // 尝试获取region指定版本，如果未指定则使用默认版本
                releaseVersion =
                  !!regionId && appConfig[regionId] && appConfig[regionId][versionKey]
                    ? appConfig[regionId][versionKey]
                    : appConfig[versionKey];
                loadRes();
                window.addEventListener('after_region_changed', eventData => {
                  if (eventData && eventData.detail && eventData.detail.newRegion) {
                    const data = eventData.detail;
                    const newRegion = data.newRegion || '';
                    const newVersion =
                      !!newRegion && appConfig[newRegion] && appConfig[newRegion].version
                        ? appConfig[newRegion].version
                        : appConfig.version;
                    if (newRegion === regionId || newVersion === releaseVersion) {
                      return;
                    }
                    location.reload();
                  }
                });
              })
              .catch(() => loadRes());
          } else {
            return loadRes();
          }
        } else {
          return loadRes();
        }
      }

      if (kEnv === 'development') {
        require.config({
          paths: {
            [kModuleName]: `${location.origin}${location.pathname}static/js/main`
          }
        });
        require(['framework'], function (framework) {
          window.$framework = framework;
          framework.boot().then(function (initData) {
            require([kModuleName], function (app) {
              app.bootstrap(initData);
            });
          });
        });
      } else {
        require([versionConf], function (conf) {
          const loadResult = loadAppRes(conf);
          require(['framework'], function (framework) {
            window.$framework = framework;
            framework.boot().then(function (initData) {
              if (loadResult && loadResult.then) {
                loadResult.then(() => {
                  waitLoadJS(scriptNodeList, function () {
                    require([kModuleName], function (app) {
                      app.bootstrap(initData);
                    });
                  });
                });
              } else {
                waitLoadJS(scriptNodeList, function () {
                  require([kModuleName], function (app) {
                    app.bootstrap(initData);
                  });
                });
              }
            });
          });
        });
      }
    </script>
  </body>
</html>
