import {join} from 'path';

import {
  IApi,
  ProjectType
} from '../../types';

export default (api: IApi) => {
  api.describe({
    key: 'cba-creator:console-react-init',
    enableBy: () => {
      return api.appData.projectType === ProjectType.consoleReact;
    }
  });

  api.addFileFromTemplate(async () => {
    const source = join(__dirname, 'template');
    const target = join(api.cwd, api.appData.projectName);
    await api.renderTemplate(source, target, api.appData);
  });

};
