import {join} from 'path';

import {
  IApi,
  ProjectType
} from '../../types';

export default (api: IApi) => {
  api.describe({
    key: 'cba-creator:console-react-embed-init',
    enableBy: () => {
      return api.appData.projectType === ProjectType.consoleReactEmbed;
    }
  });

  api.addFileFromTemplate(async () => {
    const baseSource = join(__dirname, '../console-react/template');
    const baseTarget = join(api.cwd, api.appData.projectName);
    await api.renderTemplate(baseSource, baseTarget, api.appData);

    const source = join(__dirname, 'template');
    const target = join(api.cwd, api.appData.projectName);
    await api.renderTemplate(source, target, api.appData);
  });

  api.modifyFile(async () => {
    const removedFiles = [
      'src/App.tsx',
      'src/index.tsx',
      'src/pages/index.tsx'
    ];
    removedFiles.forEach(async (filePath) => {
      await api.removeTemplate(join(api.cwd, api.appData.projectName, filePath));
    });
  });

};
