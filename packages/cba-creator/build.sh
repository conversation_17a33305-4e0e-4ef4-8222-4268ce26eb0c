#!/usr/bin/env bash
set -e

echo "node $(node -v)"
echo "npm $(npm -v)"

source_root="src"  # 源文件夹路径
target_root="lib"  # 目标文件夹路径

npx cba-scripts $1

# 遍历源文件夹
find "$source_root" -type d -name "template" | while read -r source_folder; do
    # 拼接目标文件夹路径
    target_folder="${source_folder/src/lib}"
    # 复制子文件夹到目标文件夹
    if [ -d $source_folder ]; then
      # 移除掉旧文件
      rm -rf $target_folder
      cp -r "$source_folder" "$target_folder"
    fi
done



