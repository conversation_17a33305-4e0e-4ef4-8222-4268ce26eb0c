import {existsSync} from 'fs';
import assert from 'assert';
import shell from 'shelljs';
import inquirer from 'inquirer';
import ora from 'ora';
import {logger} from '@baidu/cba-utils';
import {IApi} from '../types';

export interface IPrompt {
  projectType?: string;
  projectName?: string;
  stateName?: string;
  [key: string]: any;
}

export interface IProject {
  name: string;
  key: string;
  initFn: (prompt: IPrompt) => Promise<any> | void;
  extPrompts?: () => Promise<any> | void; // 额外的prompt参数
}

const projectMap: {[key: string]: IProject} = {};

const afterInit = async (api: any, projectName?: string) => {
  projectName && shell.cd(projectName);
  const spinner = ora();
  spinner.prefixText = '已执行命令：npm install --legacy-peer-deps， 自动为您安装依赖中';
  spinner.start();
  shell.exec('npm install --legacy-peer-deps', (code, stdout, stderr) => {
    projectName && shell.cd('..');
    spinner.prefixText = '';
    spinner.text = '依赖安装完成！';
    spinner.succeed();
  });
};

export const addProject = (project: IProject) => {
  assert(!projectMap[project.key], `${project.name} has aleardy existed!`);
  projectMap[project.key] = project;
};

export const getProjects: () => IProject[] = () => {
  return Object.values(projectMap);
};

export const getProjectByKey = (key: string) => {
  return projectMap[key];
};

export default (api: IApi) => {
  api.describe({
    key: 'preset:init'
  });

  api.registerPlugins([
    require.resolve('./init-react'),
    require.resolve('./init-pagemaker-react'),
    require.resolve('./init-npm'),
    require.resolve('./init-react-project')
  ]);

  api.registerCommand({
    name: 'init',
    alias: '-i',
    description: 'init bce-cli project',
    fn: async ({args}: any) => {
      const choices = getProjects().map(({name, key}) => ({
        name,
        value: key
      }));
      const {projectType} = await inquirer.prompt([
        {
          type: 'list',
          message: '请选择初始化项目类型:',
          name: 'projectType',
          default: choices[0]?.value,
          choices: choices
        }
      ]);
      let prompt: IPrompt = {};
      prompt.projectType = projectType;
      const project = getProjectByKey(projectType);

      project?.extPrompts && Object.assign(prompt, await project.extPrompts());

      const {projectName} = await inquirer.prompt([
        {
          type: 'input',
          message: '请输入项目名称: (默认当前目录)',
          name: 'projectName',
          when: () => {
            return !args.projectName;
          }
        }
      ]);

      if (projectName && existsSync(projectName)) {
        logger.error(`文件夹【${projectName}】已存在`);
        process.exit(1);
      }
      prompt.projectName = args.projectName || projectName;
      try {
        await project?.initFn(prompt);
        logger.info(`初始化项目${prompt.projectName ? prompt.projectName : ''}成功`);
        afterInit(api, prompt.projectName);
      } catch (error) {
        logger.error(`初始化项目失败：${error}`);
      }
    }
  });
};
