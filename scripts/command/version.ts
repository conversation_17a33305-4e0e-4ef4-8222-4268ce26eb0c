import {join} from 'path';
import {writeFileSync} from 'fs';
import inquirer from 'inquirer';
import yargsParser from 'yargs-parser';

import {PATHS} from '../utils/constants';
import {getPkgs, getCurrentVersion} from '../utils';
import logger from '../utils/logger';

(async function () {
  const args = yargsParser(process.argv.slice(2));
  let pkgs = getPkgs(PATHS.PACKAGES, args.pkgType);
  const oldVersion = getCurrentVersion(args.pkgType);
  logger.wait(`当前版本号：${oldVersion}`);
  const logs: string[] = [];
  const {version} = await inquirer.prompt([
    {
      type: 'input',
      message: '请输入版本号：',
      name: 'version'
    }
  ]);
  pkgs.forEach(pkgDir => {
    const pkgPath = join(PATHS.PACKAGES, pkgDir, 'package.json');
    const pkgJson = require(pkgPath);
    const oldVersion = pkgJson.version;
    pkgJson.version = version;
    writeFileSync(pkgPath, JSON.stringify(pkgJson, null, 2), 'utf-8');
    logs.push(`@baidu/${pkgDir}: ${oldVersion} => ${version}`);
  });

  logger.info("Changes:");
  logs.forEach(log => {
    logger.ready(log);
  });
})();
