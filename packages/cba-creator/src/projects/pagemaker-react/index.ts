import {join} from 'path';
import {IUserConfig} from '@baidu/cba-core';

import {
  IApi,
  ProjectType,
  StateName
} from '../../types';

export default (api: IApi) => {
  api.describe({
    key: 'cba-creator:pagemaker-react-init',
    enableBy: () => {
      return api.appData.projectType === ProjectType.pagemakerReact;
    }
  });

  api.addFileFromTemplate(async () => {
    const source = join(__dirname, 'template');
    const target = join(api.cwd, api.appData.projectName);
    await api.renderTemplate(source, target, api.appData);
  });

  api.modifyFile(async() => {
    const {stateName} = api.appData;
    if (!stateName) {
      await api.removeTemplate(join(api.cwd, api.appData.projectName, 'src/store'));
    }
  });

  api.modifyPkgJson(async (pkg) => {
    const {stateName} = api.appData;
    if (stateName !== StateName.MOBX) {
      delete pkg.dependencies.mobx;
      delete pkg.dependencies['mobx-react'];
    }
    return pkg;
  });

  api.modifyConfig((config: IUserConfig) => {
    const {routerMode, stateName} = api.appData;
    routerMode && (config.routerMode = routerMode);
    stateName && (config.stateName = stateName);
    return config;
  });

};
