import {chalk, logger} from '@baidu/cba-utils';
import {IApi} from '../types';

export default (api: IApi) => {
  function getDeps(commands: any) {
    return Object.keys(commands)
      .map(key => {
        return `    ${chalk.green(key.padEnd(10))}  ${commands[key].description || ''}`;
      })
      .join('\n');
  }

  function padLeft(str: string) {
    return str
      .trim()
      .split('\n')
      .map((line: string) => `    ${line}`)
      .join('\n');
  }

  function showHelp(command: any) {
    console.log(`
Usage: cba-cli ${command.name} [options]
${command.description ? `${chalk.gray(command.description)}.\n` : ''}
${command.options ? `Options:\n${padLeft(command.options)}\n` : ''}
${command.details ? `Details:\n${padLeft(command.details)}` : ''}
`);
  }

  function showHelps(commands: typeof api.service.commands) {
    console.log(`
Usage: cba-cli <command> [options]

Commands:

${getDeps(commands)}
`);
    console.log(`Run \`${chalk.bold('cba-cli help <command>')}\` for more information of specific commands.`);
    console.log();
  }

  api.registerCommand({
    name: 'help',
    description: 'show commands help',
    details: `
cba-cli help build
cba-cli help dev
`,
    fn() {
      const subCommand = api.args._[0];
      if (subCommand) {
        if (subCommand in api.service.commands) {
          showHelp(api.service.commands[subCommand]);
        } else {
          logger.error(`Invalid sub command ${subCommand}.`);
        }
      } else {
        showHelps(api.service.commands);
      }
    }
  });
};
