import path from 'path';
import {IApi} from '@baidu/cba-preset';
import ReactRefreshWebpackPlugin from '@pmmmwh/react-refresh-webpack-plugin';
import EmbeddedModulePlugin from './EmbeddedModulePlugin';
import {getFlags, logger} from '@baidu/cba-utils';

export default (api: IApi) => {
  api.describe({
    key: 'preset-console-react-embed:webpack-dev',
    enableBy: () => api.env === 'development'
  });

  api.modifyWebpackConfig((webpackConfig: any, {merge}: any) => {
    const userConfig = api.userConfig;
    const port = userConfig.port || 8890;
    const moduleFileName = 'static/js/embed.js';
    const moduleName = `${userConfig.appName}AppModule`;
    const devServerHost = 'localhost';
    const output = {
      filename: moduleFileName,
      libraryTarget: 'amd',
      library: moduleName
    };

    webpackConfig = merge(webpackConfig, {
      mode: 'development',
      devtool: 'cheap-module-source-map',
      output,
      devServer: {
        allowedHosts: 'all',
        open: false,
        historyApiFallback: false,
        port,
        hot: true,
        compress: true,
        https: userConfig.protocol ? userConfig.protocol === 'https' : true,
        client: {
          overlay: false,
          progress: false,
          reconnect: 5
        }
      },
      plugins: [
        new ReactRefreshWebpackPlugin(),
        new EmbeddedModulePlugin({
          moduleFileName,
          port,
          host: devServerHost,
          moduleName,
          protocol: userConfig.protocol || 'https'
        })
      ]
    });
    return webpackConfig;
  });

  api.onBeforeServerStart(async ({origin}: {origin: string}) => {
    const flagPath = path.join(api.cwd, './src/flags.ts');
    await getFlags(
      {
        templateId: api.userConfig.templateId,
        flags: api.userConfig.flags
      },
      flagPath
    );
    logger.info(`开发调试： ${origin}/${api.userConfig.appName}/`);
  });
};
