# 百度云控制台前端示例模块（react）

> 百度云控制台项目参考文档：[文档参考](http://sandbox.bce.console.baidu-int.com/bce-docs/fe/console-setup/fe-setup/fe-repo.html)

## 安装环境

- 安装依赖
  > 指定`registry`为`http://registry.npm.baidu-int.com`
  > 推荐使用 `nrm` 管理 `registry`

```shell
    npm install --legacy-peer-deps
```

## 本地调试

```
    npx cba-cli dev
    // or
    npm run dev
```

## 接入Pagemaker

    在Pagemaker平台中【应用设置】-【外部js/css】中添加 http://localhost:8899/pagemaker.development.js ，并在Pagemaker应用中建立类型为【自定义开发页面】对应路由的页面。

## 本地编译

```
    npx cba-cli build
    // or
    npm run build
```

## 流水线

    配置可参照：https://console.cloud.baidu-int.com/devops/ipipe/workspaces/388790/pipelines/1054064/builds/list?branchName=master

## 相关文件说明

- `package.json`: 必要文件, 其中`name`与`main`为必要属性
- `bce-config.js`: 产品相关配置，包含`webpack`
