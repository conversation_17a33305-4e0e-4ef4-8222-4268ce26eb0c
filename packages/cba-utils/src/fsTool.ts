import {
  mkdirSync,
  writeFileSync,
  existsSync,
  readdirSync,
  lstatSync,
  unlinkSync,
  rmdirSync,

} from 'fs-extra';
import {
  dirname,
  sep,
  join
} from 'path';
/**
 * 创建多级目录的文件
 * @param filePath
 */
export function createNestedFile(filePath: string) {
  const dirPath = dirname(filePath);
  createNestedDirectories(dirPath);
  writeFileSync(filePath, '');
}

/**
 * 创建多层目录
 * @param dirPath
 */
export function createNestedDirectories(dirPath: string) {
  const directories = dirPath.split(sep);
  let currentPath = '';

  directories.forEach(directory => {
      currentPath = join(currentPath, directory);
      if (!existsSync(currentPath)) {
          mkdirSync(currentPath);
      }
  });
}

/**
 * 要删除的文件/文件夹路径
 * @param folderPath
 */
export function deleteFolderOrFile(folderPath: string) {
  if (existsSync(folderPath)) {
      if (lstatSync(folderPath).isFile()) {
        unlinkSync(folderPath);
        return;
      }
      readdirSync(folderPath).forEach(file => {
          const curPath = join(folderPath, file);
          if (lstatSync(curPath).isDirectory()) {
              // 递归删除子文件夹
              deleteFolderOrFile(curPath);
          } else {
              // 删除文件
              unlinkSync(curPath);
          }
      });
      // 删除空文件夹
      rmdirSync(folderPath);
  }
}
