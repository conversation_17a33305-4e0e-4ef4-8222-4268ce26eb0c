{"name": "@baidu/cba-preset-console-react-embed", "version": "1.2.8-beta.14", "license": "MIT", "main": "lib/index.js", "files": ["lib"], "scripts": {"build": "cba-scripts build", "dev": "cba-scripts dev"}, "dependencies": {"@babel/core": "^7.23.9", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-decorators": "^7.23.9", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-runtime": "^7.22.5", "@babel/plugin-transform-typescript": "^7.23.6", "@babel/preset-env": "^7.23.9", "@babel/preset-react": "^7.23.3", "@babel/preset-typescript": "^7.23.3", "@babel/runtime": "^7.23.9", "@baidu/cba-i18n": "workspace:*", "@baidu/cba-preset": "workspace:*", "@baidu/cba-utils": "workspace:*", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.11", "@svgr/webpack": "^8.1.0", "react-refresh": "^0.14.0", "shelljs": "^0.8.4", "url-loader": "^4.1.1"}, "devDependencies": {"@types/shelljs": "^0.8.15", "ansi-colors": "^4.1.3"}}