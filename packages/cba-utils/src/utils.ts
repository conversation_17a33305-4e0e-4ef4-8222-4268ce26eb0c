/**
 * @file 工具类
 */
import net from 'net';
import fs from 'fs-extra';
import {isEmpty} from 'lodash';
import prettier from 'prettier';
import assert from 'assert';
import childProcess from 'child_process';
import logger from './logger';
import {IReactJson} from './types';

const BCE_APP = /^BCE_APP_/i;

/**
 * 处理路径，兼容多个系统
 * @param path String 路径
 * @returns
 */
export function winPath(path: string) {
  const isExtendedLengthPath = path.startsWith('\\\\?\\');
  if (isExtendedLengthPath) {
    return path;
  }
  return path.replace(/\\/g, '/');
}

/**
 * 处理成数组
 * @param item
 * @returns
 */
export function makeArray(item: any) {
  return Array.isArray(item) ? item : [item];
}

/**
 * 获取可用的端口号
 * @param startPort
 * @param endPort
 * @returns
 */
export const findFreePort = (startPort: number | string, endPort: number): Promise<number> => {
  return new Promise((resolve, reject) => {
    if (typeof startPort !== 'number') {
      startPort = parseInt(startPort, 10);
    }
    let currentPort = startPort;
    function checkPort() {
      const server = net.createServer();
      server.once('error', (err: any) => {
        if (err.code === 'EADDRINUSE') {
          currentPort++;
          if (currentPort <= endPort) {
            checkPort();
          } else {
            reject(`${startPort} - ${endPort} 内已无可用端口`);
          }
        } else {
          reject(err);
        }
      });

      server.once('listening', () => {
        server.close(() => {
          resolve(currentPort);
        });
      });

      server.listen(currentPort, 'localhost');
    }
    checkPort();
  });
};

/**
 * 检查端口号
 * @param port
 * @returns
 */
export async function checkPort(port: number = 8899) {
  try {
    let avaliablePort = await findFreePort(port, 9000);
    if (avaliablePort !== port) {
      logger.warn(`！！！默认端口已被占用！！！，自动切换到：${port}`);
    }
    return avaliablePort;
  } catch (error) {
    logger.error(error as string);
    process.exit(1);
  }
}

/**
 * 获取环境变量
 * @param reg 正则匹配规则
 * @returns
 */
export function getProvideEnv(reg: RegExp = BCE_APP) {
  // 记录BCE_APP开头的环境变量，用于通过webpack.DefinePlugin 内联至打包结果中
  const env = Object.keys(process.env)
    .filter(key => (reg ? reg.test(key) : true))
    .reduce(
      (prev: Record<string, string | undefined>, key: string) => {
        prev[key] = process.env[key];
        return prev;
      },
      {
        NODE_ENV: process.env.NODE_ENV || 'development'
      }
    );

  return env;
}

/** 安全读取 json 文件 */
export function secureReadJsonFile(filePath: string) {
  const isExist = fs.existsSync(filePath);
  if (!isExist) {
    return {
      err: {
        name: 'NotFound',
        message: `The ${filePath} is not found.`
      }
    };
  }

  const fileContent = fs.readFileSync(filePath, 'utf-8');
  let jsonContent = {};

  try {
    jsonContent = JSON.parse(fileContent);
  } catch {
    return {
      err: {
        name: 'Invalid',
        message: 'The file content is not json.'
      }
    };
  }

  return {
    fileContent,
    content: jsonContent,
    isEmpty: isEmpty(jsonContent)
  };
}

/**
 * 判断是否是百度内部包
 * @param packageName
 * @returns
 */
export function isCliPresetPackage(packageName: string = '') {
  return /^@baidu\//.test(packageName) || /^@baiducloud\//.test(packageName);
}

/**
 * 获取npm包的最新版本
 * @param packageName 包名
 * @returns
 */
export function getPackageLatestVersion(packageName: string) {
  return childProcess
    .execSync(`npm view ${packageName} version --registry=http://registry.npm.baidu-int.com`, {
      encoding: 'utf-8'
    })
    .replace('\n', '');
}

/**
 * json对象转React dom字符串
 * @param json 类型为 IReactJson
 *  {
 *    type: 标签名称
 *    props: 值为对象时需要用以花括号包裹的字符串，示例：{object: '{object}'};
 *    children: 子元素为IReactJson数组
 *  }
 * @returns
 */
export function jsonToReactTpl(json: IReactJson): string {
  if (typeof json === 'string' || typeof json === 'number') {
    // 如果是字符串或数字，直接返回文本节点
    return JSON.stringify(json);
  }

  const { type, props, children } = json;
  // 构建 React 元素字符串
  return `<${type} ${Object.entries(props || {})
    .map(([key, value]) => `${key}=${value.startsWith('{') ? value : "'" + value + "'"}`)
    .join(' ')}>${(Array.isArray(children) ? children.map(jsonToReactTpl) : []).join('')}</${type}>`;
}

/**
 * 格式化文件
 * @param filePath 文件路径
 * @returns
 */
export async function formatFile(filePath: string) {
  if (!fs.existsSync(filePath)) {
    return;
  }
  const fileContent = fs.readFileSync(filePath, {
    encoding: 'utf-8'
  });
  const formattedFile = await formatCode(fileContent, filePath);
  fs.writeFileSync(filePath, formattedFile, 'utf-8');
}

export async function formatCode(codeText: string, filePath: string):Promise<string> {
  if (!codeText || !filePath) {
    return '';
  }
  const options = await prettier.resolveConfig(filePath);
  try {
    const formattedText = await prettier.format(codeText, {
      ...options,
      filepath: filePath
    });
    const lastIndex = formattedText.lastIndexOf(';');
    return formattedText.substring(0, lastIndex) + formattedText.substring(lastIndex + 1);
  }
  catch (err) {
    assert(false, `${filePath}：格式化出错`);
  }
}

/** 判断是否为 JSON 数据 */
export function isJSON(str: string) {
  try {
    JSON.parse(str);
    return true;
  } catch (e) {
    return false;
  }
}
