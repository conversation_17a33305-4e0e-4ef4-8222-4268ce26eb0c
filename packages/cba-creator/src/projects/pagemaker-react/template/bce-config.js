/**
 * @file 项目配置
 */

module.exports = {
  // 功能清单id
  templateId: '86089d33-aecc-424f-a241-784d4e94d08e',
  // 模块相关配置
  appName: 'demo', // 表明访问的path，默认代码库后几位字母
  pagemaker: {
    // pagemaker 应用所属组织
    company: 'console',
    // pagemaker 里的应用id，见【应用配置-基本信息-短名字】
    appId: ''
  },
  // 自定义webpack，可选。
  webpack: {
    // devServer: {
    //   port: 8899,
    //   open: true,
    //   allowedHosts: 'all',
    //   headers: {
    //     'Access-Control-Allow-Origin': 'https://pagemaker.baidu-int.com',
    //     'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, PATCH, OPTIONS',
    //     'Access-Control-Allow-Headers': 'X-Requested-With, content-type, Authorization, x-request-by',
    //     'Access-Control-Allow-Credentials': 'true'
    //   }
    // },
    // devtool: 'eval-source-map'
  },
  presets: ['@baidu/cba-preset-pagemaker-react']
};
