import {IApi} from './types';

export default (api: IApi) => {
  api.describe({
    key: 'cba-creator:preset'
  });

  return {
    plugins: [
      require.resolve('./registerMethods'),
      require.resolve('./baseApi'),
      require.resolve('./init'),
      require.resolve('./version'),

      require.resolve('./projects/consoleReact'),
      require.resolve('./projects/reactProject'),
      require.resolve('./projects/pagemaker'),
      require.resolve('./projects/consoleReactEmbed'),

      require.resolve('./projects/pagemaker-react/pagemakerReactPrompts'),

      require.resolve('./projects/base-project/index'),
      require.resolve('./projects/pagemaker-npm/index'),
      require.resolve('./projects/pagemaker-react/index'),
      require.resolve('./projects/react-project/index'),
      require.resolve('./projects/console-react/index'),
      require.resolve('./projects/console-react-embed/index'),
      require.resolve('./react-i18n'),

      require.resolve('./generate'),
      require.resolve('./generators/index')
    ]
  };
};
