// 文字超过n行省略

import React, {useCallback, useEffect, useRef, useState} from 'react';

import styles from './index.module.less';

type TextEllipsisType = {
  text: string;
  height: number;
  expandNode?: React.ReactNode;
  shrinkNode?: React.ReactNode;
};

const TextEllipsis: React.FC<TextEllipsisType> = ({
  text,
  height,
  expandNode = <span>查看全部</span>,
  shrinkNode = <span>收起</span>
}) => {
  const textRef = useRef<HTMLDivElement>(null);
  const [ellipsis, setEllipsis] = useState<boolean>(true);
  const [isExpand, setExpand] = useState<boolean>(false);

  const computedDisplayText = useCallback(() => {
    if (textRef.current) {
      const textWrap = textRef.current;
      const textDom = textRef.current?.querySelector(
        '.ellipsis-text'
      ) as HTMLSpanElement;
      if (!textDom || !textWrap) {
        return;
      }
      if (isExpand) {
        textDom.innerText = text;
      } else if (textWrap.offsetHeight > height) {
        let loop = 1000;
        let disPlayText = text;
        while (textWrap.offsetHeight > height && loop > 0) {
          if (textWrap.offsetHeight > height * 3) {
            textDom.innerText = disPlayText = disPlayText.substring(
              0,
              Math.floor(disPlayText.length / 2)
            );
          } else {
            textDom.innerText = disPlayText = disPlayText.substring(
              0,
              disPlayText.length - 1
            );
          }
          loop--;
        }
      }
    }
  }, [isExpand, height, text]);

  useEffect(() => {
    if (textRef.current) {
      const textDom = textRef.current;
      setEllipsis(textDom.offsetHeight > height);
    } else {
      setExpand(false);
    }
  }, [text, height]);

  useEffect(() => {
    computedDisplayText();
  }, [text, height, computedDisplayText]);

  const onShrink = () => {
    setExpand(false);
  };
  const onExpand = () => {
    setExpand(true);
  };
  return (
    <div
      className={styles.ellipsisTextWrap}
      ref={textRef}
      style={{lineHeight: `${height}px`}}
    >
      <span className="ellipsis-text">{text}</span>
      {ellipsis && (
        <span className={styles.moreText}>
          {!isExpand && (
            <>
              <i>...</i>
              <span onClick={onExpand}>{expandNode}</span>
            </>
          )}
          {isExpand && <span onClick={onShrink}>{shrinkNode}</span>}
        </span>
      )}
    </div>
  );
};

export default TextEllipsis;
