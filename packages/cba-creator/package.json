{"name": "@baidu/cba-creator", "version": "1.1.7", "description": "BaiduCloud Console-FE CLI", "main": "lib/index.js", "scripts": {"build": "sh build.sh build", "dev": "sh build.sh dev"}, "bin": {"create-bce-app": "bin/create-bce-app.js"}, "files": ["bin", "lib"], "license": "MIT", "dependencies": {"@baidu/cba-core": "workspace:*", "@baidu/cba-utils": "workspace:*", "assert": "^2.1.0", "deepmerge": "^4.3.1", "fs-extra": "^11.2.0", "inquirer": "^8.2.6", "ora": "^5.0.0", "lodash": "^4.17.21", "shelljs": "^0.8.4", "yargs-parser": "^21.1.1"}, "devDependencies": {"@types/debug": "^4.1.8", "@types/fs-extra": "^11.0.4", "@types/inquirer": "^9.0.7", "@types/lodash": "^4.17.4", "@types/shelljs": "^0.8.15", "@types/yargs-parser": "^21.0.0"}}