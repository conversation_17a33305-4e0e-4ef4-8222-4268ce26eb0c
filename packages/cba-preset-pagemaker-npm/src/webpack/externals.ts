/**
 * @file 配置 webpack externals
 * <AUTHOR> (<EMAIL>)
 */

import {IApi} from '@baidu/cba-preset';

export default (api: IApi) => {
  api.describe({
    key: 'preset-pagemaker-npm:externals',
    enableBy: () => !api.userConfig.__noAmisRequire
  });

  api.chainWebpack(config => {
    config.externals({
      ...['amis', 'amis-editor', 'amis-editor-core', 'react', 'react-dom', 'moment', 'axios', 'classnames'].reduce(
        (
          res: {
            [module: string]: string;
          },
          key: string
        ) => {
          res[key] = `amisRequire("${key}")`;
          return res;
        },
        {}
      ),
      '@fex/amis': 'amisRequire("amis")',
      '@fex/amis-core': 'amisRequire("amis")'
    });
  });
};
