/**
 * @file webpack build配置
 * <AUTHOR> (<EMAIL>)
 */

import fs from 'fs';
import path from 'path';
import fsExtra from 'fs-extra';
import nodeExternals from 'webpack-node-externals';
import {IApi, Env} from '@baidu/cba-preset';
import {getLocalAmisVersion} from '@baidu/cba-utils';

/**
 * 将 amis sdk 拷贝到 构建后的文件夹中
 */
function copyLocalAmisSdkToDist(distDir: string = 'dist') {
  const amisSdkPathPrefix = fs.existsSync('node_modules/amis/sdk/sdk.js') ? '' : '../../';
  const version = getLocalAmisVersion();
  return Promise.all(
    [
      {from: amisSdkPathPrefix + 'node_modules/amis/lib', to: `${distDir}/amis@${version}/lib`},
      {from: amisSdkPathPrefix + 'node_modules/amis/sdk', to: `${distDir}/amis@${version}/sdk`},
      {from: amisSdkPathPrefix + 'node_modules/amis/package.json', to: `${distDir}/amis@${version}/package.json`}
    ].map(item => fsExtra.copy(item.from, item.to))
  );
}

class ResourceBasePlugin {
  keyValues: any;
  constructor(args: any) {
    this.keyValues = args;
  }
  apply(compiler: any) {
    compiler.hooks.emit.tap('ResourceBasePlugin', (compilation: any) => {
      for (const name in compilation.assets) {
        if (name.endsWith('.js')) {
          // 仅处理js文件，css用的话再扩展
          let noComments = compilation.assets[name].source();
          for (const key in this.keyValues) {
            noComments = noComments.replaceAll(key, this.keyValues[key]);
          }
          compilation.assets[name] = {
            source: () => noComments,
            size: () => noComments.length
          };
        }
      }
    });
  }
}

export default (api: IApi) => {
  api.describe({
    key: 'preset-pagemaker-npm:webpack-build',
    enableBy: () => api.env === Env.production
  });

  api.modifyWebpackConfig((webpackConfig, {merge}) => {
    // 需要处理的amisRequire
    const replaceSource: any = {};
    replaceSource[`require('amisRequire("amis-editor-core")')`] = `amisRequire('amis-editor-core')`;
    replaceSource[`require('amisRequire("amis")')`] = `amisRequire('amis')`;
    if (!webpackConfig.plugins || !webpackConfig.plugins.length) {
      webpackConfig.plugins = [new ResourceBasePlugin(replaceSource)];
    } else {
      webpackConfig.plugins.push(new ResourceBasePlugin(replaceSource));
    }

    return merge(webpackConfig, {
      mode: 'production',
      entry: {
        index: ['./src/index.ts'],
        plugin: ['./src/plugin.ts']
      },
      output: {
        filename: '[name].js',
        path: path.resolve(api.cwd, './dist'),
        publicPath: '/',
        libraryTarget: 'commonjs'
      },
      // 兼容 pagemaker 注册npm包组件
      externals: [
        nodeExternals({
          importType: 'commonjs',
          allowlist: [/^@babel\/runtime-corejs3/, /^core-js-pure/]
        }),
        {
          'amis-editor-core': 'amisRequire("amis-editor-core")',
          'amis': 'amisRequire("amis")',
          'amis-core': 'amisRequire("amis")',
          'amis-ui': 'amisRequire("amis")',
          '@fex/amis': 'amisRequire("amis")',
          '@fex/amis-core': 'amisRequire("amis")',
          '@fex/amis-ui': 'amisRequire("amis")'
        }
      ]
    });
  });

  api.onBundlerFinished(async ({config, outPath}) => {
    if (!api.userConfig.remoteAmisSdk) {
      await copyLocalAmisSdkToDist();
    }
  });
};
