import request from '@/utils/request';

/** 查询服务参数 */
export function getServiceParam(): Promise<{
  success: boolean;
  status: number;
  result: Array<{
    roleName: string;
    policyId: string;
    serviceId: string;
    /** 是否激活 */
    isActivated?: boolean;
  }>;
}> {
  // return request({
  //   url: '/api/service/param',
  //   method: 'POST'
  // });
  return Promise.resolve({
    success: true,
    status: 200,
    result: [
      {
        roleName: 'BceServiceRole_dsc',
        policyId: 'da5633a62213468dbc810f6b1656459c',
        serviceId: 'dd4c4dd89f3d4dada4cbe7c88fb008b2'
      }
    ]
  });
}
