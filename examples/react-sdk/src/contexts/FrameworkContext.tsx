import {createContext, useMemo} from 'react';
import React from 'react';

const initialState: {
  /** framework 中的原始数据 */
  frameworkData?: any;
  /** 用户是否完成实名认证 */
  isRealNameVerified?: boolean;
  /** 当前的用户 ID */
  userId?: string;
} = {};

export const FrameworkContext = createContext(initialState);

interface FrameworkProviderProps {
  frameworkData: any;
  children?: React.ReactNode;
}

export const FrameworkProvider: React.FC<FrameworkProviderProps> = ({
  frameworkData,
  children
}) => {
  /** 当前用户是否完成实名认证 */
  const isRealNameVerified = useMemo(() => {
    return frameworkData?.constants?.verifyUser?.verifyStatus === 'PASS';
  }, [frameworkData?.constants?.verifyUser?.verifyStatus]);

  /** 当前的用户ID */
  const userId = useMemo(() => {
    return frameworkData?.constants?.userid;
  }, [frameworkData?.constants?.userid]);

  return (
    <FrameworkContext.Provider
      value={{frameworkData, isRealNameVerified, userId}}
    >
      {children}
    </FrameworkContext.Provider>
  );
};

export function useFrameworkContext() {
  const context = React.useContext(FrameworkContext);
  if (!context) {
    throw new Error(
      'useFrameworkContext must be used within a FrameworkProvider'
    );
  }
  return context;
}
