# 版本变更信息

## 1.2.8-beta.14 (2025-05-23)


**【新增功能】**

- 灰度测试版本号添加trim处理,避免因为空格导致的白屏问题
      


## 1.2.8-beta.13 (2025-05-22)

**【新增功能】**

- `@baidu/cba-preset-react` 可支持 bce-config.js文件配置 publicPath、port、host

## 1.2.8-beta.12 (2025-05-19)

**【新增功能】**

- `@baidu/cba-preset-react` 可支持公有云产品切换此预设来实现【单产品私有化】
  - 支持通过 bce-config.js中设置 `htmlTemplate` 自定义html模板
  - 支持命令行通过 `--hasFlags` 参数生成功能清单文件

**【问题修复】**
无

## 1.2.8-beta.11 (2025-05-12)

**【新增功能】**

- **【问题修复】**

- 本地运行私有化模式下使用私有云模板

## 1.2.8-beta.10 (2025-04-24)

**【新增功能】**
无

**【问题修复】**

- 修正`react_sdp.html`中`bosuploader`、`cyberlatest`依赖的CDN域名

## 1.2.8-beta.9 (2025-04-01)

**【新增功能】**

- 同步支持行业云330功能清单改造

## 1.2.8-beta.8 (2025-03-18)

> `1.2.8-beta.7` 版本存在问题 (deprecated)

**【新增功能】**

- `@baidu/cba-preset-console-react`纯react模板支持设置`ALPHA_RELEASE`环境变量，实现测试独立发版。

**【问题修复】**

## 1.2.8-beta.6 (2025-02-25)

\*_【新增功能】_

**【问题修复】**

- react mix sdp私有化index处理私有化依赖

## 1.2.8-beta.4 (2025-02-24)

**【新增功能】**

- 解决tsConfig中指定paths编译时不生效问题

**【问题修复】**

-

## 1.2.8-beta.3 (2025-02-19)

**【新增功能】**

- babel配置添加垫片能力

**【问题修复】**

-

## 1.2.8-beta.2 (2025-02-18)

**【新增功能】**

- **【问题修复】**

- less计算方法问题修复

## 1.2.8-beta.1 (2024-12-27)

**【新增功能】**

- 去掉node-sass sass-loader依赖

## 1.2.7 (2024-12-10)

**【新增功能】**

- 将1.2.7测试版本中修复的问题发布正式版本

**【问题修复】**

-

## 1.2.7-beta.32 (2024-12-10)

**【问题修复】**

- 修复React项目通过url无法加载svg资源的问题

## 1.2.7-beta.31 (2024-11-22)

**【问题修复】**

- 修复流水线获取功能清单失败问题

## 1.2.7-beta.30 (2024-11-21)

**【问题修复】**

- 修复流水线获取功能清单失败问题

## 1.2.7-beta.28 (2024-11-19)

**【新增功能】**

- 调整private_react_sdp模板；
- 调整cba-preset-console-react插件，适配sdp&cba混合工程私有化构建

**【问题修复】**

-

## 1.2.7-beta.25 (2024-11-06)

**【新增功能】**

- cba-preset-console-react babelconfig增加i18n相关配置，适配sdp混合工程打包

**【问题修复】**

-

## 1.2.7-beta.24 (2024-11-04)

**【新增功能】**

- 调整cb-preset-console-react babel config，支持SDP混合工程国际化

**【问题修复】**

-

## 1.2.7-beta.22 (2024-10-30)

**【新增功能】**

- 新增babel插件@babel/plugin-transform-private-property-in-object

**【问题修复】**

-

## 1.2.7-beta.21 (2024-10-30)

**【新增功能】**

- 修改@babel/plugin-proposal-private-property-in-object插件依赖为peer Dependence

**【问题修复】**

-

## 1.2.7-beta.20 (2024-10-30)

**【新增功能】**

- 新增babel插件，兼容safari@14 class private修饰符报错问题

**【问题修复】**

-

## 1.2.7-beta.19 (2024-10-30)

**【新增功能】**

- 调整babelconfig，适配safari 14

**【问题修复】**

-

## 1.2.7-beta.18 (2024-10-28)

**【新增功能】**

- 【cba-i18n】更新依赖i18next-parser版本，fix cheerio版本不兼容问题

**【问题修复】**

-

## 1.2.7-beta.17 (2024-10-23)

**【新增功能】**

- - IaaS-FE-298 【cba-cli】开发模式下index增加moduleName映射，解决偶现白屏问题

**【问题修复】**

-

## 1.2.7-beta.14 (2024-10-21)

**【新增功能】**

- 去掉react-sdp.html中无用map

**【问题修复】**

-

## 1.2.7-beta.13 (2024-10-18)

**【新增功能】**

- `cba-preset-console-react` 产出带上SDP自定义依赖信息

**【问题修复】**

-

## 1.2.7-beta.12 (2024-10-14)

**【新增功能】**

- **【问题修复】**

- fix: `cba-preset-console-react`模板未正确加载`alpha_version`问题

## 1.2.7-beta.10 (2024-10-10)

**【新增功能】**

- **【问题修复】**

- 通用index兼容虚商沙盒配置文件加载

## 1.2.7-beta.9 (2024-09-12)

**【新增功能】**

- **【问题修复】**

- fix getRegion变量类型问题

## 1.2.7-beta.8 (2024-09-02)

**【新增功能】**

- 支持覆盖率灰度打包方案

## 1.2.7-beta.7 (2024-08-29)

**【问题修复】**

- 开启多线程编译

## 1.2.6-beta.3 (2024-08-21)

**【新增功能】**

- cba-i18n 支持提取san页面语料

**【问题修复】**

-

## 1.2.6-beta.2 (2024-08-13)

**【新增功能】**

- regionchange cb中变量名问题修复

**【问题修复】**

-

## 1.2.6-beta.1 (2024-08-13)

**【新增功能】**

- index支持加载不同region指定版本

**【问题修复】**

-

## 1.2.6 (2024-07-26)

**【新增功能】**

- mock 核心功能发布

**【问题修复】**

-

## 1.2.5 (2024-07-18)

**【新增功能】**

- **【问题修复】**
  第三方模块flag配置处理

-

## 待发布修改

- 三方模块图片转base64 最大支持20k [2024-07-17]

## 1.2.4 (2024-07-16)

**【新增功能】**

- **【问题修复】**

- 覆盖问题版本

## 1.2.3-beta.1 (2024-07-08)

**【新增功能】**

svg 资源支持url方式引入 && https请求配置证书

**【问题修复】**

-

## 1.2.2 (2024-07-01)

**【问题修复】**

- 修复获取包版本不匹配问题

## 1.2.1 (2024-06-21)

**【问题修复】**

- 初始化项目拉取最新厂内包

## 1.2.0 (2024-06-17)

**【新增功能】**

- **【问题修复】**
  修复第三方模块图片本地加载问题

## 1.1.9 (2024-06-12)

**【问题修复】**

- 调整加载时机

## 1.1.8 (2024-06-12)

**【问题修复】**

- 修复publicPath加载问题

## 1.1.7 (2024-06-12)

**【新增功能】**

加载监控脚本-

**【问题修复】**

-

## 1.1.6 (2024-06-11)

**【新增功能】**

- **【问题修复】**

- 修复本地运行resize error报错

## 1.1.5 (2024-06-06)

**【问题修复】**

- 一些bug修复

## 1.1.4 (2024-06-06)

**【新增功能】**

- 一些bug修复

## 1.1.3-beta.6 (2024-06-05)

**【新增功能】**

- **【问题修复】**
  修复第三方模块打包module undefined问题

-

## 1.1.3-beta.5 (2024-06-04)

**【新增功能】**

- **【问题修复】**

- 修改内置的 bce-react-toolkit 版本

## 1.1.3-beta.4 (2024-05-29)

**【新增功能】**

- 支持加载online-config.new 适配hanoi上线产品要求

**【问题修复】**

-

## 1.1.3-beta.3 (2024-05-29)

**【问题修复】**

- 修改控制台应用兜底代理

## 1.1.3-beta.2 (2024-05-28)

**【问题修复】**

- 修复云控制台应用以对象方式自定义proxy无效问题

## 1.1.3-beta.1 (2024-05-20)

**【新增功能】**

- **【问题修复】**

- css中相对路径引用图片 打包后请求路径错误

## 1.1.3 (2024-05-15)

**【新增功能】**

- `@baidu/cba-preset-console-react`支持设置`ALPHA_RELEASE`环境变量，实现测试独立发版。

## 1.1.2 (2024-04-26)

**【新增功能】**

- **【问题修复】**

- SDK 增加内置 babel 相关依赖

## 1.1.1 (2024-04-17)

**【问题修复】**

- 修复配置devSever中open不生效问题

## 1.1.1-beta.6 (2024-04-10)

**【新增功能】**

- 发包测试

## 1.1.1-beta.5 (2024-04-09)

**【问题修复】**

- 调整@baidu/cba-core加载配置文件方式

## 1.1.1-beta.4 (2024-04-09)

**【问题修复】**

- 修复创建项目时读取配置文件的问题

## 1.1.1-beta.3 (2024-04-03)

**【新增功能】**

- 初始化项目拆包@baidu/cba-creator，命令为create-bce-app

## 1.1.0 (2024-03-07)

**【新增功能】**

- 不兼容升级, 命令行从create-bce-cli 改为cba-cli
- 新增版本检查功能
- react独立应用支持国际化配置

**【问题修复】**

-

## 1.0.1-beta.7 (2024-03-07)

**【问题修复】**

- 修复语料打包问题

## 1.0.1-beta.6 (2024-03-07)

**【新增功能】**

- 独立React应用支持国际化配置

## 1.0.2 (2024-03-06)

## 1.0.4 (2024-03-07)

**【问题修复】**

- 优化 sdk dev 逻辑
- 优化 i18n 语料提取逻辑

## 1.0.2 (2024-03-06)

**【问题修复】**

- 修复React独立应用打包未引入js文件问题

## 1.0.1 (2024-03-04)

**【新增功能】**

- acud配置移到项目中

## 1.0.1-beta.0 (2024-02-28)

**【新增功能】**

- 新增React独立应用插件集

## 1.0.0 (2024-02-22)

**【新增功能】**

发布正式版本【基础项目功能，支持控制台React项目初始化、构建、部署等功能】
