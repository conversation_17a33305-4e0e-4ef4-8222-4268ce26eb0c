import {
  IApi,
  ProjectType,
  RouterMode,
  StateName
} from '../../types';

export default (api: IApi) => {
  api.describe({
    key: 'cba-creator:pagemaker-react-prompts',
    enableBy: () => {
      return api.appData.projectType === ProjectType.pagemakerReact;
    }
  });

  api.addPrompts(() => {
    return [
      {
        type: 'list',
        message: '请选择路由类型:',
        name: 'routerMode',
        default: RouterMode.HASH,
        choices: [
          {
            name: 'Hash',
            value: RouterMode.HASH
          },
          {
            name: 'History',
            value: RouterMode.HISTORY
          }
        ]
      },
      {
        type: 'list',
        message: '请选择状态管理库:',
        name: 'stateName',
        default: '',
        choices: [
          {
            name: '无',
            value: ''
          },
          {
            name: 'Mobx',
            value: StateName.MOBX
          }
        ]
      }
    ];
  });

};
