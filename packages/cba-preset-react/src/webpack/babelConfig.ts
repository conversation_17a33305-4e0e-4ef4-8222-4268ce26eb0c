/**
 * 获取babel配置
 */
import path from 'path';
import {IUserConfig} from '@baidu/cba-preset';

export function getBabelConfig(userConfig: IUserConfig) {
  const isProduction = process.env.NODE_ENV === 'production';
  const {babelOptions: {presets = [], plugins = [], resolveOptions} = {}} = userConfig;
  // npm link查找模块安装包使用
  module.paths.unshift(path.join(process.cwd(), 'node_modules'));
  const babelConfig = {
    cacheDirectory: !isProduction,
    presets: [
      [
        '@babel/preset-env',
        {targets: '> 0.5%, last 2 versions, Firefox ESR, not dead'} // https://browserl.ist/
      ],
      '@babel/preset-typescript',
      '@babel/preset-react',
      ...presets
    ],
    plugins: [
      !isProduction && 'react-refresh/babel',
      ['@babel/plugin-transform-runtime', {help: true}],
      '@babel/plugin-syntax-dynamic-import',
      ['@babel/plugin-proposal-decorators', {legacy: true}],
      ['@babel/plugin-proposal-class-properties'],
      ...plugins
    ].filter(Boolean)
  };
  return resolveOptions ? resolveOptions(babelConfig) : babelConfig;
}
