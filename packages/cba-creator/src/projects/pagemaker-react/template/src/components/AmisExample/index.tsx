import React from 'react';
import {render as renderAmis} from 'amis';

export default function AmisExample() {
  return (
    <div>
      {renderAmis({
        type: 'page',
        body: {
          type: 'form',
          body: [
            {
              type: 'input-text',
              name: 'name',
              label: '姓名：',
              required: true,
              value: '王小明'
            }
          ],
          onEvent: {
            submitSucc: {
              weight: 0,
              actions: [
                {
                  args: {
                    msgType: 'success',
                    position: 'top-right',
                    closeButton: true,
                    showIcon: true,
                    msg: '${name}, 提交成功！'
                  },
                  actionType: 'toast'
                }
              ]
            }
          }
        }
      })}
    </div>
  );
}
