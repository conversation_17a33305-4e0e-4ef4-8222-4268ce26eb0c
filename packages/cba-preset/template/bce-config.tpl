/**
 * @file 项目配置
 */

module.exports = {
  // 功能清单id
  templateId: '86089d33-aecc-424f-a241-784d4e94d08e',
  {{#projectType !== 'pagemaker-npm'}}
  // 模块相关配置
  appName: 'demo', // 表明访问的path，默认代码库后几位字母
  pagemaker: {
    // pagemaker 应用所属组织
    company: 'console',
    // pagemaker 里的应用id，见【应用配置-基本信息-短名字】
    appId: '',
  },
  // 自定义webpack，支持配置 config 或 (config) => config
  {{/projectType !== 'pagemaker-npm'}}
  {{#presets}}
  presets: {{{presets}}},
  {{/presets}}
  webpack: {
      // devServer: {
      //   port: 8899,
      //   open: true,
      //   allowedHosts: 'all',
      //   headers: {
      //     'Access-Control-Allow-Origin': 'https://pagemaker.baidu-int.com',
      //     'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, PATCH, OPTIONS',
      //     'Access-Control-Allow-Headers': 'X-Requested-With, content-type, Authorization, x-request-by',
      //     'Access-Control-Allow-Credentials': 'true'
      //   }
      // },
      // devtool: 'eval-source-map'
  },
  {{#routerMode}}
  // 路由配置，默认为hash，支持hash、history
  routerMode: '{{routerMode}}',
  {{/routerMode}}
  {{#stateName}}
  // 状态库配置，默认为空，支持 mobx
  stateName: '{{stateName}}',
  {{/stateName}}
  {{#projectType === 'react'}}
  // 是否使用远程 amis sdk
  // 默认使用的远程 sdk，实际生成的 url为：https://unpkg.com/amis@3.4.2/sdk/sdk.js
  // 也可以配置 sdkJsUrl, helperCssUrl, themeCssUrl
  // remoteAmisSdk: {
  //   cdnHost: 'https://unpkg.com',
  //   version: '3.4.2',
  //   theme: 'cxd'
  // },
  // 为 false 时使用本地 install 的 amis, 需要自己执行 npm i amis -D
  // remoteAmisSdk: false,
  {{/projectType === 'react'}}
};
