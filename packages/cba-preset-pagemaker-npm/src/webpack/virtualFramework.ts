/**
 * @file 定义虚拟文件插件
 * <AUTHOR> (<EMAIL>)
 */

import path from 'path';
import fs from 'fs';
import VirtualModulesPlugin from 'webpack-virtual-modules';
import {IApi} from '@baidu/cba-preset';

const getTpl = (name: string) => {
  return fs.readFileSync(path.join(__dirname, `../../template/runtime/${name}`), 'utf-8');
};

export default (api: IApi) => {
  api.modifyWebpackConfig((webpackConfig, {merge}) => {
    return merge(webpackConfig, {
      resolve: {
        alias: {
          'register-amis-renderer': '/src/register-amis-renderer.ts',
          'register-amis-editor-plugin': '/src/register-amis-editor-plugin.ts'
        }
      },
      plugins: [
        // 虚拟文件，对用户不可见，隐藏在脚手架里
        new VirtualModulesPlugin({
          // amis-widgets 相关
          'src/register-amis-editor-plugin.ts': getTpl('amis-widget-register/register-amis-editor-plugin.ts'),
          'src/register-amis-renderer.ts': getTpl('amis-widget-register/register-amis-renderer.ts')
        })
      ]
    });
  });
};
