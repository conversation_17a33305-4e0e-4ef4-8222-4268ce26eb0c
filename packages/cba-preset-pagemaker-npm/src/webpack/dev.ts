/**
 * @file webpack Dev配置
 * <AUTHOR> (<EMAIL>)
 */

import fs from 'fs';
import path from 'path';
import {IApi, Env} from '@baidu/cba-preset';
import {logger} from '@baidu/cba-utils';
import HtmlWebpackPlugin from 'html-webpack-plugin';

const getTpl = (name: string) => {
  return fs.readFileSync(path.join(__dirname, `../../template/runtime/${name}`), 'utf-8');
};

export default (api: IApi) => {
  api.describe({
    key: 'preset-pagemaker-npm:webpack-dev',
    enableBy: () => api.env === Env.development
  });

  api.modifyWebpackConfig((config: any, {merge}) => {
    const {host, port} = config.devServer || {
      host: 'localhost',
      port: '8899'
    };

    config = merge(config, {
      entry: './src/preview.tsx',
      output: {
        filename: 'amis.widget.development.js'
      },
      devtool: 'eval-source-map',

      devServer: {
        https: true,
        // 解决pageMaker中websocket连接失败导致的热更新失效问题
        // 1. 指定 client.webSocketURL: 'ws://host:port/ws' 否则pageMaker中会自动请求 ws://pagemaker-int.baidu.com:port/ws
        // 2. 设置 allowedHosts，允许 跨域连接 websocket
        // 3. 设置 headers.Access-Control-Allow-Origin , 允许热更新后跨域访问 xxx.update.json 文件
        //    顺便解决 pageMaker中请求静态资源失败的问题
        client: {
          overlay: false,
          progress: true,
          reconnect: 5,
          webSocketURL: `auto://${host}:${port}/ws`
        },
        allowedHosts: 'all',
        headers: {
          'Access-Control-Allow-Origin': 'https://pagemaker.baidu-int.com',
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, PATCH, OPTIONS',
          'Access-Control-Allow-Headers': 'X-Requested-With, content-type, Authorization, x-request-by',
          'Access-Control-Allow-Credentials': 'true'
        }
      },
      plugins: [
        new HtmlWebpackPlugin({
          scriptLoading: 'blocking',
          templateContent: getTpl('amis-widget-index.html')
        })
      ]
    });

    return config;
  });

  api.onBeforeServerStart(({config, origin}: {config: any; origin: string}) => {
    logger.info(`本地预览地址： ${origin}`);
    logger.info('pagemaker 平台开发调试【应用设置 -> 临时JS/CSS -> 外链JS】地址：');
    logger.info(`${origin}/${config.output.filename}`);
  });
};
