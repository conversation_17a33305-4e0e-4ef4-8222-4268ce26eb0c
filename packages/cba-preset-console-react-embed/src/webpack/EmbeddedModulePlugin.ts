/* eslint-disable @typescript-eslint/no-var-requires */
import {logger} from '@baidu/cba-utils';
import {Compiler} from 'webpack';

interface IEmbedOpts {
  host: string;
  port: number;
  protocol: string;
  moduleFileName: string;
  moduleName: string;
}

export default class EmbeddedModulePlugin {
  host: string;
  port: number;
  protocol: string;
  moduleFileName: string;
  moduleName: string;
  url: string;

  constructor(options: IEmbedOpts) {
    this.host = options.host;
    this.port = options.port;
    this.protocol = options.protocol || 'https';
    this.moduleFileName = options.moduleFileName;
    this.moduleName = options.moduleName;
    this.url = this.buildUrl();
  }

  processPath(path = '') {
    path = path.replace(/^\//, '').replace(/\/$/, '');
    return '/' + path;
  }

  buildUrl() {
    return `${this.protocol}://${this.host}:${this.port}${this.processPath(this.moduleFileName)}`;
  }

  apply(compiler: Compiler) {
    compiler.hooks.done.tap('EmbeddedModulePlugin', () => {
      logger.info(`\nembedded amd module address: ${this.url}`);
      logger.info(`embedded amd module name: ${this.moduleName}`);
    });
  }
}
