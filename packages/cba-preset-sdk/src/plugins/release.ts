import fs from 'fs';
import {execSync} from 'child_process';
import {IApi} from '@baidu/cba-preset';
import inquirer from 'inquirer';
import semver from 'semver';
import {logger} from '@baidu/cba-utils';
import configuration from '../configuration';

export default (api: IApi) => {
  api.registerCommand({
    name: 'release',
    description: 'SDK 发版',
    fn: async ({config}) => {
      configuration.setConfig(config);

      const getPath = configuration.getPath.bind(configuration);
      const getCwd = configuration.getCwd.bind(configuration);

      const packageJsonFilePath = getPath('package.json');

      const packageJsonFileContent = JSON.parse(fs.readFileSync(packageJsonFilePath, 'utf-8'));

      const currentVersion = packageJsonFileContent.version;

      const betaVersion = 'Beta 测试版';

      const {releaseType} = await inquirer.prompt([
        {
          type: 'list',
          name: 'releaseType',
          message: '请选择发布类型',
          choices: [betaVersion, '正式版'],
          default: betaVersion
        }
      ]);

      const isBetaVersion = releaseType === betaVersion;

      const {incType} = await inquirer.prompt([
        {
          type: 'list',
          name: 'incType',
          message: '请选择升级版本号',
          choices: ['major', 'minor', 'patch', isBetaVersion && 'prerelease'].filter(Boolean),
          default: isBetaVersion ? 'prerelease' : 'patch'
        }
      ]);

      let releaseTarget = incType;

      if (isBetaVersion && incType !== 'prerelease') {
        releaseTarget = `pre${incType}`;
      }

      const targetVersion = semver.inc(currentVersion, releaseTarget, releaseType === betaVersion ? 'beta' : undefined);

      const {isConfirm} = await inquirer.prompt([
        {
          type: 'confirm',
          name: 'isConfirm',
          message: `目标版本为 ${targetVersion}, 是否确认发布？`
        }
      ]);

      if (!isConfirm) {
        logger.error('The release has been canceled.');
        return;
      }

      packageJsonFileContent.version = targetVersion;

      fs.writeFileSync(packageJsonFilePath, JSON.stringify(packageJsonFileContent, undefined, 2), 'utf-8');

      execSync('npm publish', {
        stdio: 'inherit',
        cwd: getCwd()
      });

      execSync(`git tag -a ${targetVersion} -m "Release version ${targetVersion}"`, {
        stdio: 'inherit',
        cwd: getCwd()
      });

      logger.info(`The version ${targetVersion} has been successfully released.`);
    }
  });
};
