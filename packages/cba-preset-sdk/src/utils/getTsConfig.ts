/* eslint-disable @typescript-eslint/no-require-imports */
import fs from 'fs';
import configuration from '../configuration';

export default function getTsConfig() {
  const tsConfigFilePath = configuration.getPath('tsconfig.json');

  let compilerOptions: any = {};

  if (fs.existsSync(tsConfigFilePath)) {
    // eslint-disable-next-line import/no-dynamic-require, @typescript-eslint/no-var-requires
    compilerOptions = require(tsConfigFilePath)?.compilerOptions || {};
  }

  return Object.assign(
    {
      noUnusedParameters: true,
      noUnusedLocals: true,
      strictNullChecks: false,
      target: 'es6',
      jsx: 'preserve',
      moduleResolution: 'node',
      declaration: true,
      allowSyntheticDefaultImports: true
    },
    compilerOptions
  );
}
