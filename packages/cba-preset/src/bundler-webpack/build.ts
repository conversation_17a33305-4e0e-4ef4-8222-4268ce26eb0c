/**
 * @file webpack build
 * <AUTHOR> (<EMAIL>)
 */
import path from 'path';
import webpack from 'webpack';
import fs from 'fs-extra';
import {Env} from '@baidu/cba-core';
import {logger} from '@baidu/cba-utils';
import {getConfig} from './config/config';
import {mergeUserConfig} from './mergeUserConfig';

interface IOpts {
  env: Env;
  cwd: string;
  userConfig: Record<string, any>;
  chainWebpack?: Function;
  modifyWebpackConfig?: Function;
  onBeforeBundlerStart?: Function;
  onBundlerFinished?: Function;
}

export async function build(opts: IOpts) {
  let webpackConfig = await getConfig({
    env: opts.env,
    cwd: opts.cwd,
    userConfig: opts.userConfig,
    chainWebpack: opts.chainWebpack,
    modifyWebpackConfig: opts.modifyWebpackConfig
  });

  // 合并用户配置
  webpackConfig = mergeUserConfig(webpackConfig, opts.userConfig);

  // 开始构建
  logger.info(`开始构建`);

  const outPath = path.join(opts.cwd, 'dist'); // output目录路径
  try {
    // 移除旧产物
    if (fs.existsSync(outPath)) {
      await fs.remove(outPath);
    }
    if (opts.onBeforeBundlerStart && typeof opts.onBeforeBundlerStart === 'function') {
      await opts.onBeforeBundlerStart({
        config: webpackConfig,
        outPath
      });
    }
    webpack(webpackConfig, async (err, stats) => {
      if (err) {
        console.log(err);
      }
      if (err || (stats && stats.hasErrors())) {
        logger.error('编译失败');
        stats &&
          console.log(
            stats.toString({
              chunks: true,
              colors: true
            })
          );
        return process.exit(1);
      }
      // 构建完成后的事件回调
      if (opts.onBundlerFinished) {
        await opts.onBundlerFinished({
          config: webpackConfig,
          outPath,
          stats
        });
      }

      logger.info('构建完成！输出目录：', outPath);
    });
  } catch (error) {
    console.error(error);
  }

  process.on('SIGINT', () => {
    console.log('结束进程');
    process.exit(0);
  });
}
