/**
 * @file webpack build配置
 * <AUTHOR> (<EMAIL>)
 */

import path from 'path';
import fs from 'fs';
import {IApi, Env} from '@baidu/cba-preset';
import fsExtra from 'fs-extra';
import {getLocalAmisVersion} from '@baidu/cba-utils';

/**
 * 将 amis sdk 拷贝到 构建后的文件夹中
 */
function copyLocalAmisSdkToDist(distDir: string = 'dist') {
  const amisSdkPathPrefix = fs.existsSync('node_modules/amis/sdk/sdk.js') ? '' : '../../';
  const version = getLocalAmisVersion();
  return Promise.all(
    [
      {from: amisSdkPathPrefix + 'node_modules/amis/lib', to: `${distDir}/amis@${version}/lib`},
      {from: amisSdkPathPrefix + 'node_modules/amis/sdk', to: `${distDir}/amis@${version}/sdk`},
      {from: amisSdkPathPrefix + 'node_modules/amis/package.json', to: `${distDir}/amis@${version}/package.json`}
    ].map(item => fsExtra.copy(item.from, item.to))
  );
}

export default (api: IApi) => {
  api.describe({
    key: 'preset-pagemaker-react:webpack-build',
    enableBy: () => api.env === Env.production
  });

  api.modifyWebpackConfig((config, {merge}) => {
    config = merge(config, {
      entry: './hidden-bootstrap.tsx',
      output: {
        path: path.resolve(api.cwd, 'dist'),
        filename: 'bootstrap.js',
        clean: true
      },
      cache: {
        type: 'filesystem',
        allowCollectingMemory: true
      }
    });

    return config;
  });

  api.onBundlerFinished(async ({config, outPath}) => {
    if (!api.userConfig.remoteAmisSdk) {
      await copyLocalAmisSdkToDist();
    }
  });
};
