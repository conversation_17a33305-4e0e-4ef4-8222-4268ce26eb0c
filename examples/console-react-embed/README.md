# 百度智能云 React 工程项目模板

## 写在前面

本仓库是基于 **react17 + wepack5 + typescript + acud** 实现的一个前端工程化项目模板， 与脚手架、组件库和 SDK 等项目存在本质区别。

我们的目标是为开发者（不限于云控制台业务）提供**一套现代化的、高性能的、可定制的、易用的、高度透明且全方位的通用场景开发解决方案**。

我们希望每位开发者不仅能在本模板中寻找到解决通用业务问题的答案，还能获得更出色的开发体验，从而能够更高效地支持产品需求的快速迭代。



> 代码仓库地址：https://console.cloud.baidu-int.com/devops/icode/repos/baidu/baiducloud/bce-react-template/tree/main
>
> 详细文档可查阅：https://now.bdstatic.com/stash/v1/6223f5e/bce-react-template/5472950/docs/README.pdf



## 核心功能

**React 工程项目模板主要功能和特性如下：**

1. **代码规范检查与自动格式化：**使用 `eslint`、`prettier`、`editorconfig`  进行代码规范检查和自动格式化，支持 react 组件、react hooks、typescript、javascript 通用校验规则，并支持自定义扩展规则；
2. **TypeScript 支持：**模板内置集成 `typescript`， 预定义 less、svg 和 png 等文件模块；
3. **透明式配置：**使用 webpack 5 进行项目构建，内置 css、less、 less module、图片及字体资源、svg 组件式引用、 jsx/tsx、等文件处理规则，集成 `babel` 和 `postcss` 满足 js 和 css 浏览器兼容性，对生产环境下的构建时内置优化逻辑，所有配置均透明展示给开发者，完全支持定制化修改；
4. **性能提升**：使用 webpack plugin `压缩` js 和 css、image 图片体积，并内置 acurd `按需打包 ` 逻辑，支持 `区分图片资源` 在公有云和私有化交付场景的构建逻辑，在公有云场景下加载 CDN 地址，私有化交付时将图片等资源打包进 js或单独输出至静态资源目录中，有效提升前端加载速度；
5. **路由懒加载：**支持路由文件懒加载，支持 chunks 拆分，有效减小构建体积，提升首屏加载速度；
6. **内置 axios 请求器：**使用 axios 进行 API 接口请求，`请求拦截器` 中增加 csrftoken header ， `响应拦截器` 中进行全局右上角 toast `接口报错提示`，用户可根据实际需求自定义修改 axios 拦截器；
7. **统一的项目结构：**合理拆分项目基础目录结构，包括 `apis`（接口）、`assets`（图片等静态资源）、`components`（全局组件）、`contexts`（自定 context & provider）、`hooks` (自定义 hook)、`pages` (产品页面)、`styles`（全局样式）、`types`（类型定义）、`utils`（工具方法） 等；
8. **页面模板：**提供`列表页`、`开通页`、`概览页`等基础页面模板，开发者可根据实际需求进行参考研发，有效提升研发效率；
9. **云控制台 Layout：**提供 `AppLayout` ，支持用户定义菜单的数据结构（可参考 src/pages/index.tsx 文件）, 自动渲染左侧菜单和右侧页面区域，并支持左侧菜单的展开和收起；
10. **应用级状态管理：**提供 `AppContext` 和 `AppProvider` ，结合reducer 支持用户定义、存储和使用应用级全局数据，如产品是否开通、开通参数等等，AppLayout 内置开通控制逻辑，当用户未开通时仅可访问开通页面，且显示左侧菜单，菜单不可点击、路由不可切换，解决`产品开通`此类通用场景问题；
11. **Framework 数据管理：**提供 `FrameworkContext` 和 `FrameworkProvider` ，在应用初始化时存储 framework 中的数据，并支持在任意 react 组件中快捷获取用户是否实名 `isRealNameVerified`,  用户ID `userId` 等数据；
12. **Region 获取与设置**：提供 `useRegion` hook ，可快捷获取当前 region ，监听 region 变换和主动设置 region；
13. **研发体验提升：**
    - 通过 `AppContext` 和 `AppLayut` 内置的功能，**开发者仅关注实际产品页面的研发内容即可，方便快速拓展产品功能**，提升产品开发效率；

    - 内置支持通过 `npm run dev:sandbox` 和 `npm run dev:online` 在本地进行云沙盒环境及线上环境研发调试；

    - 支持在 `app.config.json5` 文件中设置产品名称 `appName` , 根据 `appName` 会自动生成产品访问的 pathname  和 用于 `AMD`  加载的模块名称；

    - 支持在 `app.config.json5`  文件中设置运行端口 `port` ， 且 webpack 启动 devServer 时会根据配置的端口进行占用扫描，如果当前端口不可用，会动态分配新端口，防止端口冲突导致服务报错；

    - 支持在 `app.config.json5`  文件中设置 `appTitle`, 自动更新设置页面 head 中的 `title` 标签；

    - 支持文件保存热更新，使用 `@pmmmwh/react-refresh-webpack-plugin` 满足 react 组件热更新需求，有效提升开发体验；

    - typescript 和 webpack 内置配置，支持` "@"` 路径别名使用和智能提示；
14. **场景支持：**
    - 支持作为一个 `第三方模块`，嵌入显示到 `SDP` 或 `EDP` 技术框架中，并具备良好的渲染效果和本地环境调试开发体验。



## 如何使用

前文中已提到本项目与脚手架、组件库或SDK存在本质区别，我们仅向开发者用户提供基础工程项目模板和通用场景解决方案，无需进行安装。

新产品可直接克隆当前模板进行项目初始化，用户仅需要关注 `app.config.json5` 文件中的 `appName` 、`appTitle` 等配置参数根据各自产品进行调整。

> 🔔  下载项目模板之后：
>
> - 可删除 `@/pages` 目录下面的路由界面组件，新增页面功能时，仅需要关注在 `@/pages/index.tsx` 进行页面注册即可；
>
> - 可删除 `@/apis` 目录下面的 api 文件；
>
> - 可删除 `@/assets` 中的 `png` 和 `svg` 文件；
>
> - 可删除 `@/components` 目录下除 `basic` 目录中的其它所有组件目录；
>
> - 可删除 `@/styles` 目录下的 `dbsc.less` 文件，其它 `.less` 文件建议保留；
>
> - `@/utils` 目录下的 `request.tsx` 文件建议不要修改，其它文件可任意变更；



当模板内容功能无法完全满足产品需求时，可自定义调整。当项目模板新增或修改通用场景解决方案时，开发者可参考 `更新日志` 中的内容自行对各自产品项目进行更新升级。



相关运行命令如下：

```shell
# 本地联调沙盒环境
npm run dev:sandbox

# 本地研发线上环境
npm run dev:online

# 生产环境构建
npm run build

# 本地运行项目作为第三方模块
npm run dev:embed

# 作为第三方模块进行生产环境构建
npm run build:embed
```



> 🔔  若本地运行项目之后，浏览器出现 https 链接安全提示时，如下图，请直接键入 `thisisunsafe` ， 浏览器会信任  https 链接并自动刷新。
>
> ![](http://bj.bcebos.com/ibox-thumbnail98/********************************?authorization=bce-auth-v1%2Ffbe74140929444858491fbf2b6bc0935%2F2024-01-22T08%3A23%3A20Z%2F1800%2F%2F9725f457430ec343745b048dd1d9de3b4d826c12bd44cf614c38f94c8ff3f33b)



## 更新日志

- 2024年01月23日：新增项目国际化支持；

- 2024年01月17日：新增支持作为第三方模块，集成至 SDP 或 EDP 项目中；

- 2024年01月16日：初始化项目模板，内置核心 webpack 配置和云控制台相关处理逻辑；



## 配置详解

### 代码规范配置

当前项目通过 `eslint`、`prettier`、`editorconfig` 共同完成代码规范检查提示和自动保存格式化。

eslint 核心校验规则主要是通过 `react`、`react-hooks`、`@typescript-eslint` 等 extends 扩展及对应的 plugins 来实现，并由 `eslint-config-prettier` 和 `eslint-plugin-prettier` 等 npm 包来集成 eslint 与 prettier 功能。

另外通过 `eslint-plugin-simple-import-sort` 工具包实现文件中 import 的自动排序功能。



**.eslintrc** 配置如下，开发者可在 `rules` 字段中新增规则限制，具体内容可参考 [eslint 官方文档](https://eslint.org/docs/latest/)

```json
{
  "parser": "@typescript-eslint/parser",
  "env": {
    "node": true,
    "browser": true,
    "commonjs": true,
    "es2021": true
  },
  "extends": [
    "plugin:@typescript-eslint/recommended",
    "plugin:react/recommended",
    "plugin:react-hooks/recommended",
    "eslint:recommended",
    "plugin:prettier/recommended"
  ],
  "plugins": [
    "@typescript-eslint",
    "react",
    "react-hooks",
    "prettier",
    "simple-import-sort"
  ],
  "globals": {
    "APP_TITLE": "readonly",
    "__webpack_public_path__": "writable"
  },
  "rules": {
    "object-curly-spacing": ["error", "never"],
    "comma-dangle": ["error", "never"],
    "simple-import-sort/imports": "error",
    "@typescript-eslint/no-explicit-any": "off",
    "no-unused-vars": "off",
    "@typescript-eslint/no-unused-vars": ["error"]
  }
}
```



**.prettierrc** 配置如下，具体配置可参考 [prettier 官方文档](https://prettier.io/docs/en/)

```json
{
  "tabWidth": 2,
  "singleQuote": true,
  "semi": true,
  "printWidth": 80,
  "bracketSpacing": false,
  "trailingComma": "none"
}
```



**.editorConfig** 配置如下，具体配置可参考 [EditorConfig 官方文档](https://editorconfig.org/)

```shell
root = true
[*]
charset = utf-8
end_of_line = lf
indent_style = space
indent_size = 2
insert_final_newline = true
trim_trailing_whitespace = true
```



为了保持项目代码规范的一致性及开启保存自动格式化，在 `.vscode/settings.json` 文件中需要配置如下内容，且推荐不要在 .gitignore 文件中忽略 .vscode 文件夹。

```json
{
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit"
  },
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "rvest.vs-code-prettier-eslint"
}
```



> 🔔 注意：请在 vscode 中安装以下插件来保证上述配置的生效。
>
> ![](http://bj.bcebos.com/ibox-thumbnail98/********************************?authorization=bce-auth-v1%2Ffbe74140929444858491fbf2b6bc0935%2F2024-01-15T06%3A21%3A16Z%2F1800%2F%2Fde18ec44f48fa8e62e2d015a51e9aac116f9fb501b7c4a0bef579e5b395af252)
>
> ![](http://bj.bcebos.com/ibox-thumbnail98/********************************?authorization=bce-auth-v1%2Ffbe74140929444858491fbf2b6bc0935%2F2024-01-15T06%3A21%3A16Z%2F1800%2F%2Fbeff7bd08a525c6c3883371382cc14e30307ee8af3a87f5c5abb9ec49296ab48)
>
> ![](http://bj.bcebos.com/ibox-thumbnail98/********************************?authorization=bce-auth-v1%2Ffbe74140929444858491fbf2b6bc0935%2F2024-01-15T06%3A21%3A16Z%2F1800%2F%2F4f72348ee9d00aaacf86fe3c2b93f1e6ce80b3732f92a19e682702b486164475)
>
> ![](http://bj.bcebos.com/ibox-thumbnail98/********************************?authorization=bce-auth-v1%2Ffbe74140929444858491fbf2b6bc0935%2F2024-01-15T06%3A21%3A16Z%2F1800%2F%2F518bf2597560d9558ba2b298083db20ba2a8ceaa96d72257eb28e3f155eccbb6)



> 🔔 若上述插件安装完毕，且 npm 包均已下载成功，但是校验规则或自动保存未生效，请重启 vscode 及检查 output 中 eslint、prettier 或 prettier-eslint 输出日志中是否有报错信息：
>
> ![](http://bj.bcebos.com/ibox-thumbnail98/********************************?authorization=bce-auth-v1%2Ffbe74140929444858491fbf2b6bc0935%2F2024-01-15T06%3A32%3A30Z%2F1800%2F%2F2049a0591a850a9989d26ee0c4533bd110e6deeccbe73377f0896288c70cbfab)





### typescript 配置

Tsconfig.json 配置内容如下，了解具体配置项信息可参考 [TypeScript 官方文档](https://www.typescriptlang.org/docs/)

```json
{
  "compilerOptions": {
    "moduleResolution": "Node",
    "baseUrl": "./",
    "paths": {
      "@/*": ["src/*"],
    },
    "target": "ES5",
    "lib": ["DOM", "ES6", "ES2017", "ESNext"],
    "module": "ESNext",
    "jsx": "react",
    "strict": true,
    "esModuleInterop": true,
    "allowUmdGlobalAccess": true
  }
}

```



另外需要在 `@/types/gloabl.d.ts` 中进行部分模块的全局类型定义，具体内容如下：

```ts
declare interface Window {
  $framework: any;
  appPublicPath?: string;
}

declare module '*.less' {
  const resource: {[key: string]: string};
  export = resource;
}

declare module '*.svg' {
  import React from 'react';
  const svg: React.FC<{
    className?: any;
  }>;
  export default svg;
}

declare module '*.png';

declare let APP_TITLE: string;

declare let __webpack_public_path__: string;
```



### webpack loader 配置

请仔细阅读 webpack 的 loader 配置，若配置不满足项目需求，可进行定制化修改。

> 🔔 注意：本项目模板中为了提高编译性能，使用 `oneOf` 来配置模块规则，定制化修改时需要注意此项，避免出现配置不生效的问题。

#### css 文件

css 文件 loader 配置如下：

```javascript
{
  test: /\.css$/,
    use: [
      MiniCssExtractPlugin.loader,
      'css-loader',
      {
        loader: 'postcss-loader',
        options: {
          postcssOptions: {
            plugins: ['postcss-preset-env']
          }
        }
      }
    ]
}
```

> 🔔 样式文件未使用 `style-loader` , 而是使用 `MiniCssExtractPlugin.loader`  将 样式 打包成独立文件，提升页面加载速度，避免首次渲染页面有样式闪烁的问题。



#### less 文件

less 文件 loader 配置如下：

```javascript
{
  test: /\.module\.less$/,
    use: [
      MiniCssExtractPlugin.loader,
      {
        loader: 'css-loader',
        options: {
          modules: {
            localIdentName: '[local]_[hash:base64:5]'
          }
        }
      },
      {
        loader: 'postcss-loader',
        options: {
          postcssOptions: {
            plugins: ['postcss-preset-env']
          }
        }
      },
      'less-loader'
    ]
},
{
  test: /\.less$/,
    use: [
      MiniCssExtractPlugin.loader,
      'css-loader',
      {
        loader: 'postcss-loader',
        options: {
          postcssOptions: {
            plugins: ['postcss-preset-env']
          }
        }
      },
      'less-loader'
    ]
}
```



> 🔔 全局的样式文件请以 `.less` 文件后缀结尾，业务页面和业务组件的样式文件请以 `.module.less` 文件后缀结尾，.module.less 文件打包时会以 css-modules 的形式进行处理，命名规则为 "类名 + 下划线 +  5位 Hash 值"。



#### png/jpeg/gif 图片文件

png/jpeg/gif 图片文件 loader 配置如下：

```javascript
{
  test: /\.(png|jpe?g|gif)$/,
  type: 'asset/inline'
}
```

> 🔔 webpack5 新增 `asset` 模块，替代原有的 `url-loader` 或 `file-loader` ，通过配置 `asset/inline` 可将相关的图片资源转换为 base64 打包到 js 文件中。
>
> 由于当前的部署流程限制，图片资源暂时无法单独打包成静态文件再上传至 CDN，不过可先将图片上传至 BOS 中，再在项目中引用。
>
> 而私有化环境中可将图片资源打包成独立文件，届时需要修改此处配置为：
>
> ```javascript
> {
>   test: /\.(png|jpe?g|gif)$/,
>   type: 'asset',
>     parser: {
>       dataUrlCondition: {
>         maxSize: 10 * 1024
>       }
>     }
> }
> ```



#### svg 文件

svg 文件 loader 配置如下：

```javascript
{
  test: /\.svg$/,
  use: ['@svgr/webpack']
}
```



主要使用 `@svgr/webpack` 工具包对 svg 文件进行处理，支持用户以 react 组件的形式对 svg 进行使用，并可通过设置 `className` 来修改 svg 的样式， 如：

```jsx
import CopyIcon from '@/assets/svg/copy.svg';

export function FuncCompt() {
  return <CopyIcon className={styles.copyIcon} />
}
```



#### jsx/tsx 文件

jsx/tsx 文件 loader 配置如下：

```javascript
{
  test: /\.(jsx|js)$/,
  include: path.resolve(__dirname, '../src'),
  loader: 'babel-loader',
  options: {
    cacheDirectory: true,
    cacheCompression: false,
    plugins: [
      [
        'import',
        {libraryName: 'acud', style: true, libraryDirectory: 'es'},
        'acud'
      ],
      !isProduction && 'react-refresh/babel'
    ].filter(Boolean)
  }
},
{
  test: /\.tsx?$/,
  use: [
    {
      loader: 'babel-loader',
      options: {
        cacheDirectory: true,
        cacheCompression: false,
        plugins: [
          [
            'import',
            {
              libraryName: 'acud',
              style: true,
              libraryDirectory: 'es'
            },
            'acud'
          ],
          !isProduction && 'react-refresh/babel'
        ].filter(Boolean)
      }
    },
    'ts-loader'
  ],
  exclude: /node_modules/
}
```



> 🔔  在 jsx 和 tsx 的 loader 配置中，均增加了对 `acud` 的按需打包逻辑，并对 Babel-loader 开启了缓存配置，来提升编译性能。
>
> 在非生产环境下配置 `react-refresh/babel` 来启用 webpack react 组件热更新功能。
>
> 生产环境的判断条件如下：
>
> ```javascript
> const isProduction = process.env.NODE_ENV === 'production';
> ```



### webpack plugin 配置

#### CopyPlugin

```javascript
const CopyPlugin = require('copy-webpack-plugin');

{
  "plugins": [
    new CopyPlugin({
      patterns: [
        {
          from: path.resolve(__dirname, '../public'),
          to: path.resolve(__dirname, '../dist'),
          toType: 'dir',
          noErrorOnMissing: true,
          globOptions: {
            ignore: ['**/*.html']
          },
          info: {
            minimized: true
          }
        }
      ]
    }),
  ]
}
```



> 🔔  `CopyPlugin` 会将 public 目录下的文件拷贝到打包之后的 dist 目录中，注意在 `globOptions` 中的 `ignore` 配置选项会忽略所有的 `.html` 文件，`.html` 文件会由 `HtmlWebpackPlugin` 进行处理。



#### ProgressPlugin

```javascript
const config = json5.parse(
  fs.readFileSync(path.resolve(__dirname, '../app.config.json5'))
);

const isInPipeline = process.env.BUILD_ENV === 'pipeline';

{
  "plugins": [
    config.enableWebpackProgressPlugin &&
      !isInPipeline &&
      new webpack.ProgressPlugin({
        activeModules: false,
        entries: true,
        modules: true,
        modulesCount: 5000,
        profile: true,
        dependencies: true,
        dependenciesCount: 10000,
        percentBy: 'entries'
      })
  ]
}
```

> 🔔  `ProgessPlugin` 会根据 `app.config.json5` 文件中的 `enableWebpackProgressPlugin` 配置来判断是否启用，默认启用，启用之后可以详细查看 webpack 的构建流程及进度。关闭之后，可提升 webpack 的打包速度，提升效果随着项目规模变化，若感觉打包速度较慢，可在 `app.config.json5` 文件中将 `enableWebpackProgressPlugin` 设置为 `false` 进行关闭。
>
> 另外还会根据 `BUILD_ENV` 来判断是否在流水线中执行构建，如果是在流水线中，则默认不开启，能够提升流水线编译速度。



#### MiniCssExtractPlugin

```javascript
const MiniCssExtractPlugin = require('mini-css-extract-plugin');

{
  "plugins": [
    new MiniCssExtractPlugin({
      filename: 'static/css/main.css'
    })
  ]
}
```

> 🔔  `MiniCssExtractPlugin` 结合前面设置的 MiniCssExtractPlugin.loader 可以将样式单独打包到独立的 css 样式文件中，与 js 分别进行加载，可以提升项目加载速度，避免首页渲染样式闪烁问题，生产环境的 css 文件的加载可具体参考后面的 `部署流程`。



#### DefinePlugin

```javascript
{
	"plugins": [
    new webpack.DefinePlugin({
      APP_TITLE: JSON.stringify(config.appTitle || '百度智能云')
    })
  ]
}
```

> 🔔 `DefinePlugin` 主要用于在编译时，替换项目中定义的一些全局常量，`app.config.json5` 中的页面 title 设置就是借用这个插件进行实现的， 需要注意的是定义的全局常量的值需要通过 JSON.stringify 进行序列化。



#### 开发环境 Plugin

在 dev 环境中，需要配置 `ReactRefreshWebpackPlugin` 和 `HtmlWebpackPlugin` ，具体可参考 `config/webpack.dev.config.js` 文件：

```javascript
const ReactRefreshWebpackPlugin = require('@pmmmwh/react-refresh-webpack-plugin');
const HtmlWebpackPlugin = require('html-webpack-plugin');

const config = json5.parse(
  fs.readFileSync(path.resolve(__dirname, '../app.config.json5'))
);
const moduleName = `${config.appName}AppModule`;

{
  "plugins": [
    new ReactRefreshWebpackPlugin(),
    new HtmlWebpackPlugin({
      moduleName,
      template: path.resolve(__dirname, '../public/local.html')
    })
  ]
}
```

> 🔔  `ReactRefreshWebpackPlugin `  是用于实现 react 组件的热更新。注意 `HtmlWebpackPlugin`  中加载的页面模板是 `local.html` 文件，`local.html` 文件主要用于本地开发时进行加载，具体内容如下：
>
> ```html
> <!DOCTYPE html>
> <html lang="en">
>   <head>
>     <meta charset="UTF-8" />
>     <meta name="viewport" content="width=device-width, initial-scale=1.0" />
>     <script
>       type="text/javascript"
>       src="https://bce.bdstatic.com/ecom/esl/2.2.0-rc.3/esl.js"
>     ></script>
>     <script
>       type="text/javascript"
>       src="https://bce.bdstatic.com/console/fe-framework/loadjs.js"
>     ></script>
>     <title>百度智能云-数据库智能驾驶舱 DBSC</title>
>   </head>
>   <body>
>     <div id="root-app-container"></div>
>
>     <script type="text/javascript">
>       require(['framework'], function (framework) {
>         window.$framework = framework;
>         framework.boot().then(function (initData) {
>           require([
>             '<%= htmlWebpackPlugin.options.moduleName %>'
>           ], function (app) {
>             app.bootstrap(initData);
>           });
>         });
>       });
>     </script>
>   </body>
> </html>
>
> ```



#### 生产环境 Plugin

```javascript
const CssMinimizerPlugin = require('css-minimizer-webpack-plugin');

const config = json5.parse(
  fs.readFileSync(path.resolve(__dirname, '../app.config.json5'))
);
const moduleName = `${config.appName}AppModule`;

{
  "plugins": [
    new CssMinimizerPlugin(),
    new HtmlWebpackPlugin({
      moduleName,
      appName: config.appName,
      template: path.resolve(__dirname, '../public/index.html'),
      inject: false
    })
  ],
}
```

> 🔔  `CssMinimizerPlugin` 主要用于在生产环境下，对 css 文件进行压缩，减小代码体积，提升加载速度。
>
> 生产环境下的 `HtmlWebpackPlugin` 中的 `inject` 需要设置成 `false` ， 打包之后的 js 和 css 文件的加载流程可详细参考 `部署流程` 中的 `index.html` 介绍。





### 路径别名配置

项目模板支持 "@" 路径别名使用和引用智能提示，主要通过修改 webpack resolve 和 tsconfig.json 文件来实现。

webpack 配置如下：

```javascript
resolve: {
  extensions: ['.tsx', '.jsx', '.ts', '.js', 'json'],
  alias: {
    '@': path.resolve(__dirname, '../src')
  }
}
```



tsconfig.json 配置如下：

```json
{
  "compilerOptions": {
    "paths": {
      "@/*": ["src/*"],
    }
  }
}
```



### devServer 配置

```javascript
const {merge} = require('webpack-merge');
const webpackBaseConfig = require('./webpack.base.config');
const fs = require('fs');
const path = require('path');
const json5 = require('json5');
const portfinder = require('portfinder');
const ReactRefreshWebpackPlugin = require('@pmmmwh/react-refresh-webpack-plugin');
const HtmlWebpackPlugin = require('html-webpack-plugin');

const isOnline = process.env.DEV_ENV === 'online';

const devServerHost = isOnline
  ? 'localhost.console.bce.baidu.com'
  : 'localhost.qasandbox.bcetest.baidu.com';

const devProxyTarget = isOnline
  ? 'https://console.bce.baidu.com'
  : 'https://qasandbox.bcetest.baidu.com';

const config = json5.parse(
  fs.readFileSync(path.resolve(__dirname, '../app.config.json5'))
);

const port = config.port || 8899;

const pathname = `/${config.appName}/`;
const moduleName = `${config.appName}AppModule`;

module.exports = () => {
  return new Promise((resolve) => {
    portfinder.getPort({port}, (_, port) => {
      resolve(
        merge(webpackBaseConfig, {
          mode: 'development',
          devtool: 'cheap-module-source-map',
          output: {
            filename: 'static/js/[name].js',
            chunkFilename: 'static/js/[name].chunk.js',
            assetModuleFilename: 'static/js/[hash:10][ext][query]',
            publicPath: pathname,
            libraryTarget: 'amd',
            library: moduleName
          },
          devServer: {
            open: [pathname],
            host: devServerHost,
            historyApiFallback: true,
            port,
            hot: true,
            compress: true,
            https: true,
            client: {
              progress: false,
              reconnect: 5
            },
            proxy: {
              '/api': {
                target: devProxyTarget,
                changeOrigin: true,
                secure: false,
                onProxyReq(proxyReq) {
                  proxyReq.setHeader('origin', devProxyTarget);
                }
              }
            }
          },
          plugins: [
            new ReactRefreshWebpackPlugin(),
            new HtmlWebpackPlugin({
              moduleName,
              template: path.resolve(__dirname, '../public/local.html')
            })
          ]
        })
      );
    });
  });
};
```

>🔔  dev 环境的 webpack 主要注意事项如下：
>
>- 通过 `json5` 工具包读取 `app.config.json5` 配置文件中的内容，获取 `appName`、`port` 等信息；
>- 通过 `portfinder` 工具包来进行端口占用扫描，如果当前端口不可用，会动态分配新端口，防止端口冲突导致服务报错；
>- 在 `devServer` 中根据 `DEV_ENV` 环境变量，来判断自动打开的浏览器访问地址以及 api 代理的目标地址;
>- 通过设置 `publicPath: pathname` 和  `historyApiFallback: true`  来保证本地访问的地址中的产品短路径与实际环境一致；
>- 通过设置 `https: true` 和  `secure: false` 来保证通过 https 协议访问沙盒环境和线上环境时均能成功，若不设置 `secure: false` ，则无法正常访问线上环境。



### 生产环境配置

```javascript
/* eslint-disable @typescript-eslint/no-var-requires */
const {merge} = require('webpack-merge');
const webpackBaseConfig = require('./webpack.base.config');
const TerserPlugin = require('terser-webpack-plugin');
const isInPipeline = process.env.BUILD_ENV === 'pipeline';
const CssMinimizerPlugin = require('css-minimizer-webpack-plugin');
const fs = require('fs');
const path = require('path');
const json5 = require('json5');
const HtmlWebpackPlugin = require('html-webpack-plugin');

const config = json5.parse(
  fs.readFileSync(path.resolve(__dirname, '../app.config.json5'))
);
const moduleName = `${config.appName}AppModule`;

module.exports = merge(webpackBaseConfig, {
  mode: 'production',
  ...(isInPipeline ? {performance: false} : {}),
  output: {
    filename: 'static/js/[name].js',
    chunkFilename: 'static/js/[name].chunk.js',
    libraryTarget: 'amd',
    library: moduleName,
    publicPath: '',
    clean: true
  },
  plugins: [
    new CssMinimizerPlugin(),
    new HtmlWebpackPlugin({
      moduleName,
      appName: config.appName,
      template: path.resolve(__dirname, '../public/index.html'),
      inject: false
    })
  ],
  optimization: {
    minimizer: [
      new TerserPlugin({
        parallel: true
      })
    ]
  }
});
```

> 🔔 在生产环境构建时，会判断 `BUILD_ENV` 环境变量是否开启 `performance`， 如果是在流水线中进行编译，则关闭 `performance` , 可提升流水线中的打包速度。
>
> 当 webpack5 设置 mode 为 production 时，会自动开启一系列优化配置，上述文件中仅在 `optimization` 中设置 `TerserPlugin` 的 `paraller` 为 true , 可提升构建速度。



### app.config.json5

`app.config.json5` 目前具体的配置内容如下：

| 参数名                      | 实例值                          | 参数说明                                                     |
| --------------------------- | ------------------------------- | ------------------------------------------------------------ |
| appName                     | dbsc                            | 影响产品默认访问的路径: /dbsc/, 影响 webpack amd 构建的模块名称 dbscAppModule，需要重点关注 appName 的赋值，不要以 / 开头和结尾。 |
| port                        | 8899                            | 本地项目运行端口, 端口有冲突时，项目会动态分配端口           |
| appTitle                    | 百度智能云-数据库智能驾驶舱DBSC | 页面 title                                                   |
| enableWebpackProgressPlugin | true                            | 是否开启 webpack progress ，默认显示，关闭之后可提升编译性能 |



### package.json 配置

`package.json` 文件中主要关注的是 `scripts` 配置：

```json
{
    "dev": "npm run dev:online",
    "dev:sandbox": "cross-env NODE_ENV=development DEV_ENV=sandbox webpack serve --config config/webpack.dev.config.js",
    "dev:online": "cross-env NODE_ENV=development DEV_ENV=online webpack serve --config config/webpack.dev.config.js",
    "build": "cross-env NODE_ENV=production webpack --config config/webpack.prod.config.js"
}
```

> 🔔  通过 `cross-env` 工具包来设置 `NODE_ENV` 和 `DEV_ENV` 两个环境变量，来判断项目构建环境和本地开发时区分代理支线上环境或沙盒环境。



## 功能详解

### FrameworkContext

在 `src/index.tsx` 文件中，监听 `framework` 加载完成之后，将 `initData` 传递至 `FrameworkProvider` 中，示例代码如下：

```tsx
require(['framework'], function (framework) {
  window.$framework = framework;
  framework.boot().then(function (initData) {
    require([
      '<%= htmlWebpackPlugin.options.moduleName %>'
    ], function (app) {
      app.bootstrap(initData);
    });
  });
});

export function bootstrap(initData: any) {
  render(
    <StrictMode>
      <FrameworkProvider frameworkData={initData}>
        <AppProvider>
          <App />
        </AppProvider>
      </FrameworkProvider>
    </StrictMode>,
    document.querySelector('#main')
  );
}
```



在 `FrameworkProvider` 中，对 `initData` 进行读取，并将 用户是够完成实名认证，用户ID 等信息挂载到 `context` 中，其它常用的 framework 中的全局数据可参考如下形式进行处理，示例代码如下：

```tsx
const FrameworkProvider: React.FC<FrameworkProviderProps> = ({
  frameworkData,
  children
}) => {
  /** 当前用户是否完成实名认证 */
  const isRealNameVerified = useMemo(() => {
    return frameworkData?.constants?.verifyUser?.verifyStatus === 'PASS';
  }, [frameworkData?.constants?.verifyUser?.verifyStatus]);

  /** 当前的用户ID */
  const userId = useMemo(() => {
    return frameworkData?.constants?.userid;
  }, [frameworkData?.constants?.userid]);

  return (
    <FrameworkContext.Provider
      value={{frameworkData, isRealNameVerified, userId}}
    >
      {children}
    </FrameworkContext.Provider>
  );
};

export default FrameworkProvider;
```



在 react 组件中进行使用，示例代码如下：

```tsx
import {FrameworkContext} from '@/contexts/FrameworkContext';

export function FuncCompt() {
	const {isRealNameVerified, useId, frameworkData} = useContext(FrameworkContext);
}
```



### AppContext

项目模板提供 `AppContext` 和 `AppProvider` ，结合reducer 支持用户定义、存储和使用应用级全局数据，如产品是否开通、开通参数等等，AppLayout 内置开通控制逻辑，当用户未开通时仅可访问开通页面，且显示左侧菜单，菜单不可点击、路由不可切换，解决`产品开通`此类通用场景问题；

🔔  **其它应用级的全局状态也推荐统一挂载到此处。**

```tsx
interface State {
  /** 产品是否激活 */
  isActivated: boolean;
  /** 接口是否加载完成 */
  isPreRequestCompleted: boolean;
  /** 服务参数, 用于产品激活 */
  serviceParams: Array<{
    roleName: string;
    policyId: string;
    serviceId: string;
    /** 是否激活 */
    isActivated?: boolean;
  }>;
  /** 菜单是否收起 */
  isMenuCollapsed?: boolean;
}

const initialState: State = {
  isActivated: false,
  isPreRequestCompleted: false,
  serviceParams: [],
  isMenuCollapsed: false
};

const reducer = (state: State, action: Action) => {
  switch (action.type) {
    // 激活产品
    case 'ACTIVATE_PRODUCT': {
      // 通过设置 sessionStrage 可以用于在账号已开通的场景下调试开通页
      if (sessionStorage.getItem('_bce_activation_')) {
        return {
          ...state,
          isActivated: false
        };
      }
      return {
        ...state,
        isActivated: true
      };
    }
    // 展开或收起左侧菜单
    case 'TOGGLE_MENU': {
      return {
        ...state,
        isMenuCollapsed: !state.isMenuCollapsed
      };
    }
    default: {
      return state;
    }
  }
};

const AppContext = createContext<
  | {
      appState: State;
      appDispatch: React.Dispatch<Action>;
    }
  | undefined
>(undefined);

interface AppProviderProps {
  children: ReactNode;
}

const AppProvider: React.FC<AppProviderProps> = ({children}) => {
  const [state, dispatch] = useReducer(reducer, initialState);

  return (
    <AppContext.Provider value={{appState: state, appDispatch: dispatch}}>
      {children}
    </AppContext.Provider>
  );
};

export {AppContext, AppProvider};
```



> 🔔 `AppContext` 上挂载的 `appState` 是通过 `useReducer` 创建的全局状态容器，在 react 组件中可以通过  `appDisptach` 来修改 `appState` 中的值，从而重新渲染组件。
>
> 注意在 dispatch `ACTIVATE_PRODUCT` 时，增加了一个灰度机制，可以通过设置 `sessionStorage.setItem('_bce_activation_', true)` 来用于在账号已开通的场景下调试开通页。
>
> `AppContext` 具体使用方式，示例代码如下：
>
> ```tsx
> import {AppContext} from '@/contexts/AppContext';
>
> export function FuncCompt() {
>   const {appState, appDispatch} = useContext(AppContext)!;
>
>   useEffect(() => {
>     appDispatch({
>       type: 'SET_SERVICE_PARAMS',
>       payload: {
>         serviceParams
>       }
>     });
>   }, [])
>
>   return <div>{appState.xxx}</div>
> }
> ```



### useRegion

项目模板提供 `useRegion` 自定义 hook 来进行 `region` 信息的读取、变化监听和设置。

useRegion 具体实现实例代码如下：

```tsx
export function useRegion(initialValue?: InitialValue) {
  /** 支持传入监听 region 变化的方法，在 region 变化时进行回调 */
  const {onRegionChange} = initialValue || {};
  const [currentRegion, setCurrentRegion] = useState<Region>(
    window.framework.region.getCurrentRegion()
  );

  /** 获取 region 值，如: bj */
  const region = useMemo(() => {
    return currentRegion?.id;
  }, [currentRegion?.id]);

  /** 处理 region 变换 */
  const handleRegionChange = useCallback(() => {
    const value = window.framework.region.getCurrentRegion();
    setCurrentRegion(value);
    onRegionChange?.(value?.id);
  }, [onRegionChange]);

  /** 设置 region */
  const setRegion = useCallback((region: string) => {
    window.framework.region.setRegion(region);
  }, []);

  useEffect(() => {
    /** 监听 region 变化 */
    window.framework.events.on(
      FrameworkEvents.AFTER_REGION_CHANGED,
      handleRegionChange
    );

    return () => {
      /** 卸载监听 region 变化 */
      window.framework.events.un(
        FrameworkEvents.AFTER_REGION_CHANGED,
        handleRegionChange
      );
    };
  }, [handleRegionChange]);

  return {currentRegion, region, setRegion};
}
```



具体使用示例代码如下：

```tsx
import {useRegion} from '@/hooks/useRegion';

export function FuncCompt() {
  // 监听 region 变化
  const onRegionChange = useCallback((region) => {}, []);

	const {region, setRegion} = useRegion({onRegionChange});
}
```



### useDocumentTitle

项目模板提供  `useDocumentTitle` 自定义 hook 来实现页面 title 的修改，具体实现代码如下：

```tsx
export function useDocumentTitle(title?: string) {
  useEffect(() => {
    document.title = title || APP_TITLE;
  }, [title]);
}
```



> 🔔  `APP_TITLE` 的取值，来自于 `app.config.json5` 的 `appTitle` 字段，并通过 webpack 的 `DefinePlugin` 在编译时进行替换。
>
> 注意，在 DefinfePlugin 中定义的替换常量，推荐以 `APP_` 开头进行命名。
>
> 另外，需要在 `.eslintrc` 中配置 `globals` 字段来避免 eslint 报错，具体配置如下：
>
> ```json
> "globals": {
>   	"APP_TITLE": "readonly"
> }
> ```



### AppLayout

`AppLayout` 具体实现可参考 `@/components/Applout/index.tsx` 代码文件。

用户仅需要关注在 `@/pages/index.tsx` 文件中进行菜单注册即可，示例代码如下：

```tsx
const Activation = React.lazy(
  () => import(/* webpackChunkName: "Activation" */ '@/pages/Activation')
);

const AuditReport = React.lazy(
  () => import(/* webpackChunkName: "AuditReport" */ '@/pages/AuditReport')
);
export interface MenuItem {
  /** 菜单名称 */
  menuName: string;
  /** 菜单 key */
  key: string;
  /** 是否默认展开 */
  isDefaultOpened?: boolean;
  /** 是否为导航菜单 */
  isNavMenu?: boolean;
  /** 是否为顶部导航栏类型  */
  isHeaderNav?: boolean;
  /** 子菜单列表 */
  children?: MenuItem[];
  /** 子节点菜单是否包含顶部导航类菜单 */
  hasHeaderMenu?: boolean;
  /** 顶部组件 */
  Header?: React.FC<any>;
  /** 对应的页面组件 */
  Component?: ComponentType<any>;
  /** 子标题 */
  renderSubtitle?: FunctionComponent<any>;
}

/** 菜单定义 */
const menus: MenuItem[] = [
  {
    menuName: '开通页',
    key: urls.activation,
    isNavMenu: true,
    Component: Activation,
    isPageWrapperNotRequired: true
  },
  {
    menuName: '二级菜单',
    key: '/secure/audit',
    isNavMenu: true,
    isDefaultOpened: false,
    children: [
      {
        menuName: '列表页',
        key: urls.auditReport,
        isNavMenu: true,
        Component: AuditReport
      }
    ]
  }
];

export default menus;
```



`AppLayout` 会自动根据上述配置渲染页面框架，实现自动渲染左侧菜单和右侧页面区域，并支持左侧菜单的展开和收起，**开发者可快速拓展产品功能，且仅关注实际页面需求研发内容即可**，提升产品开发效率，具体效果如下：

![](http://bj.bcebos.com/ibox-thumbnail98/********************************?authorization=bce-auth-v1%2Ffbe74140929444858491fbf2b6bc0935%2F2024-01-15T08%3A55%3A16Z%2F1800%2F%2Fd3900d03058eaf96596072b53a663f3a8ee5a646053afcd202fa0cde8fcf84a0)



另外项目模板内置**`产品开通`**逻辑，并判断用户未开通时，无法点击菜单或手动更改路由去访问其它页面，且提供开通页模板如下，开发者可根据产品实际需求进行调整适配即可：

![](http://bj.bcebos.com/ibox-thumbnail98/********************************?authorization=bce-auth-v1%2Ffbe74140929444858491fbf2b6bc0935%2F2024-01-16T02%3A13%3A50Z%2F1800%2F%2F4c5876ccdc889b5c2ff254787e2f6b7e2e4f03821c37e66f7bf1eb9e09ed7c46)



### API Request

axios 实例化：

```tsx
const request: AxiosInstance = axios.create({
  baseURL: '',
  timeout: 5000
});
```



axios 扩展请求参数支持 `静默请求`：

```tsx
declare module 'axios' {
  interface AxiosRequestConfig {
    /** 开启静默请求 */
    silent?: boolean;
  }
}
```



axios 请求拦截器：

```tsx
/** 请求拦截器 */
request.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    config.headers['csrftoken'] = Cookies.get('bce-user-info');
    return config;
  },
  (err) => Promise.reject(err)
);
```



axios 响应拦截器，请求错误 toast 提示和 MFA 二次验证，均在此处处理：

```tsx
/** 响应拦截器 */
request.interceptors.response.use(
  (response: AxiosResponse) => {
    const {status, data, headers, config} = response;

    if (config.silent) {
      return data;
    }

    if (status === 200 && [false, 'false'].includes(data?.success)) {
      const requestId = headers?.['x-bce-request-id'];
      let message = data?.message?.global || data?.message?.noSession;

      if (!message && !data?.message?.field) {
        message = '系统提示：请求失败（未知错误）';
      }

      if (message) {
        toast.error({
          message: (
            <GlobalNotifyMessage
              url={config.url!}
              requestId={requestId!}
              message={data.message.global}
            ></GlobalNotifyMessage>
          ),
          description: (
            <GlobalNotifyDescription
              requestId={requestId}
            ></GlobalNotifyDescription>
          ),
          key: headers?.['x-bce-request-id'],
          duration: 5,
          className: 'global-toast-error-container'
        });
      }
    }

    return data;
  },
  (err) => Promise.reject(err)
);
```



全局提示弹窗效果提下：

![](http://bj.bcebos.com/ibox-thumbnail98/********************************?authorization=bce-auth-v1%2Ffbe74140929444858491fbf2b6bc0935%2F2024-01-15T10%3A51%3A13Z%2F1800%2F%2F7cdd19cf77c09112e03dfd6af75c266b27fb65c5cd1c5a913566c9930ad3e011)



🔔  推荐的使用方式，如下：

在 `@/apis` 路径下，新建业务对应的 `.ts` 文件，如 `auth.ts` 文件存放鉴权相关的接口：

```typescript
import request from '@/utils/request';

/** 激活产品角色 */
export function activateIamStsRole(params: {
  roleName: string;
  accountId: string;
  serviceId: string;
  policyId: string;
}): Promise<{
  status: number;
  success: boolean;
}> {
  return request({
    url: '/api/iam/sts/role/activate',
    method: 'POST',
    data: params
  });
}
```



> 🔔   `params` 作为接口的参数，可分别对接口参数和接口响应进行类型定义。 `request` 是 `@/utils/request.ts` 文件导出的 axios 实例，接收的具体参数可查看  [axios 官方文档](https://axios-http.com/docs/intro)。
>
> 另外新增 `silent` 参数，支持进行静默请求，当接口报错时，不进行 toast 提示, 具体使用示例如下：
>
> ```tsx
> return request({
>     url: '/api/iam/sts/role/activate',
>     method: 'POST',
>     data: params,
>     silent: true
> });
> ```





### 路由懒加载

在 `@/pages/index.tsx` 中进行菜单注册时，可通过 `React.lazy` 进行引用页面组件，示例代码如下：

```tsx
const Activation = React.lazy(
  () => import(/* webpackChunkName: "Activation" */ '@/pages/Activation')
);

/** 菜单定义 */
const menus: MenuItem[] = [
  {
    menuName: '概览',
    key: urls.activation,
    isNavMenu: true,
    Component: Activation
  },
];
```

> 🔔  当需要自定义 chunk 文件的名称时，可通过 `webpackChunkName` 的注释来赋值 chunk 文件名称。



由于 `bce-versions` 的上线流程限制，`chunks` 文件的加载需要基于运行时的 CDN 版本，因此需要特殊配置，具体配置如下：

1. 在 `public/index.html` 中赋值 `window.appPublicPath` 变量：

   ```javascript
   window.appPublicPath = baseUrl + appName + '/' + targetVersion + '/';
   ```

2. 在 `src/index.tsx` 文件中，定义 `__webpack_public_path__`：

   ```tsx
   __webpack_public_path__ = window.appPublicPath || '';
   ```

通过上述配置，`main.js`  即可正确加载 `chunks` 文件。

> 🔔  为了保证 `window.appPublicPath` 和  `__webpack_public_path__` 不会在 vscode 中报错，还需要在 `.eslintrc` 和 `global.d.ts` 文件中进行声明。

## 部署流程

### ci.yml

`ci.yml`配置文件内容如下：

```yaml
Global:
  version: '2.0'
  group_email: <EMAIL>
Default:
  profile:
    - buildProduction
Profiles:
  - profile:
    name: buildProduction
    mode: AGENT
    environment:
      image: DECK_CENTOS6U3_K3
      tools:
        - nodejs: 16.16.0
    build:
      command: sh scripts/build.sh
    artifacts:
      release: true
```



### build.sh

`build.sh` 脚本文件内容如下：

```shell
#!/usr/bin/env bash
set -e
export PATH=:$PATH
echo "node: $(node -v)"
echo "npm: v$(npm -v)"

export BUILD_ENV="pipeline"

npm install
npm run build

if [ -d "output" ]; then
    rm -rf output
fi

mkdir -p output
mv dist/* output
```

> 🔔   `export BUILD_ENV="pipeline"` 用来定义当前在流水线中进行编译，webpack 会根据此环境变量来控制某些参数以提升编译速度。



### 发布 CDN

Ipipe 流水线中的发布 CDN 脚本配置如下：

```shell
#!/bin/sh

source ~/.env

prepare_env ${AGILE_MODULE_NAME} ${AGILE_RELEASE_VERSION}

wget --header ${AGILE_RELEASE_PRODUCT_AUTH_HEADER} -O output.tar.gz ${AGILE_RELEASE_PRODUCT_HTTP_URL}

tar xzvf output.tar.gz

upload_to_cdn output
```

> 🔔 发布完成 CDN 之后可访问如下地址验证静态资源是否上传成功：
>
> - js: https://bce.bdstatic.com/console/static/模块名/版本号/static/js/main.js
> - css：https://bce.bdstatic.com/console/static/模块名/版本号/static/css/main.css
>
> 示例如下：
>
> - js:https://bce.bdstatic.com/console/static/dbsc/*******/static/js/main.js
>
> - css：https://bce.bdstatic.com/console/static/dbsc/*******/static/css/main.css



### 部署沙盒

在 ipipe 流水线中可添加脚本，选择 `upload-to-cdn` agent， 执行如下命令：

```shell
#!/bin/sh

source ~/.env

prepare_env ${AGILE_MODULE_NAME} ${AGILE_RELEASE_VERSION}

# 模块名称
export MODULE=dbsc

auto_deplay
```

> 🔔   注意将 `MODULE` 替换为产品实际对应的值。
>
> 部署沙盒之后，可以访问 https://bce.bdstatic.com/console/static/qasandbox-config.js 来验证是否部署成功。
>
> ![](http://bj.bcebos.com/ibox-thumbnail98/********************************?authorization=bce-auth-v1%2Ffbe74140929444858491fbf2b6bc0935%2F2024-01-15T11%3A35%3A37Z%2F1800%2F%2F3d4f8c990619b5d4840aba030d48c85d6c3e571910cad9abb1617aeedc8c7d24)



### Console Hub 首页

本项目模板中的 `public` 目录下存在一个 `index.html` ， 此 html 文件与 `local.html` 存在区别。`index.html` 主要用于生产环境下的产品访问，具体内容如下：

```html
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <title>百度智能云</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <link
      rel="shortcut icon"
      href="https://bce.bdstatic.com/img/favicon.ico"
      type="image/x-icon"
    />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <link rel="dns-prefetch" href="//code.bdstatic.com" />
    <link rel="dns-prefetch" href="//bce.bdstatic.com" />
    <script
      type="text/javascript"
      src="https://bce.bdstatic.com/ecom/esl/2.2.0-rc.3/esl.js"
    ></script>
    <script
      type="text/javascript"
      src="https://bce.bdstatic.com/console/fe-framework/loadjs.js"
    ></script>
  </head>

  <body>
    <div id="root-app-container"></div>

    <script type="text/javascript">
      function loadCss(url) {
        var cssLink = document.createElement('link');
        cssLink.setAttribute('rel', 'stylesheet');
        cssLink.setAttribute('href', url);
        document.head.appendChild(cssLink);
      }

      function loadJS(url) {
        var scriptNode = document.createElement('script');
        scriptNode.setAttribute('type', 'text/javascript');
        scriptNode.setAttribute('src', url);
        document.head.appendChild(scriptNode);
      }

      var pagemakerAppName = window.location.pathname
        .replace(/^\//, '')
        .replace(/\//g, '_')
        .toUpperCase();
      sessionStorage.removeItem('PAGEMAKER_RELEASE_STATE_' + pagemakerAppName);

      var baseUrl = 'https://bce.bdstatic.com/console/static/';

      var versionConf = /^console\.bce\.baidu\.com/.test(
        window.location.hostname
      )
        ? 'online-config'
        : 'qasandbox-config';

      var appName = '<%= htmlWebpackPlugin.options.appName %>';

      var jsList = ['/static/js/main.js'];
      var cssList = ['/static/css/main.css'];

      require.config({
        baseUrl: baseUrl
      });

      require([versionConf], function (conf) {
        var grayVersion = sessionStorage.getItem('_' + appName + '_version_');

        if (grayVersion) {
          console.warn(
            'The browser is currently in a gray environment, and the gray version number is ' + grayVersion + '.'
          );
        }

        var releaseVersion = conf && conf[appName] && conf[appName].version;
        var targetVersion = grayVersion || releaseVersion;

        window.appPublicPath = baseUrl + appName + '/' + targetVersion + '/';

        function concatUrl(filePath) {
          return baseUrl + appName + '/' + targetVersion + filePath;
        }

        cssList.forEach(function (filePath) {
          loadCss(concatUrl(filePath));
        });
        jsList.forEach(function (filePath) {
          loadJS(concatUrl(filePath));
        });

        require(['framework'], function (framework) {
          window.$framework = framework;
          framework.boot().then(function (initData) {
            require([
              '<%= htmlWebpackPlugin.options.moduleName %>'
            ], function (app) {
              app.bootstrap(initData);
            });
          });
        });
      });
    </script>
  </body>
</html>
```



> 🔔  上述的 html 中存在 `灰度机制`，通过设置 sessionStorage 可以访问任意版本的前端资源，且支持 `css` 与 `js` 文件分别加载。
>
> 当发布 CDN 时，html 文件会一并上传到 bos ， 此时将 CDN 上的 index.html 访问地址维护到 console-hub 的 index 配置中即可：
>
> - Console hub 沙盒地址：http://sandbox.bce.console.baidu-int.com/#/console-hub
> - Console hub 线上地址：http://bce.console.baidu-int.com/#/console-hub
> - index html CDN 访问地址示例：https://bce.bdstatic.com/console/static/dbsc/*******/index.html
>
> ![](http://bj.bcebos.com/ibox-thumbnail98/********************************?authorization=bce-auth-v1%2Ffbe74140929444858491fbf2b6bc0935%2F2024-01-15T11%3A40%3A04Z%2F1800%2F%2F0bee96a91dac13ad5d1c497bf75c0057e17eb524cdd761affd8b927c93dddf6d)
>
>
>
> 🚨  如果产品已经接入过 PageMaker ，需要在 `index.html`  中添加如下代码，否则会出现 framework 找不到正确的挂载节点，导致白屏的问题：
>
> ```javascript
> var pagemakerAppName = window.location.pathname.replace(/^\//, '').replace(/\//g, '_').toUpperCase();
> sessionStorage.removeItem("PAGEMAKER_RELEASE_STATE_" + pagemakerAppName);
> ```
>
> 🌷 Tips:  `index.html` 中增加了灰度判断，当浏览器处于灰度环境时，会在控制台打印提示信息，方便产研和测试同学判断当前访问环境，并获取当前灰度版本号：
>
> ```javascript
> if (grayVersion) {
>   console.warn(
>     'The browser is currently in a gray environment, and the gray version number is ' + grayVersion + '.'
>   );
> }
> ```



### 灰度验证

加入当前产品的模块名称为 dbsc ， 则访问产品页面之后，通过设置 sessionStorage 进行灰度验证，示例代码如下：

```javascript
sessionStorage.setItem("_dbsc_version_", "*******");
```



### 发布上线

拉取 [bce-versions](https://console.cloud.baidu-int.com/devops/icode/repos/baidu/bce-console/bce-versions/tree/master) 代码库，参考其它产品模块进行新增，修改版本号为要上线的版本号，发起评审，执行完毕流水线后即上线完成。

可通过访问 https://bce.bdstatic.com/console/static/online-config.js 来验证版本是否上线成功。



## 场景支持

### 第三方模块

> 🚨 **本方案支持 React 项目同时作为 `独立项目交付`和 `抽取部分组件整合为第三方模块` 集成至其它项目中使用两种场景， 且与路由解耦，支持在其它项目中任意指定渲染区域，不限制以页面为粒度**。

当需要进行技术栈迁移时，本模板支持作为一个 `第三方模块`，嵌入显示到 `SDP` 或 `EDP` 技术框架中，并具备良好的渲染效果和本地环境调试开发体验，且支持私有化混合研发场景。

在 `package.json` 中包含如下两个 `script` 命令：

```json
{
  "scripts": {
    "dev:embed": "cross-env NODE_ENV=development BUILD_TYPE=embed webpack serve --config config/webpack.dev.config.js",
    "build:embed": "cross-env NODE_ENV=production BUILD_TYPE=embed webpack --config config/webpack.prod.config.js"
  }
}
```

> 🔔  在 `webpack` 进行构建时，会根据 `BUILD_TYPE` 环境变量判断是否作为 `第三方模块` 进行打包。



当 `BUILD_TYPE=embed` 时，在 ``webpack``  的配置中主要存在以下变动，具体可查看源码：

1. `.css`、`.module.less` 和 `.less` 文件的 `loader` 的首项会由  `MiniCssExtractPlugin.loader` 替换为 `style-loader` ， 并在 `plugins` 中禁用 `MiniCssExtractPlugin` ，作用是禁止将样式打包到单独文件中，而是将其统一打包到 `.js` 文件中，作为一个完整的 `第三方模块`；

2. `devServer` 不再支持自动打开浏览器及 `proxy` 设置，并关闭 `https` 和 `historyAppFallback` ；

3. 禁用 `HtmlWebpackPlugin` 的相关逻辑；

4. 新增自定义 `EmbeddedModulePlugin` webpack plugin， 用于在本地开发时快捷获取编译之后的 amd `模块名称` 和 `访问地址`，名称和地址信息会在终端中进行显示，如：

   ![](http://bj.bcebos.com/ibox-thumbnail98/********************************?authorization=bce-auth-v1%2Ffbe74140929444858491fbf2b6bc0935%2F2024-01-17T09%3A19%3A17Z%2F1800%2F%2F6ebf4f19989d29979d952370be5112b43f735ef12d9d1b75960e8c153a5ec85e)

   自定义插件内容如下：

   ```tsx
   const colors = require('ansi-colors');

   class EmbeddedModulePlugin {
     constructor(options) {
       this.host = options.host;
       this.port = options.port;
       this.protocol = options.protocol || 'https';
       this.moduleFileName = options.moduleFileName;
       this.moduleName = options.moduleName;
       this.url = this.buildUrl();
     }

     processPath(path = '') {
       path = path.replace(/^\//, '').replace(/\/$/, '');
       return '/' + path;
     }

     buildUrl() {
       return `${this.protocol}://${this.host}:${this.port}${this.processPath(
         this.moduleFileName
       )}`;
     }

     apply(compiler) {
       compiler.hooks.done.tap('afterEmit', () => {
         console.log(`\nembedded amd module address: ${colors.blue(this.url)}`);
         console.log(`embedded amd module name: ${colors.blue(this.moduleName)}`);
       });
     }
   }

   module.exports = EmbeddedModulePlugin;
   ```

5. `dev` 和 `prod` 的配置文件中，`entry` 入口文件统一由  `./src/index.tsx` 替换为 `.src/embed.tsx` 文件，` embed.tsx` 文件内容如下：

   ```tsx
   import '@/pages/Activation';
   import '@/styles/embed.entry.less';
   import '@/pages/Overview';

   import React, {StrictMode} from 'react';
   import {render} from 'react-dom';
   import {HashRouter} from 'react-router-dom';
   import {AppProvider} from './contexts/AppContext';
   import FrameworkProvider from './contexts/FrameworkContext';
   import {moduleRegistry} from './decorators/asModule';

   interface MountOptions {
     /** 挂载节点 */
     selector: string | Node;
     /** framework 数据 */
     frameworkData: any;
     /** 渲染的模块名称 */
     moduleName: string;
     /** 渲染的参数 */
     props: {
       [key: string]: any;
     };
   }

   const EmbedWrapper: React.FC<Partial<MountOptions>> = (options) => {
     const Component = moduleRegistry.get(options.moduleName!) as any;
     return (
       <div className="embedded-wrapper">
         <Component {...options.props} />
       </div>
     );
   };

   export function mount(options: MountOptions) {
     const {selector, frameworkData, moduleName} = options;
     const targetNode =
       selector instanceof Node ? selector : document.querySelector(selector);
     if (!targetNode || !moduleName) {
       return;
     }
     render(
       <StrictMode>
         <FrameworkProvider frameworkData={frameworkData}>
           <AppProvider isEmbed>
             <HashRouter>
               <EmbedWrapper
                 moduleName={options.moduleName}
                 props={options.props}
               ></EmbedWrapper>
             </HashRouter>
           </AppProvider>
         </FrameworkProvider>
       </StrictMode>,
       targetNode as Element
     );
   }
   ```

   > 🔔  当以 `embed.tsx` 作为入口文件进行 `amd` 打包时，会在 amd 模块中暴露 `mount` 方法。
   >
   > `mount` 方法接收 4 个参数：
   >
   > - `selector` ：挂载节点，可以是字符串或者 DOM 节点，用于 `React` 进行渲染组件时挂载；
   > - `frameworkData`: framework 的初始化数据，需要由 `SDP` 或 `EDP` 框架 传递进来，用于初始化 `FrameworkProvider` 中的值，供页面组件通过 `useFrameworkContext` 进行使用；
   > - `moduleName`: 指定需要渲染的模块名称，此处的 `模块` 指的是通过 `asModule` 装饰器进行注册的页面或业务组件；
   > - `props`:  像要渲染的 React 组件中传递的 props 参数。

   详细介绍一下 `@/decorators/asModule.ts` 文件，文件内容如下：

   ```typescript
   import React from 'react';

   interface AsModuleParams {
     /** 模块注册名称 */
     name?: string;
   }

   class ModuleRegistry {
     registry: {
       [key: string]: React.Component;
     };

     constructor() {
       this.registry = {};
     }

     register(name: string, Component: React.Component) {
       if (this.registry[name]) {
         throw new Error(`this module <${name}> is already registered`);
       }

       this.registry[name] = Component;
     }

     get(name: string): React.Component {
       return this.registry[name];
     }
   }

   export const moduleRegistry = new ModuleRegistry();

   function asModule(params?: AsModuleParams) {
     const {name} = params || {};
     return function <T extends {new (...args: any[]): object}>(constructor: T) {
       const registerName = name || constructor.name;
       const target = class extends constructor {};
       moduleRegistry.register(registerName, target);

       return target;
     };
   }
   export default asModule;
   ```

   > 🔔  在 `@/decorators/asModule.ts` 文件中主要存在两部分内容：
   >
   > - `ModuleRegistry`: 模块注册中心，用于存储和获取注册的模块及其名称。
   > - `asModule` ：类装饰器，用于将 React 类组件注册成模块，后续提供给 `EmbedWrapper` 组件进行渲染。

   `asModule` 的具体用法如下：

   ```tsx
   import asModule from '@/decorators/asModule';

   // 函数式组件，可直接用于非嵌入式的完整项目引用显示
   export function Overview() {
     return <div>...<div>
   }

   // 单独注册，用于作为第三方模块集成到其它项目中
   @asModule()
   export class OverviewModule extends React.Component {
     render() {
       return <Overview {...this.props}></Overview>;
     }
   }
   ```



当 React 项目完成模块注册和打包构建之后，可以在 `SDP` 或 `EDP` 项目中通过 amd 的模块加载方式进行引用，使用示例如下：

1. 在 `utils` 目录下新增 `framework.ts` 文件，用于 framework 数据的存储和获取，示例代码如下：

   ```ts
   class Framework {
       frameworkData: any;
       constructor() {
           this.frameworkData = {}
       }
       getFrameworkData() {
           return this.frameworkData;
       }
       setFrameworkData(frameworkData: any) {
           this.frameworkData = frameworkData;
       }
   }
   const framework = new Framework();
   export default framework;
   ```

2. 在项目入口文件处，或其它能够获取到 framework 数据的地方，进行数据存储，下例是在 `bootstrap.ts` 文件中：

   ```tsx
   export function start(frameworkData: any) {
       framework.setFrameworkData(frameworkData);
       return Promise.resolve();
   }
   ```

3. 在 `utils` 目录下新增 `renderEmbeddedModule` 工具方法，用于在项目中渲染 React 的指定模块：

   ```tsx
   import framework from "./framework";
   // @ts-ignore
   window.require.config({
       paths: {
           dbscAppModule: 'http://localhost:8890/static/js/embed'
       }
   })
   interface Params {
       selector: string | Node;
       moduleName: string;
       props?: {
           [key: string]: any;
       }
   }

   // 渲染 react 第三方模块的对应组件
   export function renderEmbeddedModule(params: Params) {
       // @ts-ignore
       widnow.require(['dbscAppModule'], (app) => {
           app.mount({
               frameworkData: framework.getFrameworkData(),
               ...params
           })
       });
   }
   ```

4. 定义一个 `san` 组件，在组件的 `attached` 声明周期中，调用 `renderEmbeddedModule` 方法如下：

   ```tsx
   attached() {
     renderEmbeddedModule({
       selector: this.el,
       moduleName: 'OverviewModule'
     })
   }
   ```

   效果如下，左侧菜单为 SDP 项目本身渲染，右侧页面内容为 react 组件渲染：

   ![](http://bj.bcebos.com/ibox-thumbnail98/********************************?authorization=bce-auth-v1%2Ffbe74140929444858491fbf2b6bc0935%2F2024-01-17T09%3A23%3A09Z%2F1800%2F%2Fa54d4d5ee3fb6ea61240227a9bfe504d5069abe7c154e6bbd9cc8e8e9328637a)

   ![](http://bj.bcebos.com/ibox-thumbnail98/********************************?authorization=bce-auth-v1%2Ffbe74140929444858491fbf2b6bc0935%2F2024-01-17T09%3A22%3A03Z%2F1800%2F%2Faa5cf98176ae3a317593e9db19a250226b1a8dc281002f6e01469af8b1cf75c5)



> 🔔  本地开发时，appModule 的 amd 模块填写为对应的本地地址。当需要部署沙盒或发布上线时，需要替换为 CDN 地址。
>
> 注意： 在 React 项目构建时选择 `npm run build:embed` 命令，并获取 CDN 中 `static/js/embed.js` 作为模块加载文件。



### 国际化

国际化不仅要考虑项目中文字内容的翻译，还要兼顾日期时间、货币格式在不同国家地区间的区别。

本项目模板采用社区中比较成熟的国际化解决方案 `react-i18next` 来满足云产品不同场景下的多语言需求。



#### 安装 NPM 工具包

```shell
npm i react-i18next i18next
npm i i18next-parser -D
```

> 🔔  `i18next-parser` 主要用于在项目中自动提取语料，除此之外，还可以使用 `babel-plugin-i18next-extract` babel 插件的形式进行语料提取，但相比与插件形式，`i18next-parser` 方案更加灵活独立，更适用于复杂的项目结构和特定的提取需求。



#### 使用方式

在 `react-i18next`中可以通过 `useTranslation` hook 进行使用，示例代码如下：

```tsx
import {useTranslation} from 'react-i18next';

function Compt() {
  const {t} = useTranslation();

  return <div>{t('待翻译内容')}</div>
}
```

> 🔔  更多使用方法可参考：https://react.i18next.com/guides/quick-start#translate-your-content，推荐使用 hook 形式进行使用。



#### 应用配置文件

在应用配置文件 `app.config.json5` 中，增加 `enableI18n` 的配置项，示例代码如下：

```json
{
  ...,
  /** 是否启用国际化 */
  enableI18n: true,
  /**
   * 是否开启独立的国际化，独立国际化将不再将语料上传至国际化平台
   * 独立国际化可解决两个问题：
   * 1. 语料文件可单独维护，跟随项目版本，目前云控制台拉取的语料文件是所有产品的合集，不区分产品，会存在语料翻译覆盖的情况
   * 2. 满足私有化时的多语言需求
  */
  enableIndependentI18n: false,
  /** 支持的语言类型, 默认支持中文，可不在列表中维护中文，推荐全部小写 */
  allowedLanguageTypes: ['en-us']
}
```



#### package.json 命令

在 `package.json` 文件中增加国际化相关命令：

```json
{
  "scripts": {
    "i18n:extract": "node config/i18n/extract.js",
    "i18n:upload": "node config/i18n/uploader.js"
  }
}
```

> 🔔   `i18n:extract` 用于提取语料，`i18n:upload` 用于将提取之后的语料转换为 `.po` 文件，然后上传至`云桥国际化平台`。



#### 语料提取

`config/i18n/extract.js` 文件内容如下：

```javascript
/* eslint-disable @typescript-eslint/no-var-requires */
const path = require('path');
const fs = require('fs');
const json5 = require('json5');
const colors = require('ansi-colors');
const tmp = require('tmp');
const ejs = require('ejs');
const fse = require('fs-extra');
const {execSync} = require('child_process');

const cwd = process.cwd();

const config = json5.parse(
  fs.readFileSync(path.resolve(cwd, 'app.config.json5'))
);

const {enableI18n, enableIndependentI18n, allowedLanguageTypes} = config;

if (!enableI18n || !allowedLanguageTypes?.length) {
  console.log(
    colors.red(
      '🚨 Please enable i18n and maintain allowedLanguageTypes in app config file.'
    )
  );
  return;
}

const i18nParserFile = tmp.fileSync({
  postfix: '.js'
});
const inputPath = path.resolve(cwd, './src/**/*.{js,jsx,ts,tsx}');
const outputFilePath = path.resolve(cwd, './public/locale/corpus.json');
const fileData = ejs.render(
  `const path = require('path');
  const cwd = process.cwd();
  module.exports = {
    input: ['<%= inputPath %>'],
    output: '<%= outputFilePath %>',
    options: {}
  };`,
  {
    inputPath,
    outputFilePath
  }
);

fs.writeFileSync(i18nParserFile.name, fileData);

execSync(`npx i18next-parser -c ${i18nParserFile.name}`, {
  cwd,
  encoding: 'utf8',
  stdio: 'inherit'
});

i18nParserFile.removeCallback();

if (enableIndependentI18n) {
  const corpus = JSON.parse(fs.readFileSync(outputFilePath, 'utf-8'));
  allowedLanguageTypes.forEach((type) => {
    const targetPath = path.resolve(cwd, `./public/locale/${type}.json`);
    if (fs.existsSync(targetPath)) {
      const translatedData = JSON.parse(fs.readFileSync(targetPath, 'utf-8'));
      const dataToTranslate = {};
      Object.keys(corpus).forEach((key) => {
        if (!translatedData[key] && translatedData[key] !== '') {
          dataToTranslate[key] = '';
        }
      });
      if (!Object.keys(dataToTranslate).length) {
        return;
      }
      fs.writeFileSync(
        targetPath,
        JSON.stringify(
          Object.fromEntries([
            ...Object.entries(translatedData),
            ...Object.entries(dataToTranslate)
          ]),
          undefined,
          2
        ),
        'utf-8'
      );
      console.log(
        colors.green(
          `✅ The i18n language file ${path.relative(
            cwd,
            targetPath
          )} has been successfully updated.`
        )
      );
      return;
    }
    fse.ensureFileSync(targetPath);
    console.log(
      colors.green(
        `✅ The i18n language file ${path.relative(
          cwd,
          targetPath
        )} has been successfully created.`
      )
    );
    fs.writeFileSync(targetPath, JSON.stringify(corpus, undefined, 2));
    console.log(
      colors.green(
        `✅ The i18n language file ${path.relative(
          cwd,
          targetPath
        )} has been successfully written.`
      )
    );
  });

  fse.removeSync(outputFilePath);
}
```

> 🔔  在上述文件中，主要读取 `app.config.json5` 配置文件，判断是否启用国际化、是否启用独立国际化以及要支持哪些语言的国际化，再 通过 `i18next-parser` 提取当前项目中的 `待翻译` 语料数据，输出至 `public/locale/corpus.json` 文件。
>
> 当启用独立国际化 `enableIndependentI18n` 时，会将 `corpus.json` 的内容生成到对应的语言文件中，如 `public/locale/en-us.json` ，供开发者或相关同学进行内容翻译，此处语料可通过着项目版本进行跟踪。



#### 语料上传

`config/i18n/upload.js` 文件内容如下：

```javascript
/* eslint-disable @typescript-eslint/no-var-requires */
const json2po = require('json2po');
const fs = require('fs');
const path = require('path');
const json5 = require('json5');
const colors = require('ansi-colors');
const {execSync} = require('child_process');

const config = json5.parse(
  fs.readFileSync(path.resolve(__dirname, '../../app.config.json5'))
);

if (config.enableIndependentI18n) {
  console.log(
    colors.red('🚨 Please disable independent i18n in app config file.')
  );
  return;
}

const corpus = fs.readFileSync(
  path.resolve(__dirname, '../../public/locale/corpus.json'),
  'utf-8'
);

const po = json2po(corpus);

function ensurePath(filePath) {
  const dirname = path.dirname(filePath);
  if (!fs.existsSync(dirname)) {
    ensurePath(dirname);
    fs.mkdirSync(dirname);
  }
  return filePath;
}

fs.writeFileSync(
  ensurePath(
    path.resolve(
      __dirname,
      `../../public/locale/po/${config.appName}.i18n.zh-cn.po`
    )
  ),
  po
);

execSync(
  `npx bce-i18n upload -d locale/po --module ${config.appName} && rm -rf locale/po`,
  {
    cwd: path.resolve(__dirname, '../../public'),
    encoding: 'utf8',
    stdio: 'inherit'
  }
);
```

> 🔔  在上述文件中，主要是将语料提取时生成的 `public/locale/corpus.json` 的内容通过 `json2po` 工具包转换为 `.po` 文件，再通过 `@baiducloud/i18n` 工具包上传至 `云桥国际化平台` 中，供相关同学进行翻译。
>
> 当启用独立国际化 `enableIndependentI18n` 时，无需手动执行 `上传` 命令，相关翻译的国际化语料数据会跟随项目构建产物上传至 `CDN` 中。



#### 语料注册

运行时国际化相关的工具方法，可查看 `utils/i18n.ts` 文件内容。

在项目入口中，执行国际化初始化的方法：

```tsx
import {intiI18n} from './utils/i18n';

export async function bootstrap(initData: any) {
  // 国际化相关初始化
  await intiI18n();
  render(
    <StrictMode>
      <FrameworkProvider frameworkData={initData}>
        <AppProvider>
          <App />
        </AppProvider>
      </FrameworkProvider>
    </StrictMode>,
    document.querySelector('#main')
  );
}
```



`initI18n` 方法内容如下：

```tsx
import i18n from 'i18next';
import {initReactI18next} from 'react-i18next';

export async function intiI18n() {
  const languageType = getLanguageType();
  if (APP_ENABLE_I18N && languageType !== LanguageType.zhCN) {
    const i18nCorpusData = await getI18nCorpusData();
    const abbrLanguageType = getAbbrLanguageType(languageType);
    i18n.use(initReactI18next).init({
      resources: {
        [abbrLanguageType]: {
          translation: i18nCorpusData
        }
      },
      lng: abbrLanguageType
    });
  }
}
```

> 🔔  上述方法判断项目配置中是否启用了国际化 `APP_ENABLE_I18N` 以及当前的语言环境是否为中文，若是中文环境，不拉取语料数据。
>
> 通过 `getI18nCorpusData` 获取语料数据，然后将语料数据注册到 `i18n` 中，执行 `init` 方法。



#### 语料拉取

如上述内容，语料拉取主要通过 `getI18nCorpusData` 方法，具体内容如下：

```tsx
export function getI18nCorpusData(): Promise<{
  [key: string]: string;
}> {
  const languageType = getLanguageType();
  if (!APP_ENABLE_INDEPENDENT_I18N) {
    return new Promise((resolve) => {
      const isSandbox = /qasandbox|bcetest/.test(location.host);
      const targetUrl = `https://bce.bdstatic.com/console/static/i18n/console.${
        isSandbox ? 'offline.' : ''
      }${languageType}`;
      window.require([targetUrl], (data: {[key: string]: string}) => {
        resolve(data);
      });
    });
  }
  return new Promise((resolve) => {
    const url = __webpack_public_path__ + `locale/${languageType}.json`;
    axios(url)
      .then((res) => {
        resolve(res?.data || {});
      })
      .catch(() => {
        resolve({});
      });
  });
}
```

> 🔔  项目运行时，会判断是否启用了 `APP_ENABLE_INDEPENDENT_I18N` 独立国际化，去不同的目标地址拉取语录文件。
>
> 默认情况下，从 `云桥国际化平台` 发布之后的 `CDN js` 文件中获取语料数据，注意这种方式拉取的语料是包含云上全部产品的，有可能会出现语料配置信息覆盖的情况。
>
> 当启用 `独立国际化` 后，会从当前访问的前端版本或灰度版本的 `CDN json` 文件中，获取语料数据。



#### ACUD 多语言

`ACUD` 部分组件不支持通过配置全局的 `ConfigProvider` 来进行多语言展示，需要单独通过 `props` 传递参数来明确各组件在不同语言下的展示信息。

项目模板会不断内置各组件的多语言配置，并提供 `I18nWrapper` 来包裹 `acud` 组件，自动进行翻译，无需单独配置。



## Q & A

**1、为什么采用 react17 ，而不是最新的 react18？**

在  [acud React 安装文档](https://acud.now.baidu-int.com/components/docs) 中有版本兼容性的的注意事项说明：

- 支持 react16、react17
- react18 在严格模式下还需要调整，建议不要启动 StrictMode

经过实测，在 react18 版本下会出现 acud 菜单组件交互异常，useEffect 等 hooks 表现不符合预期等问题，因此选择 react17 作为本项目模板的基础版本。



**2、webpack5 相比较 webpack4 有哪些方面的提升？**

具体区别可搜索查阅相关文档,  如 [掘金：webpack5 与 webpack4 对比](https://juejin.cn/post/6990869970385109005)

目前我们需要重点关注的提升点如下：

- **性能优化**
  - **持久性缓存**：Webpack 5 引入了更加高效的持久性缓存机制，可以显著提升二次构建的速度；
  - **更好的算法和默认值**：改进了许多内部算法和默认的配置值，这些改进旨在减少构建和重新构建的时间；
- **模块和依赖管理**
  - **树摇（Tree Shaking）**：Webpack 5 对树摇的支持更加成熟，能够更有效地识别无用代码并将其删除；
  - **模块联邦**：模块联邦允许多个构建之间共享模块，而不需要包含在每个构建中；
  - **更好的处理模块**：Webpack 5 改进了对 ECMAScript 模块的处理，包括对 CommonJS 和其他模块类型的兼容性；
- **资源管理：**
  - **内置的资源模块**：Webpack 5 内置了对资源文件的支持，可以不再需要`file-loader`和`url-loader`等额外的加载器；
  - **自动资产模块类型**：可以自动选择是否将资源内联到bundle中，还是将其分离成单独的文件；
- **配置和输出：**
  - **配置项清理**：Webpack 5 清理和移除了许多过时的配置项，使得配置更加清晰和简洁；
  - **改进的输出**：Webpack 5 提供了更清晰和更可定制的构建输出，便于开发者理解和追踪构建过程；

项目迁移至 webpack5 可参考官方文档：[webpack To v5 from v4](https://webpack.js.org/migrate/5/)



**3、为什么 app.config 配置文件使用 json5， 而不是 json 或 js 文件？**

Json5 相比于 json 更接近于 javascript 语言的对象和数组字面量语法，能够支持单行和多行注释，支持单引号字符串且允许字符串跨行，支持多进制数字字面量和 undefined、Infinity 等更多额外的值，而且又比 Javascript 文件更加轻量，用户仅关注配置文件的每个属性及取值即可，能够更好的提高配置的可读性和可写性。



**4、为什么采用 amd 的打包方式？而不是 ES Module ？ 什么情况下可以改为 ES Module ?**

**5、为什么默认只配置路由懒加载，而不配置 `chunks="all` ？ 什么时候可以配置 `chunks="all"`？**



## 相关文档

- [react17](https://17.reactjs.org/docs/getting-started.html)
- [webpack5](https://webpack.js.org/concepts/)
- [acud](https://acud.now.baidu-int.com/components/button)
- [TypeScript](https://www.typescriptlang.org/docs/)
- [eslint](https://eslint.org/docs/latest/)
- [prettier](https://prettier.io/docs/en/)
- [editorConfig](https://editorconfig.org/)
- [axios](https://axios-http.com/docs/intro)
- [react-i18next](https://react.i18next.com/guides/quick-start)
