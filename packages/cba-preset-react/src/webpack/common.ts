/**
 * @file dev & build 公共配置
 * <AUTHOR> (<EMAIL>)
 */
import MiniCssExtractPlugin from 'mini-css-extract-plugin';
import {IApi} from '@baidu/cba-preset';
import os from 'os';
import {getBabelConfig} from './babelConfig';

const threads = os.cpus().length;

export default (api: IApi) => {
  api.describe({
    key: 'preset-react:webpack-common'
  });

  api.modifyWebpackConfig((webpackConfig, {merge}) => {
    return merge(webpackConfig, {
      module: {
        rules: [
          {
            oneOf: [
              {
                test: /\.css$/,
                use: [
                  MiniCssExtractPlugin.loader,
                  'css-loader',
                  {
                    loader: 'postcss-loader',
                    options: {
                      postcssOptions: {
                        plugins: ['postcss-preset-env']
                      }
                    }
                  }
                ]
              },
              {
                test: /\.module\.less$/,
                use: [
                  MiniCssExtractPlugin.loader,
                  {
                    loader: 'css-loader',
                    options: {
                      modules: {
                        localIdentName: '[local]_[hash:base64:5]'
                      }
                    }
                  },
                  {
                    loader: 'postcss-loader',
                    options: {
                      postcssOptions: {
                        plugins: ['postcss-preset-env']
                      }
                    }
                  },
                  'less-loader'
                ]
              },
              {
                test: /\.less$/,
                use: [
                  MiniCssExtractPlugin.loader,
                  'css-loader',
                  {
                    loader: 'postcss-loader',
                    options: {
                      postcssOptions: {
                        plugins: ['postcss-preset-env']
                      }
                    }
                  },
                  'less-loader'
                ]
              },
              {
                test: /\.(png|jpe?g|gif)$/,
                type: 'asset',
                parser: {
                  dataUrlCondition: {
                    maxSize: 4 * 1024 // 4kb
                  }
                }
              },
              {
                test: /\.svg$/i,
                type: 'asset',
                resourceQuery: /url/, // *.svg?url
                parser: {
                  dataUrlCondition: {
                    maxSize: 10 * 1024 // 10kb
                  }
                }
              },
              {
                test: /\.svg$/i,
                resourceQuery: {not: [/url/]}, // exclude react component if *.svg?url
                use: ['@svgr/webpack']
              },
              {
                test: /\.(jsx|js)$/,
                use: [
                  {
                    loader: 'thread-loader',
                    options: {
                      workers: threads
                    }
                  },
                  {
                    loader: 'babel-loader',
                    options: getBabelConfig(api.userConfig)
                  }
                ],
                exclude: /node_modules/
              },
              {
                test: /\.tsx?$/,
                use: [
                  {
                    loader: 'thread-loader',
                    options: {
                      workers: threads
                    }
                  },
                  {
                    loader: 'babel-loader',
                    options: getBabelConfig(api.userConfig)
                  }
                ],
                exclude: /node_modules/
              }
            ]
          }
        ]
      },
      plugins: [
        new MiniCssExtractPlugin({
          filename: 'static/css/[name].[contenthash:8].css',
          chunkFilename: 'static/css/[name].[contenthash:8].chunk.css',
          ignoreOrder: true
        })
      ].filter(Boolean)
    });
  });
};
