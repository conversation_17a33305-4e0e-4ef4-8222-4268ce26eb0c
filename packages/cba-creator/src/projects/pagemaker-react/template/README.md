# 百度云控制台前端示例模块（react）

> 百度云控制台项目参考文档：[文档参考](http://sandbox.bce.console.baidu-int.com/bce-docs/fe/console-setup/fe-setup/fe-repo.html)

## 安装环境

- 安装依赖
  > 指定`registry`为`http://registry.npm.baidu-int.com`
  > 推荐使用 `nrm` 管理 `registry`

```shell
    npm install --legacy-peer-deps
```

## 本地调试

```
    npx cba-cli dev
    // or
    npm run dev
```

## 接入 Pagemaker

    在Pagemaker平台中【应用设置】-【外部js/css】中添加 http://localhost:8899/pagemaker.development.js ，并在Pagemaker应用中建立类型为【自定义开发页面】对应路由的页面。

## 本地编译

```
    npx cba-cli build
    // or
    npm run build
```

## 流水线

    配置可参照：https://console.cloud.baidu-int.com/devops/ipipe/workspaces/388790/pipelines/1054064/builds/list?branchName=master

## 项目内 API 请求

项目内置请求器，无需额外使用 axios、$.ajax 等库，示例如下:

> 默认请求器在 pagemaker 预览环境时，会自动代理到 console-hub 沙盒环境

```ts
import {amisRequest} from '@baidu/cba-cli/runtime';

export default {
  getList(fileName: string) {
    return amisRequest({
      url: `/api/v2/list?fileName=${fileName}`
    });
  }
};
```

如需代理到指定服务器，用法如下：

> 此代理只在 pagemaker 预览环境生效，生产环境无效，无需做额外特殊处理

```ts
import {amisRequest} from '@baidu/cba-cli/runtime';

// 构建新的使用代理的请求器
const newRequest = options =>
  amisRequest(options, {
    path: '/api/v2', // 需要代理的api前缀
    target: 'https://baidu.com' // 需要代理到的 host
  });

export default {
  getList(fileName: string) {
    return newRequest({
      url: `/api/v2/list?fileName=${fileName}`
    });
  }
};
```

如需使用自己的请求器，如 axios，示例如下:

> 此代理只在 pagemaker 预览环境生效，生产环境无效，无需做额外特殊处理

```ts
import axios from 'axios';
import {pagemakerProxy} from '@baidu/cba-cli/runtime';

// 获取代理配置
const {pathPrefix, headers} = pagemakerProxy('/api/v2', 'https://baidu.com');

// 配置 axios
const service = axios.create({
  baseURL: pathPrefix,
  timeout: 30000,
  headers
});

export default {
  getList(fileName: string) {
    // 注意：这里不再需要加 /api/v2 前缀
    return service.get(`/list?fileName=${fileName}`);
  }
};
```

## 相关文件说明

- `package.json`: 必要文件, 其中`name`与`main`为必要属性
- `bce-config.js`: 产品相关配置，包含`webpack`
