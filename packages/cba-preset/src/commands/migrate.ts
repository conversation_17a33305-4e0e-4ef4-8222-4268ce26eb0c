import {join} from 'path';
import {readFileSync, writeFileSync, existsSync, mkdirpSync} from 'fs-extra';
import shell from 'shelljs';

import {writeTpl2File, mkFile} from '../utils';

export default async (api: any) => {
  api.registerCommand({
    name: 'migrate',
    description: 'migrate to bce-cli project',
    fn: async ({args}: any) => {
      // 1、修改package.json
      const pkg = JSON.parse(readFileSync('package.json', 'utf-8'));
      pkg.main = 'src/App.tsx';
      if (!pkg.scripts) {
        pkg.scripts = {};
      }
      pkg.scripts.dev = 'cba-cli dev';
      pkg.scripts.build = 'cba-cli build';
      pkg.scripts.preview = 'cba-cli preview';
      writeFileSync('package.json', JSON.stringify(pkg, null, 2), 'utf-8');
      // 2、修改bce-config.js
      if (!existsSync('bce-config.js')) {
        shell.touch('bce-config.js');
      }
      const configTplPath = join(__dirname, '../template/bce-config.tpl');
      writeTpl2File({
        tplPath: configTplPath,
        targetPath: 'bce-config.js'
      });
      // 3、添加入口文件
      const appPath = 'src/App.tsx';
      if (!existsSync(appPath)) {
        mkFile(appPath);
      }
      const appTplPath = join(__dirname, '../template/project/react-for-pagemaker/src/App.tsx');
      writeTpl2File({
        tplPath: appTplPath,
        targetPath: appPath,
        data: {}
      });
      // 4、路由配置
      const routePath = 'src/routes.tsx';
      if (!existsSync(routePath)) {
        mkFile(routePath);
      }
      const routeTplPath = join(__dirname, '../template/project/react-for-pagemaker/src/routes.tsx');
      writeTpl2File({
        tplPath: routeTplPath,
        targetPath: routePath
      });
    }
  });
};
