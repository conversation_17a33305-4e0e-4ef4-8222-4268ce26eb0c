import yargsParser from 'yargs-parser';
import {ILogger} from '@baidu/cba-utils';
import {PluginAPI} from './service/pluginAPI';
import {Plugin} from './service/plugin';

export enum Env {
  development = 'development',
  production = 'production'
}

/**
 * 服务运行阶段
 */
export enum ServiceStage {
  /**
   * 未初始化
   */
  uninitialized,
  /**
   * 初始化
   */
  init,
  /**
   * 注册预设
   */
  initPresets,
  /**
   * 注册插件
   */
  initPlugins,
  /**
   * 收集数据
   */
  collectData,
  /**
   * 运行命令前的事件钩子
   */
  onStart,
  /**
   * 运行命令
   */
  runCommand
}

export interface Service {
  cwd: string; // 路径
}

export interface IUserConfig {
  // 其他配置
  [key: string]: any;

  /**
   * 预设列表
   */
  presets?: string[];
  /**
   * 插件列表
   */
  plugins?: string[];
}

/**
 * Service暴露给插件的属性和方法
 */
export interface IServicePluginAPI {
  /**
   * 当前路径
   *
   */
  cwd: string;
  /**
   * 命令行参数
   * @default {_: [], $0: ''}
   * @example
   * ```$ cba-cli init --projectName react-local```
   * args = { _: [ 'init' ], projectName: 'react-local' }
   */
  args: yargsParser.Arguments;
  /**
   * 项目package.json包信息
   */
  pkg: Record<string, any>;
  /**
   * 项目package.json包路径
   */
  pkgPath: string;
  /**
   * 命令名称
   * @example
   * init, dev, build, ...
   */
  name: string;
  /**
   * 当前环境
   */
  env: Env;
  /**
   * 命令行执行阶段
   */
  stage: ServiceStage;
  /**
   * 用户配置
   */
  userConfig: Record<string, any>;
  /**
   * 上下文数据
   */
  appData: any;
  /**
   * 环境变量
   */
  rawEnv: Record<string, string | undefined>;

  /**
   * 应用插件hook的类型
   * @枚举 add | modify | event
   */
  ApplyPluginsType: typeof ApplyPluginsType;
  /**
   * 命令执行阶段
   */
  ServiceStage: typeof ServiceStage;
  /**
   * 执行插件通过register() 注册的 hooks
   */
  applyPlugins: (opts: {key: string; type?: ApplyPluginsType; initialValue?: any; args?: any}) => Promise<any>;

  /**
   * 注册插件预设
   * @param presets
   * @returns
   */
  registerPresets: (presets: any[]) => void;
  /**
   * 注册插件
   * @param plugins
   * @returns
   */
  registerPlugins: (plugins: Array<Plugin | {}>) => void;
  /**
   * 修改用户配置
   */
  modifyUserConfig: (fn: (userConfig: IUserConfig) => IUserConfig) => void;
  /**
   * 开始执行命令前的事件回调
   */
  onStart: (fn: () => void) => void;
  /**
   * 修改上下文数据
   */
  updateAppData: (fn: (appData: Record<string, any>) => Record<string, any>) => void;
}

export type IApi = Omit<PluginAPI, 'registerPresets' | 'registerPlugins'> & IServicePluginAPI;

export enum ApplyPluginsType {
  add = 'add',
  modify = 'modify',
  event = 'event'
}
