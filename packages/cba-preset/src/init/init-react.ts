import {logger, getPackageLatestVersion, updatePackageCliVersion, downloadTemplate} from '@baidu/cba-utils';
import {IApi} from '../types';
import {addProject} from '.';
import type {IPrompt} from '.';

export enum BuildToolType {
  WEBPACK = 'webpack',
  VITE = 'vite'
}

async function initConsoleReact(prompt: IPrompt) {
  const {projectName} = prompt;
  logger.info('初始化云控制台应用');
  await downloadTemplate(prompt.projectName, 'console-react');

  const pkgPath = projectName ? `${projectName}/package.json` : 'package.json';

  updatePackageCliVersion(pkgPath);
}

export default async (api: IApi) => {
  addProject({
    name: '云控制台应用(React)',
    key: 'console-react',
    // extPrompts: () => {
    //     return inquirer.prompt([
    //       {
    //         type: 'list',
    //         message: '请选择构建工具:',
    //         name: 'buildTool',
    //         default: BuildToolType.WEBPACK,
    //         choices: [
    //           {
    //             name: 'webpack',
    //             value: BuildToolType.WEBPACK
    //           },
    //           {
    //             name: 'Vite',
    //             value: BuildToolType.VITE
    //           }
    //         ]
    //       }
    //     ])
    // },
    initFn: initConsoleReact
  });

  addProject({
    name: 'React第三方模块',
    key: 'console-react-embed',
    initFn: async (prompt: IPrompt) => {
      const {projectName} = prompt;
      logger.info('初始化React第三方模块应用');
      await downloadTemplate(prompt.projectName, 'console-react-embed');

      const pkgPath = projectName ? `${projectName}/package.json` : 'package.json';

      updatePackageCliVersion(pkgPath);
    }
  });
};
