{"name": "@baidu/cba-preset", "version": "1.2.8-beta.14", "license": "MIT", "main": "lib/index.js", "files": ["lib", "template"], "scripts": {"build": "cba-scripts build", "dev": "cba-scripts dev"}, "dependencies": {"@baidu/cba-core": "workspace:*", "@baidu/cba-utils": "workspace:*", "@typescript-eslint/eslint-plugin": "^6.18.0", "@typescript-eslint/parser": "^6.18.0", "ajv": "^8.16.0", "axios": "^1.6.7", "babel-loader": "^9.1.3", "babel-plugin-import": "^1.13.8", "babel-plugin-transform-decorators-legacy": "^1.3.5", "clean-webpack-plugin": "^4.0.0", "copy-webpack-plugin": "^11.0.0", "css-loader": "^6.8.1", "css-minimizer-webpack-plugin": "^5.0.1", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-simple-import-sort": "^10.0.0", "fs-extra": "^11.2.0", "html-webpack-plugin": "^5.6.0", "inquirer": "^8.2.6", "less": "^4.2.0", "less-loader": "^11.1.4", "mustache": "^4.2.0", "ora": "^5.0.0", "postcss": "^8.4.33", "postcss-loader": "^7.3.4", "postcss-preset-env": "^9.3.0", "shelljs": "^0.8.4", "style-loader": "^3.3.4", "terser-webpack-plugin": "^5.3.10", "typescript": "^5.3.3", "webpack": "^5.88.0", "webpack-chain": "^6.5.1", "webpack-cli": "^5.1.4", "webpack-dev-server": "^4.15.1", "webpack-merge": "^4.2.2"}, "devDependencies": {"@types/fs-extra": "^11.0.4", "@types/inquirer": "^9.0.7", "@types/shelljs": "^0.8.15", "@types/webpack-merge": "^5.0.0"}}