/**
 * @file 插件参数API
 * <AUTHOR> (<EMAIL>)
 */

import assert from 'assert';
import {makeArray} from '@baidu/cba-utils';
import isPlainObject from 'lodash/isPlainObject';
import {Env, IUserConfig, ServiceStage} from '../types';
import {Plugin} from './plugin';
import {PluginType, Service} from './service';
import {Command, ICommandOpts} from './command';
import {Hook, IHookOpts} from './hooks';
import {Generate, IGenerateOpts} from './generate';

export class PluginAPI {
  /**
   * 服务实例
   */
  service: Service;
  /**
   * 插件实例
   */
  plugin: Plugin;

  constructor(opts: {service: Service; plugin: Plugin}) {
    this.service = opts.service;
    this.plugin = opts.plugin;
  }

  /**
   * 代理插件API的属性和方法，可以获取Service中的属性及registerMethod注册的方法
   * @param opts
   * @returns
   */
  static proxyPluginAPI(opts: {
    pluginAPI: PluginAPI;
    service: Service;
    serviceProps: string[];
    staticProps: Record<string, any>;
  }) {
    return new Proxy(opts.pluginAPI, {
      get: (target, prop: string) => {
        if (opts.service.pluginMethods[prop]) {
          return opts.service.pluginMethods[prop].fn;
        }
        if (opts.serviceProps.includes(prop)) {
          const serviceProp = opts.service[prop];
          return typeof serviceProp === 'function' ? serviceProp.bind(opts.service) : serviceProp;
        }
        if (prop in opts.staticProps) {
          return opts.staticProps[prop];
        }
        return target[prop];
      }
    });
  }

  /**
   * 插件描述
   * @param opts 配置项
   */
  describe(opts: {
    /**
     * 插件key
     */
    key?: string;
    enableBy?: ((enableByOpts: {userConfig: IUserConfig; env: Env}) => boolean) | boolean;
  }) {
    this.plugin.merge(opts);
  }

  /**
   * 注册命令
   * @param opts 配置项
   */
  registerCommand(opts: Omit<ICommandOpts, 'plugin'> & {alias?: string | string[]}) {
    const {alias} = opts;
    delete opts.alias;
    const registerCommand = (commandOpts: Omit<typeof opts, 'alias'>) => {
      const {name} = commandOpts;
      // assert(
      // 	!this.service.commands[name],
      // 	`api.registerCommand() failed, the command 「${name}」 is exists from 「${this.service.commands[name]?.plugin.id}」.`
      // );
      this.service.commands[name] = new Command({
        ...commandOpts,
        plugin: this.plugin
      });
    };
    registerCommand(opts);
    if (alias) {
      const aliases = makeArray(alias);
      aliases.forEach(alias => {
        registerCommand({...opts, name: alias});
      });
    }
  }

  /**
   * 注册钩子函数
   * @param opts 配置项
   */
  register(opts: Omit<IHookOpts, 'plugin'>) {
    assert(
      this.service.stage <= ServiceStage.initPlugins,
      `api.register() should not be called after plugin register stage.`
    );
    this.service.hooks[opts.key] ||= [];
    this.service.hooks[opts.key].push(new Hook({...opts, plugin: this.plugin}));
  }

  /**
   * 注册插件公共方法，可以被其他插件调用
   * @param methodOpts
   */
  registerMethod(methodOpts: {name: string; fn?: Function}) {
    assert(
      !this.service.pluginMethods[methodOpts.name],
      `api.registerMethod() failed, method ${methodOpts.name} is already exist.`
    );
    this.service.pluginMethods[methodOpts.name] = {
      plugin: this.plugin,
      fn:
        methodOpts.fn ||
        function (fn: Function | Object) {
          // @ts-ignore
          this.register({
            key: methodOpts.name,
            ...(isPlainObject(fn) ? (fn as any) : {fn})
          });
        }
    };
  }

  /**
   * 注册插件预设
   * @param source 当前预设集合
   * @param presets 要添加的预设
   */
  registerPresets(source: Plugin[], presets: any[]) {
    assert(
      this.service.stage === ServiceStage.initPresets,
      `api.registerPresets() failed, it should only used in presets.`
    );
    source.splice(
      0,
      0,
      ...presets.map(preset => {
        return new Plugin({
          path: preset,
          cwd: this.service.cwd,
          type: PluginType.preset
        });
      })
    );
  }

  /**
   * 注册插件
   * @param source
   * @param plugins
   */
  registerPlugins(source: Plugin[], plugins: any[]) {
    assert(
      this.service.stage === ServiceStage.initPresets || this.service.stage === ServiceStage.initPlugins,
      `api.registerPlugins() failed, it should only be used in registering stage.`
    );
    const mappedPlugins = plugins.map(plugin => {
      if (isPlainObject(plugin)) {
        assert(plugin.id && plugin.key, `Invalid plugin object, id and key must supplied.`);
        plugin.type = PluginType.plugin;
        plugin.enableBy ||= true;
        plugin.apply ||= () => () => {};
        plugin.config = plugin.config || {};
        return plugin;
      }
      return new Plugin({
        path: plugin,
        cwd: this.service.cwd,
        type: PluginType.plugin
      });
    });
    if (this.service.stage === ServiceStage.initPresets) {
      source.push(...mappedPlugins);
    } else {
      source.splice(0, 0, ...mappedPlugins);
    }
  }
  /**
   * 注册生成器
   * @param opts
   */
  registerGenerator(opts: IGenerateOpts) {
    const { name, fn, description } = opts;
    assert(
      !this.service.generators[name],
      `api.registerGenerator() failed, the generator ${name} is exists from ${this.service.generators[name]?.name}.`,
    );
    this.service.generators[name] = new Generate({
      name,
      description,
      fn
    });
  }
}
