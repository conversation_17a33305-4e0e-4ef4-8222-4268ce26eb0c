import axios from 'axios';
import i18n from 'i18next';
import {isString} from 'lodash';
import {initReactI18next} from 'react-i18next';

import {LanguageType, toolkitConfig, ToolkitConfigOptions} from '../config';

export * from 'react-i18next';

const i18nInstance = i18n;

export class I18nUtil {
  private supportedLanguageTypes = toolkitConfig.getConfig(
    'supportedLanguageTypes'
  ) as ToolkitConfigOptions['supportedLanguageTypes'];
  private defaultLanguageType = toolkitConfig.getConfig(
    'defaultLanguageType'
  ) as ToolkitConfigOptions['defaultLanguageType'];
  private enableIndependentI18n = toolkitConfig.getConfig(
    'enableIndependentI18n'
  ) as ToolkitConfigOptions['enableIndependentI18n'];
  private publicPath = toolkitConfig.getConfig(
    'publicPath'
  ) as ToolkitConfigOptions['publicPath'];
  private enableI18n = toolkitConfig.getConfig(
    'enableI18n'
  ) as ToolkitConfigOptions['enableI18n'];

  private getAbbrLanguageType(languageType: LanguageType) {
    return languageType.split('-')[0];
  }

  private isSupportedLanguageType(languageType: LanguageType) {
    return this.supportedLanguageTypes.includes(languageType);
  }

  private formatLanguageType(languageType: string | null) {
    return (
      isString(languageType)
        ? (languageType as string).toLowerCase()
        : languageType
    ) as LanguageType;
  }

  public isZhCNLanguage() {
    return this.getLanguageType() === LanguageType.zhCN;
  }

  public getLanguageType(): LanguageType {
    let locale: string | LanguageType | null = '';
    const url = new URL(window.location.href);
    locale = url?.searchParams?.get('locale');

    if (this.isSupportedLanguageType(this.formatLanguageType(locale))) {
      return this.formatLanguageType(locale);
    }

    locale = window.navigator.language;
    if (this.isSupportedLanguageType(this.formatLanguageType(locale))) {
      return this.formatLanguageType(locale);
    }

    return this.defaultLanguageType;
  }

  private getCorpusData(): Promise<{
    [key: string]: string;
  }> {
    return new Promise((resolve) => {
      const languageType = this.getLanguageType();

      if (!this.enableIndependentI18n) {
        const isSandbox = /qasandbox|bcetest/.test(location.host);
        const targetUrl = `https://bce.bdstatic.com/console/static/i18n/console.${
          isSandbox ? 'offline.' : ''
        }${languageType}`;
        window.require([targetUrl], (data: {[key: string]: string}) => {
          resolve(data);
        });
        return;
      }

      const url = this.publicPath + `locale/${languageType}.json`;
      axios(url).then((res) => {
        resolve(res?.data || {});
      });
    });
  }

  public async init() {
    const languageType = this.getLanguageType();
    if (this.enableI18n && languageType !== LanguageType.zhCN) {
      const corpusData = await this.getCorpusData();
      const abbrLanguageType = this.getAbbrLanguageType(languageType);
      i18nInstance.use(initReactI18next).init({
        resources: {
          [abbrLanguageType]: {
            translation: corpusData
          }
        },
        lng: abbrLanguageType
      });
    }
  }
}

export {i18nInstance};
