/**
 * @file dev & build 公共配置
 * <AUTHOR> (<EMAIL>)
 */
import webpack from 'webpack';
import MiniCssExtractPlugin from 'mini-css-extract-plugin';
import os from 'os';
import {IApi} from '@baidu/cba-preset';
import path from 'path';
import {getBabelConfig} from './babelConfig';

const isEmbed = process.env.BUILD_TYPE === 'embed';

const threads = os.cpus().length;

export default (api: IApi) => {
  api.describe({
    key: 'preset-console-react:webpack-common'
  });

  api.modifyWebpackConfig((webpackConfig, {merge}) => {
    webpackConfig.module ||= {};
    const sanDir = [];
    const customSanDir = api.userConfig?.i18n?.sdpDir;
    if (Array.isArray(customSanDir)) {
      customSanDir.forEach(dir => {
        path.isAbsolute(dir) ? sanDir.push(dir) : sanDir.push(path.resolve(process.cwd(), dir));
      });
    }
    if (typeof customSanDir === 'string') {
      path.isAbsolute(customSanDir) ? sanDir.push(path.resolve(process.cwd(), customSanDir)) : customSanDir;
    }
    webpackConfig.module.rules = [
      {
        oneOf: [
          {
            test: /\.css$/,
            use: [
              isEmbed ? 'style-loader' : MiniCssExtractPlugin.loader,
              'css-loader',
              {
                loader: 'postcss-loader',
                options: {
                  postcssOptions: {
                    plugins: ['postcss-preset-env']
                  }
                }
              }
            ]
          },
          {
            test: /\.module\.less$/,
            use: [
              isEmbed ? 'style-loader' : MiniCssExtractPlugin.loader,
              {
                loader: 'css-loader',
                options: {
                  modules: {
                    localIdentName: '[local]_[hash:base64:5]'
                  }
                }
              },
              {
                loader: 'postcss-loader',
                options: {
                  postcssOptions: {
                    plugins: ['postcss-preset-env']
                  }
                }
              },
              {
                loader: 'less-loader',
                options: {
                    lessOptions: {
                        math: 'always'
                    },
                },
            }
            ]
          },
          {
            test: /\.less$/,
            use: [
              isEmbed ? 'style-loader' : MiniCssExtractPlugin.loader,
              'css-loader',
              {
                loader: 'postcss-loader',
                options: {
                  postcssOptions: {
                    plugins: ['postcss-preset-env']
                  }
                }
              },
              {
                loader: 'less-loader',
                options: {
                    lessOptions: {
                        math: 'always'
                    },
                },
            }
            ]
          },
          {
            test: /\.(png|jpe?g|gif)$/,
            type: 'asset',
            parser: {
              dataUrlCondition: {
                maxSize: 4 * 1024 // 4kb
              }
            }
          },
          {
            test: /\.svg$/i,
            type: 'asset',
            resourceQuery: /url/, // *.svg?url
            parser: {
              dataUrlCondition: {
                maxSize: 10 * 1024 // 10kb
              }
            }
          },
          {
            test: /\.svg$/i,
            resourceQuery: {not: [/url/]}, // exclude react component if *.svg?url
            use: ['@svgr/webpack']
          },
          customSanDir
            ? {
                test: /\.(ts|js)$/,
                use: [
                  {
                    loader: 'thread-loader',
                    options: {
                      workers: threads
                    }
                  },
                  {
                    loader: 'babel-loader',
                    options: getBabelConfig(api.userConfig, true)
                  }
                ],
                include: sanDir,
                exclude: /node_modules/
              }
            : null,
          {
            test: /\.(jsx|js)$/,
            use: [
              {
                loader: 'thread-loader',
                options: {
                  workers: threads
                }
              },
              {
                loader: 'babel-loader',
                options: getBabelConfig(api.userConfig)
              }
            ],
            exclude: /node_modules/
          },
          {
            test: /\.tsx?$/,
            use: [
              {
                loader: 'thread-loader',
                options: {
                  workers: threads
                }
              },
              {
                loader: 'babel-loader',
                options: getBabelConfig(api.userConfig)
              }
            ],
            exclude: /node_modules/
          }
        ].filter(Boolean)
      }
    ];
    return merge(webpackConfig, {
      entry: './src/index.tsx',
      plugins: [
        !isEmbed &&
          new MiniCssExtractPlugin({
            filename: 'main.css',
            ignoreOrder: true
          }),
        new webpack.DefinePlugin({
          APP_TITLE: JSON.stringify(api.userConfig.appTitle || '百度智能云'),
          APP_ENABLE_I18N: JSON.stringify(api.userConfig?.i18n?.enabled),
          APP_ENABLE_INDEPENDENT_I18N: JSON.stringify(api.userConfig?.i18n?.independent),
          APP_ALLOWED_LANGUAGE_TYPES: JSON.stringify(api.userConfig?.i18n?.supportedLanguages),
          APP_IS_EMBED_MODE: JSON.stringify(isEmbed)
        })
      ].filter(Boolean)
    });
  });
};
