import fs from 'fs';
import path from 'path';
import {execSync} from 'child_process';
import fse from 'fs-extra';
import {IApi} from '@baidu/cba-preset';
import {logger} from '@baidu/cba-utils';
import configuration from '../configuration';

export default (api: IApi) => {
  api.registerCommand({
    name: 'restore',
    description: 'SDK 调试变更恢复',
    fn: async ({config}) => {
      configuration.setConfig(config);

      const getPath = configuration.getPath.bind(configuration);
      const getCwd = configuration.getCwd.bind(configuration);

      const packageJsonFilePath = getPath('package.json');

      const packageJson = JSON.parse(fs.readFileSync(packageJsonFilePath, 'utf-8'));

      if (packageJson.module === 'es/index.js') {
        return;
      }

      const configFilePath = configuration.getTempConfigFilePath();

      fse.ensureFileSync(configFilePath);

      let configFileContent: any = fs.readFileSync(configFilePath, 'utf-8');

      if (!configFileContent) {
        configFileContent = {};
        fs.writeFileSync(configFilePath, JSON.stringify(configFileContent, undefined, 2), 'utf-8');
      } else {
        configFileContent = JSON.parse(configFileContent);
      }

      if (!configFileContent.debugProjectPath || !fs.existsSync(configFileContent.debugProjectPath)) {
        logger.error(`The debug project path ${configFileContent.debugProjectPath} is not fount.`);
        process.exit(1);
      }

      const {debugProjectPath} = configFileContent;

      const reactPath = path.resolve(debugProjectPath, 'node_modules/react');
      const reactDomPath = path.resolve(debugProjectPath, 'node_modules/react-dom');
      const reactRouterDomPath = path.resolve(debugProjectPath, 'node_modules/react-router-dom');

      if (!fs.existsSync(reactPath)) {
        logger.error(`The path ${reactPath} is not found.`);
        process.exit(1);
      }

      if (!fs.existsSync(reactDomPath)) {
        logger.error(`The path ${reactDomPath} is not found.`);
        process.exit(1);
      }

      if (!fs.existsSync(reactRouterDomPath)) {
        logger.error(`The path ${reactRouterDomPath} is not found.`);
        process.exit(1);
      }

      if (
        fs.lstatSync(getPath('node_modules/react')).isSymbolicLink() &&
        fs.existsSync(getPath('node_modules/react'))
      ) {
        execSync(`cd ${getCwd()} && yarn unlink react && cd ${reactPath} && yarn unlink`, {
          stdio: 'inherit',
          cwd: getCwd()
        });
      }

      if (
        fs.lstatSync(getPath('node_modules/react-dom')).isSymbolicLink() &&
        fs.existsSync(getPath('node_modules/react-dom'))
      ) {
        execSync(`cd ${getCwd()} && yarn unlink react-dom && cd ${reactDomPath} && yarn unlink`, {
          stdio: 'inherit',
          cwd: getCwd()
        });
      }

      if (
        fs.lstatSync(getPath('node_modules/react-router-dom')).isSymbolicLink() &&
        fs.existsSync(getPath('node_modules/react-router-dom'))
      ) {
        execSync(`cd ${getCwd()} && yarn unlink react-router-dom && cd ${reactRouterDomPath} && yarn unlink`, {
          stdio: 'inherit',
          cwd: getCwd()
        });
      }

      const packageName = packageJson.name;

      if (
        fs.lstatSync(path.resolve(debugProjectPath, `node_modules/${packageName}`)).isSymbolicLink() &&
        fs.existsSync(path.resolve(debugProjectPath, `node_modules/${packageName}`))
      ) {
        execSync(`cd ${getCwd()} && yarn unlink`, {
          stdio: 'inherit',
          cwd: getCwd()
        });
      }

      const npmUnlinkCommand = 'yarn install --check-files';
      logger.info(`Starting to execute ${npmUnlinkCommand}`);
      execSync(`${npmUnlinkCommand}`, {
        stdio: 'inherit',
        cwd: getCwd()
      });
      logger.info('Execute npm unlink successfully.');

      logger.info('Starting to update package.json...');

      packageJson.module = 'es/index.js';
      packageJson.main = 'es/index.js';
      packageJson.typings = 'es/index.d.ts';
      fs.writeFileSync(packageJsonFilePath, JSON.stringify(packageJson, undefined, 2));
      logger.info('Update package.json file successfully.', 1, 1);

      execSync(`cd ${debugProjectPath} && yarn --check-files && cd ${getCwd()}`, {
        stdio: 'inherit'
      });

      logger.info('All restore operations were executed successfully.');
    }
  });
};
