import {
  logger,
  getPackageLatestVersion,
  updatePackageCliVersion,
  downloadTemplate,
  jsonToReactTpl,
  formatFile,
  formatCode,
  type IReactJson
} from '@baidu/cba-utils';
import {join} from 'path';
import {readFileSync, writeFileSync} from 'fs-extra';

import inquirer from 'inquirer';
import {writeTpl2File} from '../utils';
import {IApi} from '../types';
import {addProject} from '.';
import {reactI18nTpl} from './tplBlock';
import type {IPrompt} from '.';

async function dealwithReactExtPrompts(prompt: IPrompt) {
  await dealwithReactI18n(prompt);
}

async function dealwithReactI18n(prompt: IPrompt) {
  const {projectName, enableI18n} = prompt;
  let appDomJson: IReactJson = {
    type: 'BrowserRouter',
    children: [
      {
        type: 'div',
        props: {
          className: 'app'
        },
        children: [
          {
            type: 'Router'
          }
        ]
      }
    ]
  };
  let i18nImport = '';
  let i18nCodeBlock = '';

  // 1、修改入口页面
  if (enableI18n) {
    appDomJson = {
      type: 'I18nProvider',
      props: {
        i18n: '{i18nInstance}',
        defaultNS: 'translation',
        i18nUtil: '{i18nUtil}'
      },
      children: [appDomJson]
    };
  }
  i18nImport = reactI18nTpl.i18nImport;
  i18nCodeBlock = reactI18nTpl.i18nCodeBlock;
  const tplPath = join(__dirname, '../../template/react-project/src/index.tpl');
  const filePath = join(process.cwd(), `${projectName ? projectName : ''}/src/index.tsx`);
  const appDomTpl = await formatCode(jsonToReactTpl(appDomJson), filePath);
  writeTpl2File({
    tplPath,
    targetPath: filePath,
    data: {
      enableI18n,
      appDom: appDomTpl,
      i18nImport,
      i18nCodeBlock
    }
  });
  await formatFile(filePath);
  // 2、添加package.json依赖、脚本
  if (enableI18n) {
    const pkgPath = join(process.cwd(), `${projectName ? projectName : ''}/package.json`);
    const pkg = JSON.parse(
      readFileSync(pkgPath, {
        encoding: 'utf-8'
      })
    );
    const toolkitVersion = await getPackageLatestVersion('@baidu/bce-react-toolkit');
    const bdI18nVersion = await getPackageLatestVersion('@baiducloud/i18n');
    const i18nVersion = await getPackageLatestVersion('@baidu/cba-i18n');
    pkg.dependencies['@baidu/bce-react-toolkit'] = `^${toolkitVersion}`;
    pkg.dependencies['@baiducloud/i18n'] = `^${bdI18nVersion}`;
    pkg.devDependencies['@baidu/cba-i18n'] = `${i18nVersion}`;
    pkg.scripts['i18n:extract'] = 'npx cba-cli i18n:extract';
    pkg.scripts['i18n:upload'] = 'npx cba-cli i18n:upload';
    writeFileSync(pkgPath, JSON.stringify(pkg, undefined, 2));
  }
  // 3、配置文件
  const bceConfigTplPath = join(__dirname, '../../template/react-project/bce-config.tpl');
  const bceConfigPath = join(process.cwd(), `${projectName ? projectName : ''}/bce-config.js`);
  const extBceConfigData = {
    extPresets: enableI18n ? ['@baidu/cba-i18n'] : [],
    enableI18n
  };
  await writeTpl2File({
    tplPath: bceConfigTplPath,
    targetPath: bceConfigPath,
    data: extBceConfigData
  });
  // 4、添加国际化示例
  const homeIndexTplPath = join(__dirname, '../../template/react-project/src/pages/home/<USER>');
  const homeIndexPath = (process.cwd(), `${projectName ? projectName : ''}/src/pages/home/<USER>
  await writeTpl2File({
    tplPath: homeIndexTplPath,
    targetPath: homeIndexPath,
    data: {
      enableI18n
    }
  });
}

export default async (api: IApi) => {
  addProject({
    name: 'React独立应用',
    key: 'react',
    extPrompts: () => {
      return inquirer.prompt([
        {
          type: 'list',
          message: '是否开启国际化',
          name: 'enableI18n',
          default: false,
          choices: [
            {
              name: '不开启国际化',
              value: false
            },
            {
              name: '开启国际化',
              value: true
            }
          ]
        }
      ]);
    },
    initFn: async (prompt: IPrompt) => {
      const {projectName} = prompt;
      logger.info('初始化React独立应用');
      await downloadTemplate(projectName, 'react-project');
      await dealwithReactExtPrompts(prompt);
      const pkgPath = projectName ? `${projectName}/package.json` : 'package.json';

      updatePackageCliVersion(pkgPath);
    }
  });
};
