/**
 * @file 插件类
 * <AUTHOR> (<EMAIL>)
 */

import {dirname, join, relative, basename, extname} from 'path';
import {logger, winPath} from '@baidu/cba-utils';
import lodash from 'lodash';
import {pkgUpSync} from '../../compiled/pkg-up';
import {Env} from '../types';

type PluginType = 'plugin' | 'preset';
export class Plugin {
  /**
   * 当前工作目录
   */
  cwd: string;
  /**
   * 插件类型 'plugin' | 'preset'
   */
  type: PluginType;
  /**
   * 插件路径
   */
  path: string;
  /**
   * 插件id
   */
  id: string;
  /**
   * 插件key
   */
  key: string;
  /**
   * 插件执行函数
   */
  apply: () => any;

  enableBy: ((opts: {userConfig: any; env: Env}) => boolean) | boolean = true;

  constructor(opts: any) {
    this.type = opts.type;
    this.path = winPath(opts.path);
    this.cwd = opts.cwd;

    let pkg: any = null;
    let isPkgEntry = false;

    const pkgJSONPath = pkgUpSync({cwd: this.path})!;
    if (pkgJSONPath) {
      pkg = require(pkgJSONPath);
      isPkgEntry = winPath(join(dirname(pkgJSONPath), pkg?.main || 'index.js')) === winPath(this.path);
    }
    this.id = this.getId({pkg, isPkgEntry, pkgJSONPath});
    this.key = this.getKey({pkg, isPkgEntry, pkgJSONPath});

    this.apply = () => {
      let ret;
      try {
        ret = require(this.path);
      } catch (e: any) {
        logger.error(`Register ${this.type} 「${this.path}」 failed, since ${e.message}`);
      }
      return ret.__esModule ? ret.default : ret;
    };
  }

  static stripNoneScope(name: string = '') {
    if (name.startsWith('@') || name.startsWith('@baidu/')) {
      name = name.split('/')[1];
    }
    return name || '';
  }

  static getPluginsAndPresets(opts: {cwd: string; plugins?: string[]; presets?: string[]}) {
    function get(type: 'plugin' | 'preset') {
      const types = `${type}s` as 'plugins' | 'presets';
      return [...(opts[types] || [])].map(path => {
        return new Plugin({
          path: path,
          type,
          cwd: opts.cwd
        });
      });
    }
    return {
      presets: get('preset'),
      plugins: get('plugin')
    };
  }

  /**
   * 获取插件id
   * @param opts
   * @returns string
   */
  getId(opts: {pkg: any; isPkgEntry: boolean; pkgJSONPath: string | null}) {
    let id;
    if (opts.isPkgEntry) {
      id = opts.pkg!.name;
    } else if (winPath(this.path).startsWith(winPath(this.cwd))) {
      id = `./${winPath(relative(this.cwd, this.path))}`;
    } else if (opts.pkgJSONPath && opts.pkg!.name) {
      id = winPath(join(opts.pkg!.name, relative(dirname(opts.pkgJSONPath), this.path)));
    } else {
      id = winPath(this.path);
    }
    id = id.replace(/\.js$/, '');
    return id;
  }

  /**
   * 获取插件key
   * 默认key：包名+文件路径+文件名
   * @param opts
   * @returns
   */
  getKey(opts: {pkg: any; isPkgEntry: boolean; pkgJSONPath: string}) {
    // e.g.
    // initial-state -> initialState
    // webpack.css-loader -> webpack.cssLoader
    function nameToKey(name: string) {
      return name
        .split('.')
        .map(part => lodash.camelCase(part))
        .join('.');
    }

    if (opts.pkgJSONPath) {
      const relativePath = relative(dirname(opts.pkgJSONPath), this.path);
      const key = nameToKey(
        `${Plugin.stripNoneScope(opts.pkg.name + dirname(relativePath))}-${basename(this.path, extname(this.path))}`
      );
      return key;
    }
    return `${Plugin.stripNoneScope(opts.pkg?.name)}:${basename(this.path, extname(this.path))}`;
  }

  /**
   * 合并插件数据
   * @param opts
   */
  merge(opts: {key?: string; enableBy?: any}) {
    if (opts.key) this.key = opts.key;
    if (opts.enableBy) this.enableBy = opts.enableBy;
  }
}
