import {useCallback, useEffect, useMemo, useState} from 'react';

/** 前端分页筛选 */
export default function usePaginationFiltering({
  orgDataSource = [] as any[],
  defaultPageNo = 1,
  defaultPageSize = 10,
  filterMap = {} as {[key: string]: string}
} = {}) {
  const [pageNo, setPageNo] = useState(defaultPageNo);
  const [pageSize, setPageSize] = useState(defaultPageSize);
  const [filters, setFilters] = useState<{[key: string]: any[]}>({});

  useEffect(() => {
    setPageNo(defaultPageNo);
    setPageSize(defaultPageSize);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [orgDataSource]);

  /** 监听分页发生变化 */
  const onPaginationChange = useCallback(
    (_page: number, _pageSize?: number) => {
      setPageNo(_page);
      if (_pageSize) setPageSize(_pageSize);
    },
    []
  );
  /** 监听表格发生变化 */
  const onTableChange = useCallback(
    (_: any, _filters: {[key: string]: any}) => {
      setPageNo(1);
      setFilters(_filters);
    },
    []
  );

  const [dataSource, total] = useMemo(() => {
    const result = orgDataSource.filter((item) => {
      return Object.keys(filters).every((key) => {
        const relKey = filterMap[key] || key;
        return !filters[key] || filters[key].includes(item[relKey]);
      });
    });
    return [
      result.slice((pageNo - 1) * pageSize, pageNo * pageSize),
      result.length
    ];
  }, [filterMap, filters, orgDataSource, pageNo, pageSize]);

  return {
    dataSource,
    total,
    pageNo,
    pageSize,
    onPaginationChange,
    onTableChange
  };
}
