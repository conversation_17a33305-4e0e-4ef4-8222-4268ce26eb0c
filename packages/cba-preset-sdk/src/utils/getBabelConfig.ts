import fs from 'fs';
import configuration from '../configuration';

export default function getBabelConfig() {
  let babelConfigFile = configuration.getPath('./.babelrc');

  if (fs.existsSync(babelConfigFile)) {
    return JSON.parse(fs.readFileSync(babelConfigFile, 'utf8'));
  }

  babelConfigFile = configuration.getPath('./babel.config.json');

  if (fs.existsSync(babelConfigFile)) {
    return JSON.parse(fs.readFileSync(babelConfigFile, 'utf8'));
  }

  return {};
}
