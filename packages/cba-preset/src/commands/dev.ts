/**
 * @file 项目本地启动
 * <AUTHOR> (<EMAIL>)
 */
import {IApi} from '../types';
import {dev as webpackDevServer} from '../bundler-webpack/dev';

export default (api: IApi) => {
  api.describe({
    key: 'preset:dev',
    enableBy() {
      return api.name === 'dev';
    }
  });

  api.registerCommand({
    name: 'dev',
    description: 'dev server for development',
    fn: async () => {
      const chainWebpack = async (config: any, args: Object) => {
        return await api.applyPlugins({
          key: 'chainWebpack',
          type: api.ApplyPluginsType.modify,
          initialValue: config,
          args
        });
      };

      const modifyWebpackConfig = async (webpackConfig: any, args: Object) => {
        return await api.applyPlugins({
          key: 'modifyWebpackConfig',
          initialValue: webpackConfig,
          args
        });
      };

      const onBeforeServerStart = async (args: Object) => {
        return await api.applyPlugins({
          key: 'onBeforeServerStart',
          args
        });
      };

      const onServerStarted = async (args: Object) => {
        return await api.applyPlugins({
          key: 'onServerStarted',
          args
        });
      };

      const options = {
        env: api.env,
        cwd: api.cwd,
        userConfig: api.userConfig,
        chainWebpack,
        modifyWebpackConfig,
        onBeforeServerStart,
        onServerStarted
      };
      await webpackDevServer(options);
    }
  });
};
