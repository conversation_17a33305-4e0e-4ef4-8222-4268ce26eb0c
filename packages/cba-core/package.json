{"name": "@baidu/cba-core", "version": "1.2.8-beta.14", "description": "BaiduCloud Console-FE CLI", "main": "lib/index.js", "scripts": {"build": "cba-scripts build", "dev": "cba-scripts dev"}, "files": ["compiled", "lib", "runtime"], "license": "MIT", "dependencies": {"@baidu/cba-utils": "workspace:*", "chalk": "^2.4.2", "debug": "^4.1.1", "dotenv": "^16.1.4", "dotenv-expand": "^10.0.0", "lodash": "^4.17.21", "log-symbols": "^3.0.0", "tapable": "2.2.1", "yargs-parser": "^21.1.1"}, "devDependencies": {"@types/debug": "^4.1.8", "@types/lodash": "^4.14.202", "@types/yargs-parser": "^21.0.0"}}