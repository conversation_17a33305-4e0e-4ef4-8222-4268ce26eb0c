import {MenuItem, recursiveMenus} from '@baidu/bce-react-toolkit';
import React from 'react';

import urls from '@/utils/urls';

const Activation = React.lazy(
  () => import(/* webpackChunkName: "Activation" */ '@/pages/Activation')
);

const AuditReport = React.lazy(
  () => import(/* webpackChunkName: "AuditReport" */ '@/pages/AuditReport')
);

/** 菜单定义 */
const menus: MenuItem[] = [
  {
    menuName: '开通页',
    key: urls.activation,
    isNavMenu: true,
    Component: Activation,
    isPageWrapperNotRequired: true
  },
  {
    menuName: '二级菜单',
    key: '/secure/audit',
    isNavMenu: true,
    isDefaultOpened: false,
    children: [
      {
        menuName: '列表页',
        key: urls.auditReport,
        isNavMenu: true,
        Component: AuditReport
      }
    ]
  }
];

/** 打平之后的菜单列表 */
export const flattenedMenuList = recursiveMenus(menus);

export default menus;
