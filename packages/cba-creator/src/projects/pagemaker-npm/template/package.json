{"name": "@fex/cba-npm-widget-demo", "version": "1.0.27", "description": "", "main": "dist/index.js", "files": ["dist/*"], "scripts": {"dev": "cba-cli dev", "build": "cba-cli build"}, "framework": "react", "author": "fex", "license": "MIT", "dependencies": {}, "devDependencies": {"@baidu/cba-cli": "^1.1.0", "@baidu/cba-preset-pagemaker-npm": "^1.1.0", "@babel/preset-env": "^7.23.9", "@babel/preset-react": "^7.23.3", "@babel/preset-typescript": "^7.23.3", "@babel/plugin-transform-runtime": "^7.23.9"}, "amis-widgets": [{"name": "info-card", "framework": "react", "usage": "renderer", "type": "info-card", "entry": "/dist/index", "editorPlugin": {"name": "信息展示卡片", "description": "信息展示卡片", "pluginEntry": "/dist/plugin", "tag": ["自定义"], "sort": 100}}]}