# 百度智能云React CLI工具

> @baidu/cba-cli (cba全称 create-bce-app)是智能云提供的统一Console项目React脚手架工具，提供多种项目初始化、本地调试、各工具接入等能力。CLI工具严格遵守[百度智能云前端规范](http://sandbox.bce.console.baidu-int.com/bce-docs/)

## 安装

全局安装@baidu/cba-cli，镜像源使用百度镜像：

```shell
npm install @baidu/cba-cli -g --registry=http://registry.npm.baidu-int.com
```

## 使用

@baidu/cba-cli的所有自定义配置均可在bce-config.js进行定义，内容包括：

- name：工具模块名称，例如：@baidu/bce-bcc-sdk
- service：在P3M平台注册的service type，默认name全大写
- appName：应用缩写，表明访问的path，默认代码库后几位字母
- flags：获取的功能清单的模块，默认是service
- proxyTarget：代理地址
- webpack：自定义webpack，可选，默认一个入口 index.js/index.ts
- i18n: 国际化配置

您可以通过init命令初始化自己的项目，将为您生成对应的bce-config.js。

### 项目初始化

```shell
cd ${项目仓库目录}
## 初始化项目
npx cba-cli init
## ...根据提示进行操作
```

### 项目启动

```shell
# 调试产品模块
npx cba-cli dev

# 指定配置文件
npx cba-cli dev --config=bce-config.js // 产品模块

# or package.json script
npm run dev
```

<!-- ### 本地mock
```shell
npx cba-cli dev --mock
``` -->

### 项目构建

```shell
# 构建产品模块
npx cba-cli build

# 指定配置文件
npx cba-cli build --template=public-console --config=bce-config.js

# or package.json script
npm run build
```

### 测试独立发布

为了方便新技术测试，我们提供了测试版本独立发布功能，应用构建时，通过环境变量`ALPHA_RELEASE=true`开启测试版本独立发布

`export ALPHA_RELEASE=true && npm run build`

同时需要在iPipe流水线中【部署沙盒】步骤中通过环境变量开启测试版本独立发布`export ALPHA_RELEASE=true`，部署完成后会通过`alpha_version`读取测试版本，并加载对应版本静态资源，和正式版本互相不受影响

## 相关文档

- [使用文档](https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/tQvrZcAEPS/4yidKCir54/Yl586IZSCNxzLl)
- [版本变更信息](/changelog.md)
- [脚手架工程开发文档](/DEVELOPER.md)
