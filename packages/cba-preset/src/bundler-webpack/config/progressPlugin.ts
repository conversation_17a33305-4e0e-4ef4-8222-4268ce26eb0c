/**
 * @file webpack添加progressPlugin插件
 * <AUTHOR> (<EMAIL>)
 */

import webpack from 'webpack';
import Config from 'webpack-chain';

interface IOpts {
  config: Config;
}

export async function addProgressPlugin(opts: IOpts) {
  let processEnd = false;
  const {config} = opts;
  config.plugin('preset:ProgressPlugin').use(webpack.ProgressPlugin, [
    {
      handler(percentage: number, message: string, status = '') {
        if (status.includes('closing') || status === 'shutdown' || message === 'end') {
          processEnd = true;
          return;
        }
        if (processEnd) {
          return;
        }

        const progressBarWidth = 50;
        const progress = Math.round(percentage * progressBarWidth);
        let road = Array(progressBarWidth).fill('_');
        road[Math.max(0, progress - 1)] = '✈️';
        const progressBar = `> ${road.join('')}`;
        process.stdout.write(
          `\r${progressBar} ${Math.floor(percentage * 100)}% ${percentage === 1 ? 'completed!\n' : message}`
        );
      },
      activeModules: false,
      entries: true,
      modules: true,
      modulesCount: 5000,
      profile: false,
      dependencies: true,
      dependenciesCount: 10000,
      percentBy: null
    }
  ]);
}
