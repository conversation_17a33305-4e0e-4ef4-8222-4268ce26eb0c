/**
 * @file 环境变量
 * <AUTHOR> (<EMAIL>)
 */

import {existsSync, readFileSync} from 'fs';
import {join} from 'path';
import {parse} from 'dotenv';
import {expand} from 'dotenv-expand';
import {Env} from '../types';

interface IOpts {
  /**
   * 当前文件路径
   */
  cwd: string;
  /**
   * 当前执行环境
   */
  env: Env;
  /**
   * 环境变量文件名，默认为.env
   */
  envFile: string;
  /**
   * 附加的环境
   */
  additionalEnv?: Record<string, any>;
}

export function loadEnv(opts: IOpts = {cwd: '', env: Env.production, envFile: '.env'}) {
  const files = [join(opts.cwd, opts.envFile)];

  if (opts.env === Env.development) {
    files.push(join(opts.cwd, `${opts.envFile}.local`));
  }

  if (opts.additionalEnv) {
    Object.entries(opts.additionalEnv).forEach(([key, value]) => {
      process.env[key] = value;
    });
  }

  for (const file of files) {
    if (!existsSync(file)) continue;
    const parsed: Record<string, string> = parse(readFileSync(file)) || {};
    expand({parsed, ignoreProcessEnv: true});
    for (const key of Object.keys(parsed)) {
      process.env[key] = parsed[key];
    }
  }

  const rawEnv = Object.keys(process.env).reduce(
    (prev: Record<string, string | undefined>, key: string) => {
      prev[key] = process.env[key];
      return prev;
    },
    {
      NODE_ENV: process.env.NODE_ENV || 'development'
    }
  );

  return rawEnv;
}
