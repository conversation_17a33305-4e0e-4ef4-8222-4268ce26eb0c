import {
  IApi,
  ProjectType
} from '../types';

export default (api: IApi) => {
  api.describe({
    key: 'cba-creator:pagemaker',
  });

  api.addProject(() => {
    return {
      name: 'Pagemaker 应用',
      value: ProjectType.pagemaker,
      children: [
        {
          name: 'Pagemaker 本地应用',
          value: ProjectType.pagemakerReact,
        },
        {
          name: 'Pagemaker 组件扩展包',
          value: ProjectType.pagemakerNpm,
        }
      ]
    };
  });

};
