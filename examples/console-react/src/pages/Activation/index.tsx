import {useFrameworkContext, useTranslation} from '@baidu/bce-react-toolkit';
import {Alert} from 'acud';
import cx from 'classnames';
import React from 'react';

import Banner from './components/Banner';
import styles from './index.module.less';

const Activation: React.FC = () => {
  const {isRealNameVerified} = useFrameworkContext();

  const {t} = useTranslation();

  return (
    <>
      {!isRealNameVerified ? (
        <Alert
          message={
            <div className={styles.alertContentContainer}>
              <div>
                您的账号未通过实名认证，将无法完成购买，建议尽快进行实名认证。
              </div>
              <a
                className={styles.verifyRealNameBtn}
                href="https://console.bce.baidu.com/qualify/#/qualify/index"
              >
                实名认证
              </a>
            </div>
          }
          banner
          className={styles.alertContainer}
        />
      ) : null}

      <Banner></Banner>

      <div className={styles.introModule}>
        <div className={styles.title}>产品优势</div>

        <div className={styles.introItemContainer}>
          <div className={styles.introItem}>
            <div className={styles.header}>
              <img
                className={styles.icon}
                src="https://db-console-fe.bj.bcebos.com/dbsc/20231227/activation/flexible_auditing.png"
              />
              <div className={styles.headerTitle}>灵活审计</div>
            </div>
            <div className={styles.desc}>
              支持多种审计规则灵活选择，支持审计报告定制化输出。
            </div>
          </div>

          <div className={styles.introItem}>
            <div className={styles.header}>
              <img
                className={styles.icon}
                src="https://db-console-fe.bj.bcebos.com/dbsc/20231227/activation/simple_to_use.png"
              />
              <div className={styles.headerTitle}>简单易用</div>
            </div>
            <div className={styles.desc}>
              能够联动云数据库 RDS，接入便捷，方便统一管理。
            </div>
          </div>
        </div>
      </div>

      <div className={styles.introModule}>
        <div className={styles.title}>{t('产品功能')}</div>

        <div className={styles.introItemContainer}>
          <div className={styles.introItem}>
            <div className={styles.header}>
              <img
                className={styles.icon}
                src="https://db-console-fe.bj.bcebos.com/dbsc/20231227/activation/security_audit.png"
              />
              <div className={styles.headerTitle}>安全审计</div>
            </div>
            <div className={styles.desc}>
              提供高风险请求识别、SQL注入检测、访问来源识别等服务，快速识别数据库异常行为，保障数据库安全。
            </div>
          </div>

          <div className={styles.introItem}>
            <div className={styles.header}>
              <img
                className={styles.icon}
                src="https://db-console-fe.bj.bcebos.com/dbsc/20231227/activation/intelligent_operations_and_maintenance.png"
              />
              <div className={styles.headerTitle}>智能运维（敬请期待）</div>
            </div>
            <div className={styles.desc}>
              提供异常检测、慢SQL分析、锁分析等运维能力，保障数据库服务稳定。
            </div>
          </div>

          <div className={styles.introItem}>
            <div className={styles.header}>
              <img
                className={styles.icon}
                src="https://db-console-fe.bj.bcebos.com/dbsc/20231227/activation/data_development.png"
              />
              <div className={styles.headerTitle}>数据开发（敬请期待）</div>
            </div>
            <div className={styles.desc}>
              提供一站式可视化数据交互窗口，支持对接入的数据库进行增删改查操作。
            </div>
          </div>
        </div>
      </div>

      <div className={styles.introModule}>
        <div className={styles.title}>{t('应用场景')}</div>

        <div className={styles.introItemContainer}>
          <div className={cx(styles.introItem, styles.introItemWithBorder)}>
            <div className={styles.header}>
              <img
                className={styles.icon}
                src="https://db-console-fe.bj.bcebos.com/dbsc/20231227/activation/compliance_audit.png"
              />
              <div className={styles.headerTitle}>合规审计</div>
            </div>
            <div className={styles.desc}>
              DBSC可对数据库追踪审计，查询历史某时间点发生所有事件，形成审计报表。
            </div>
          </div>

          <div className={cx(styles.introItem, styles.introItemWithBorder)}>
            <div className={styles.header}>
              <img
                className={styles.icon}
                src="https://db-console-fe.bj.bcebos.com/dbsc/20231227/activation/daily_operations_and_maintenance.png"
              />
              <div className={styles.headerTitle}>日常运维</div>
            </div>
            <div className={styles.desc}>
              DBSC可对数据库运行状态进行7*24实时检测，利用AI技术为用户提供优化建议，大幅提升运维效率。
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Activation;
