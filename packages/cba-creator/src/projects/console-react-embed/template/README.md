# 百度智能云 React 第三方模块工程项目

## 写在前面

本仓库是基于 **@baidu/cba-cli** 搭建的一个前端工程项目，以第三方模块的形式接入已有SDP或EDP技术栈

我们的目标是为开发者（不限于云控制台业务）提供**一套现代化的、高性能的、可定制的、易用的、高度透明且全方位的通用场景开发解决方案**。

我们希望每位开发者不仅能在本模板中寻找到解决通用业务问题的答案，还能获得更出色的开发体验，从而能够更高效地支持产品需求的快速迭代。

> 详细文档可查阅：[智能云控制台应用接入第三方React模块](https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/tQvrZcAEPS/4yidKCir54/6eSBVqRQycQDt0)


## 如何使用
> 🔔  创建项目之后：
>
> - 通过 `@asModule`装饰器注册第三方模块，后续提供给 `EmbedWrapper` 组件进行渲染；
>
> - 可删除 `@/pages` 目录下面的路由界面组件，新增页面功能时，仅需要关注在 `@/pages/index.tsx` 进行页面注册即可；
>
> - 可删除 `@/apis` 目录下面的 api 文件；
>
> - 可删除 `@/assets` 中的 `png` 和 `svg` 文件；
>
> - 可删除 `@/components` 目录下的文件；
>
> - 可删除 `@/styles` 目录下的文件；


相关运行命令如下：

```shell
# 本地联调沙盒环境
npm run dev

# 生产环境构建
npm run build

# 国际化语料提取
npm run i18n:extract

# 国际化语料上传
npm run i18n:upload
```


> 🔔  若本地运行项目之后，浏览器出现 https 链接安全提示时，如下图，请直接键入 `thisisunsafe` ， 浏览器会信任  https 链接并自动刷新。
>
> ![](http://bj.bcebos.com/ibox-thumbnail98/********************************?authorization=bce-auth-v1%2Ffbe74140929444858491fbf2b6bc0935%2F2024-01-22T08%3A23%3A20Z%2F1800%2F%2F9725f457430ec343745b048dd1d9de3b4d826c12bd44cf614c38f94c8ff3f33b)


## 配置详解
**配置项** 具体配置可参考 [配置项 使用文档](https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/tQvrZcAEPS/4yidKCir54/F8PMyCoJR_WIvw)


### 路径别名配置

项目模板支持 "@" 路径别名使用和引用智能提示，主要通过修改 webpack resolve 和 tsconfig.json 文件来实现。


## 功能详解

### API Request

🔔  推荐的使用方式，如下：

在 `@/apis` 路径下，新建业务对应的 `.ts` 文件，如 `auth.ts` 文件存放鉴权相关的接口：

```typescript
import {request} from '@baidu/bce-react-toolkit';

/** 激活产品角色 */
export function activateIamStsRole(params: {
  roleName: string;
  accountId: string;
  serviceId: string;
  policyId: string;
}): Promise<{
  status: number;
  success: boolean;
}> {
  return request({
    url: '/api/iam/sts/role/activate',
    method: 'POST',
    data: params
  });
}
```

> 另外新增 `silent` 参数，支持进行静默请求，当接口报错时，不进行 toast 提示, 具体使用示例如下：
>
> ```tsx
> return request({
>     url: '/api/iam/sts/role/activate',
>     method: 'POST',
>     data: params,
>     silent: true
> });
> ```


### 路由懒加载

在 `@/pages/index.tsx` 中进行菜单注册时，可通过 `React.lazy` 进行引用页面组件，示例代码如下：

```tsx
const Activation = React.lazy(
  () => import(/* webpackChunkName: "Activation" */ '@/pages/Activation')
);

/** 菜单定义 */
const menus: MenuItem[] = [
  {
    menuName: '概览',
    key: urls.activation,
    isNavMenu: true,
    Component: Activation
  },
];
```

> 🔔  当需要自定义 chunk 文件的名称时，可通过 `webpackChunkName` 的注释来赋值 chunk 文件名称。



### 国际化

国际化不仅要考虑项目中文字内容的翻译，还要兼顾日期时间、货币格式在不同国家地区间的区别。

本项目采用社区中比较成熟的国际化解决方案 封装到`@baidu/bce-react-toolkit`库中，来满足云产品不同场景下的多语言需求。


#### 使用方式

在 `@baidu/bce-react-toolkit`中可以通过 `useTranslation` hook 进行使用，示例代码如下：

```tsx
import {useTranslation} from '@baidu/bce-react-toolkit';

function Compt() {
  const {t} = useTranslation();

  return <div>{t('待翻译内容')}</div>
}
```

> 🔔  更多使用方法可参考：[react.i18next](https://react.i18next.com/guides/quick-start#translate-your-content)，推荐使用 hook 形式进行使用。



#### 国际化配置

在应用配置文件 `bce-config.js` 中，`i18n` 的配置项示例代码如下：

```ts
{
  ...,
  i18n: {
    /** 是否开启国际化 */
    enabled?: boolean;
    /** 是否启用独立国际化，语料将不上传至云桥，由项目自行维护 */
    independent?: boolean;
    /** 支持的语言类型 */
    supportedLanguages?: string[];
    /** 语料输出的路径, 可为绝对路径和相对路径，相对路径根据当前工作目录查找 */
    output?: string;
  }
}
```

> 🔔   `i18n:extract` 用于提取语料，`i18n:upload` 用于将提取之后的语料转换为 `.po` 文件，然后上传至`云桥国际化平台`。


### 功能清单

[功能清单配置链接](http://bce.console.baidu-int.com/inventory/#/plat-ops-inventory/product/list)

flags文件为运行时文件，在每次项目启动或打包时都会根据配置文件中的templateId和flags重新生成。


#### 使用方式

```tsx
  import flags from '@/flags';

  export default Demo = (props) => {
    return (
      <div>
        {
          flags.SmsDwzDisable && <p>
            使用优惠券
          </p>
        }
        {
          flags.SmsDwzDisable && <p>
            使用短链
          </p>
        }
      </div>
    );
}
```


## 部署流程

通过配置流水线编译发布
