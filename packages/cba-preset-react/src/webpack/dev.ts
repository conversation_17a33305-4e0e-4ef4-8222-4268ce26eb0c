import shell from 'shelljs';
import path from 'path';
import {IApi} from '@baidu/cba-preset';
import {getFlags, logger} from '@baidu/cba-utils';
import ReactRefreshWebpackPlugin from '@pmmmwh/react-refresh-webpack-plugin';
import HtmlWebpackPlugin from 'html-webpack-plugin';

export default (api: IApi) => {
  api.describe({
    key: 'preset-react:webpack-dev',
    enableBy: () => api.env === 'development'
  });

  api.modifyWebpackConfig((webpackConfig: any, {merge}: any) => {
    const userConfig = api.userConfig;

    const port = userConfig.port || 8899;
    const host = userConfig.host || 'localhost';
    const publicPath = userConfig.publicPath || '/';

    const moduleName = `${userConfig.appName}AppModule`;
    const templatePath = api.userConfig.htmlTemplate
      ? path.isAbsolute(api.userConfig.htmlTemplate)
        ? api.userConfig.htmlTemplate
        : path.join(api.cwd, api.userConfig.htmlTemplate)
      : 'index.html';

    webpackConfig = merge(webpackConfig, {
      devtool: 'eval-source-map',
      plugins: [
        new ReactRefreshWebpackPlugin(),
        new HtmlWebpackPlugin({
          moduleName: moduleName,
          template: templatePath,
          env: 'development'
        })
      ],
      output: {
        filename: 'static/js/[name].[contenthash:8].js',
        chunkFilename: 'static/js/[name].chunk.[contenthash:8].js',
        assetModuleFilename: 'static/js/[hash:10][ext][query]',
        publicPath,
        library: `${userConfig.appName}AppModule`
      },
      devServer: {
        allowedHosts: 'all',
        open: true,
        historyApiFallback: true,
        port,
        host,
        hot: true,
        compress: true,
        https: userConfig.https || false,
        client: {
          progress: false,
          reconnect: 5
        }
      }
    });
    return webpackConfig;
  });

  api.onBeforeServerStart(async ({origin}: {origin: string}) => {
    // 根据命令行参数中是否包含 hasFlags 来决定是否调用 getFlags 方法
    const hasFlags = api.args.hasFlags;

    if (hasFlags) {
      const flagPath = path.join(api.cwd, './src/flags.ts');
      await getFlags(
        {
          templateId: api.userConfig.templateId,
          flags: api.userConfig.flags
        },
        flagPath
      );
    }
    logger.info(`开发调试： ${origin}`);
  });
};
