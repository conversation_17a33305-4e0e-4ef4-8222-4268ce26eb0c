{"name": "@baidu/cba-preset-sdk", "version": "1.2.8-beta.14", "license": "MIT", "main": "lib/index.js", "files": ["lib"], "scripts": {"build": "cba-scripts build", "dev": "cba-scripts dev"}, "dependencies": {"@babel/core": "^7.23.9", "@babel/preset-env": "^7.23.9", "@babel/preset-react": "^7.23.3", "@baidu/cba-core": "workspace:*", "@baidu/cba-preset": "workspace:*", "@baidu/cba-utils": "workspace:*", "@typescript-eslint/eslint-plugin": "^6.20.0", "autoprefixer": "^10.4.17", "enquirer": "^2.4.1", "fs-extra": "^11.2.0", "gulp": "^4.0.2", "gulp-babel": "^8.0.0", "gulp-concat": "^2.6.1", "gulp-typescript": "^6.0.0-alpha.1", "inquirer": "^8.2.6", "less": "^4.2.0", "merge2": "^1.4.1", "rimraf": "^5.0.5", "semver": "^7.5.4", "through2": "^4.0.2", "ttypescript": "^1.5.15", "typescript-transform-paths": "^3.5.3"}, "devDependencies": {"@types/babel__core": "^7.20.5", "@types/fs-extra": "^11.0.4", "@types/gulp": "^4.0.17", "@types/gulp-babel": "^6.1.33", "@types/gulp-concat": "^0.0.37", "@types/inquirer": "^9.0.7", "@types/less": "^3.0.6", "@types/merge2": "^1.4.4", "@types/semver": "^7.5.7", "@types/through2": "^2.0.41"}}