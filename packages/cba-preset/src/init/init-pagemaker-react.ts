import {writeFileSync, readFileSync, existsSync} from 'fs';
import {join} from 'path';
import shell from 'shelljs';
import inquirer from 'inquirer';
import {
  downloadTemplate,
  logger
} from '@baidu/cba-utils';
import {writeTpl2File} from '../utils';
import {addProject} from '.';

import type {IPrompt} from '.';

export enum RouterMode {
  HISTORY = 'history',
  HASH = 'hash'
}

export enum StateName {
  MOBX = 'mobx',
  REDUX = 'redux'
}

function generateStateMana(stateName: string, projectName?: string) {
  switch (stateName) {
    case StateName.MOBX: {
      break;
    }
    case StateName.REDUX: {
      break;
    }
    default: {
      const homeTplPath = join(__dirname, '../../template/home-index.tpl');
      const homeTargetPath = join(process.cwd(), `${projectName ? projectName : ''}/src/pages/home/<USER>
      writeTpl2File({
        tplPath: homeTplPath,
        targetPath: homeTargetPath,
        // 这里需要一些空格提供模板内的缩进
        data: {
          stateImport: `import {useState} from 'react';`,
          stateScript: `const [count, setCount] = useState(0);`,
          stateDemo: `
        <div
          className="touch-me"
          onClick={() => setCount(count + 1)}
        >
          <div className="label">Touch Me</div>
          <div className="count">{count}</div>
        </div>`,
          exportComponent: 'export default PageHome;'
        }
      });

      const appTplPath = join(__dirname, '../../template/App.tpl');
      const appTargetPath = join(process.cwd(), `${projectName ? projectName : ''}/src/App.tsx`);

      writeTpl2File({
        tplPath: appTplPath,
        targetPath: appTargetPath,
        data: {
          useState: false,
          stateWrap: `<Provider>
        <Router {...props} routes={routes} />
    </Provider>`
        }
      });

      projectName && shell.cd(projectName);
      shell.rm('-rf', 'src/store');
      const pkg = JSON.parse(readFileSync('package.json', 'utf-8'));
      delete pkg.dependencies.mobx;
      delete pkg.dependencies['mobx-react'];
      writeFileSync('package.json', JSON.stringify(pkg, null, 2), 'utf-8');
      projectName && shell.cd('..');
      break;
    }
  }
}

function generateUserConfig(prompt?: any, projectName?: string) {
  const {stateName, routerMode, projectType} = prompt || {};
  const tplPath = join(__dirname, '../../template/bce-config.tpl');
  const targetPath = join(process.cwd(), `${projectName ? projectName : ''}/bce-config.js`);
  writeTpl2File({
    tplPath,
    targetPath,
    data: {
      projectType,
      routerMode,
      stateName,
      presets: JSON.stringify(['@baidu/cba-preset-pagemaker-react'])
    }
  });
}

async function initFn(prompt: IPrompt) {
  const {stateName = '', projectName} = prompt;
  logger.info('初始化Pagemaker 本地应用');
  await downloadTemplate(projectName, 'pagemaker-react');
  generateStateMana(stateName, projectName);
  generateUserConfig(prompt, projectName);
}

export default async (api: any) => {
  addProject({
    name: 'Pagemaker 本地应用',
    key: 'pagemaker-react',
    extPrompts: () => {
      return inquirer.prompt([
        {
          type: 'list',
          message: '请选择路由类型:',
          name: 'routerMode',
          default: RouterMode.HASH,
          choices: [
            {
              name: 'Hash',
              value: RouterMode.HASH
            },
            {
              name: 'History',
              value: RouterMode.HISTORY
            }
          ]
        },
        {
          type: 'list',
          message: '请选择状态管理库:',
          name: 'stateName',
          default: '',
          choices: [
            {
              name: '无',
              value: ''
            },
            {
              name: 'Mobx',
              value: StateName.MOBX
            }
          ]
        }
      ]);
    },
    initFn
  });
};
