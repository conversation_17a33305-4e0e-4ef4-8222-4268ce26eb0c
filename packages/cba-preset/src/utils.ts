import http from 'http';
import https from 'https';
import assert from 'assert';
import net from 'net';
import readline from 'readline';
import Mustache from 'mustache';
import {existsSync, statSync, writeFileSync, readFileSync, mkdirpSync} from 'fs-extra';
import shell from 'shelljs';

export const httpHelp = (url: string) => {
  const api = url.startsWith('https') ? https : http;

  return new Promise((resolve, reject) => {
    const req = api.get(url, res => {
      const {statusCode} = res;

      let data = '';
      res.setEncoding('utf8');
      res.on('data', chunk => (data += chunk)); // eslint-disable-line no-return-assign
      res.on('end', () => {
        if (statusCode === 200) {
          resolve(data);
          return;
        }
        reject(data);
      });
    });

    req.on('error', reject);
  });
};

export const findFreePort = (startPort: number, endPort: number) => {
  return new Promise((resolve, reject) => {
    let currentPort = startPort;
    function checkPort() {
      const server = net.createServer();
      server.once('error', (err: any) => {
        if (err.code === 'EADDRINUSE') {
          currentPort++;
          if (currentPort <= endPort) {
            checkPort();
          } else {
            reject(`${startPort} - ${endPort} 内已无可用端口`);
          }
        } else {
          reject(err);
        }
      });

      server.once('listening', () => {
        server.close(() => {
          resolve(currentPort);
        });
      });

      server.listen(currentPort, 'localhost');
    }
    checkPort();
  });
};

export const writeTpl2File = (opts: {targetPath: string; tplPath?: string; tplContent?: string; data?: any}) => {
  const {tplPath, targetPath, data = {}} = opts;
  let {tplContent} = opts;
  assert(
    !targetPath || existsSync(targetPath) || statSync(targetPath).isFile(),
    `opts.targetPath does not exists or is not a file.`
  );
  if (!tplContent) {
    assert(
      !tplPath || existsSync(tplPath) || statSync(tplPath).isFile(),
      `opts.tplPath does not exists or is not a file.`
    );
    tplContent = Mustache.render(readFileSync(tplPath as string, 'utf8'), data);
  }
  writeFileSync(targetPath, tplContent, 'utf-8');
  return '';
};

export const asyncRealLine = (question: string = '') => {
  return new Promise((resolve, reject) => {
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });
    rl.question(question, (answer: string) => {
      rl.close();
      resolve(answer.trim());
    });
  });
};

export function mkFile(path: string) {
  if (!existsSync(path)) {
    const idx = path.lastIndexOf('/');
    if (idx < 0) {
      shell.touch(path);
      return;
    }
    const dir = path.substring(0, idx);
    mkdirpSync(dir);
    shell.touch(path);
  }
}
