{"name": "console-dbsc", "version": "1.0.0", "description": "数据库智能驾驶舱 DBSC 前端项目", "main": "src/index.tsx", "scripts": {"dev": "cba-cli dev", "mock": "cba-cli dev --mock", "build": "cba-cli build", "build:private": "BUILD_TYPE=private cba-cli build", "i18n:extract": "cba-cli i18n:extract", "i18n:upload": "cba-cli i18n:upload"}, "repository": {"type": "git", "url": "ssh://<EMAIL>:8235/baidu/bce-console/console-dbsc"}, "keywords": ["DBSC"], "author": "<EMAIL>", "license": "ISC", "devDependencies": {"@baidu/cba-cli": "workspace:*", "@baidu/cba-preset-console-react": "workspace:*", "@types/lodash": "^4.14.202", "@types/react": "^17.0.75", "@types/react-dom": "^17.0.25", "thread-loader": "^4.0.2"}, "dependencies": {"@baidu/bce-react-toolkit": "^0.0.23-beta.0", "acud": "^1.4.41", "acud-icon": "^1.0.8", "ahooks": "^3.7.10", "axios": "^1.6.7", "classnames": "^2.5.1", "echarts": "^5.5.0", "lodash": "^4.17.21", "moment": "^2.30.1", "react": "^17.0.2", "react-dom": "^17.0.2", "react-i18next": "^14.0.5", "react-router-dom": "^6.22.1"}, "browserslist": ["IE >= 11"]}