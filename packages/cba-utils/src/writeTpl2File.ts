import assert from 'assert';
import Mustache from 'mustache';
import {
  existsSync,
  statSync,
  readFileSync,
  writeFileSync
} from 'fs-extra';

export function writeTpl2File(opts: {targetPath: string; tplPath?: string; tplContent?: string; data?: any}) {
  const {tplPath, targetPath, data = {}} = opts;
  let {tplContent} = opts;
  if (!tplContent) {
    assert(
      !tplPath || existsSync(tplPath) || statSync(tplPath).isFile(),
      `opts.tplPath does not exists or is not a file.`
    );
    tplContent = Mustache.render(readFileSync(tplPath as string, 'utf8'), data);
  }
  writeFileSync(targetPath, tplContent, 'utf-8');
};

