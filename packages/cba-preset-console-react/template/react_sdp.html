<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>百度智能云</title>
  </head>
  <body>
    <div id="root-app-container"></div>
    <script src="//bce.bdstatic.com/lib/@baiducloud/bce-track/@latest/monitorLoader.js"></script>
    <script type="text/javascript" src="https://bce.bdstatic.com/ecom/esl/2.2.0-rc.3/esl.js"></script>
    <script src="//code.bdstatic.com/combo/san@3.7.7/dist/san.spa.min.js&san-router@1.2.2/dist/san-router.js"></script>
    <script
      type="text/javascript"
      src="https://bce.bdstatic.com/<%= htmlWebpackPlugin.options.consoleType %>/fe-framework/loadjs.js"
    ></script>
    <script type="text/javascript">
      const isOnline =
        /^console\.bce\.baidu\.com/.test(window.location.hostname) ||
        /^console\.vcp\.baidu\.com/.test(window.location.hostname);
      const useAlphaRelease = '<%= htmlWebpackPlugin.options.useAlphaRelease %>';
      const i18nAddress = isOnline
        ? 'https://bce.bdstatic.com/console/static/i18n/console.%s'
        : 'https://bce.bdstatic.com/console/static/i18n/console.offline.%s';
      const kModuleName = '<%= htmlWebpackPlugin.options.moduleName %>';
      const kAppName = '<%= htmlWebpackPlugin.options.appName %>';
      const kEnv = '<%= htmlWebpackPlugin.options.env %>';
      const KCustomDeps = '<%= htmlWebpackPlugin.options.sdpDeps %>';
      const kVersionName = isOnline ? '<%= htmlWebpackPlugin.options.onlineConfigFile %>' : 'qasandbox-config';
      const isLocal = /^localhost/.test(location.host);

      const pagemakerAppName = window.location.pathname.replace(/^\//, '').replace(/\//g, '_').toUpperCase();
      sessionStorage.removeItem('PAGEMAKER_RELEASE_STATE_' + pagemakerAppName);
      const baseUrl = 'https://bce.bdstatic.com/console/';
      const kModuleConf = {
        baseUrl,
        paths: {
          'san': 'https://code.bdstatic.com/npm/san@3.7.7/dist/san.spa.min',
          'san-router': 'https://code.bdstatic.com/npm/san-router@1.2.2/dist/san-router',
          'lodash': 'https://code.bdstatic.com/npm/lodash@3.10.1/index',
          'jquery': 'https://code.bdstatic.com/npm/jquery@1.9.1/jquery.min',
          'moment': 'https://code.bdstatic.com/npm/moment@2.24.0/min/moment.min',
          'big.js': 'https://code.bdstatic.com/npm/big.js@5.2.2/big.min',
          'ace-builds': 'https://code.bdstatic.com/npm/ace-builds@1.4.5/src-min-noconflict/',
          'hljs': 'https://bce.bdstatic.com/console/highlight.js/9.12.0',
          'hljs/highlight': 'https://bce.bdstatic.com/console/highlight.js/9.12.0/highlight.min',
          'echarts': 'https://bce.bdstatic.com/console/dist/affaebd/dep/echarts/3.7.0/echarts+zrender.min',
          'echarts/map/js/china': 'https://bce.bdstatic.com/console/dist/affaebd/dep/echarts/3.7.0/map/js/china',
          'cyberplayer': 'https://bce.bdstatic.com/lib/@baiducloud/cyberplayer/cyberplayer',
          'bosuploader': 'https://bce.bdstatic.com/bce-bos-uploader-lite/1.0.5/bce-bos-uploader-lite.min',
          'webuploader': 'https://bce.bdstatic.com/console/dep/05cfee93/webuploader/WebUploader',
          'webuploader/webuploader': 'https://bce.bdstatic.com/console/dep/05cfee93/webuploader/webuploader',
          'cyberlatest': 'https://bce.bdstatic.com/jwplayer/latest/cyberplayer',
          'fe-version': `${baseUrl}static/${kVersionName}`
        },
        shim: {
          jquery: {
            exports: 'jQuery'
          }
        },
        bundles: {
          echarts: [
            'echarts',
            'zrender/vml/vml',
            'echarts/chart/pie',
            'echarts/chart/bar',
            'echarts/chart/line',
            'echarts/chart/lines',
            'echarts/chart/map',
            'echarts/chart/scatter',
            'echarts/chart/gauge',
            'echarts/component/legendScroll',
            'echarts/component/legend',
            'echarts/component/dataZoom',
            'echarts/component/tooltip',
            'echarts/component/title',
            'echarts/component/grid',
            'echarts/component/toolbox',
            'echarts/component/markPoint',
            'echarts/component/markLine'
          ]
        },
        map: {
          '*': {
            '@baiducloud/bce-ui/san': '@baiducloud/bce-ui'
          }
        }
      };
      const jsList = ['static/js/main.js'];
      const cssList = ['main.css'];
      var isQasandbox = /^qasandbox\.bcetest\.baidu\.com/.test(window.location.hostname);
      var JsResScriptNodeList = [];
      window.ISXSCONSOLE = '<%= htmlWebpackPlugin.options.consoleType %>' === 'xs-console';
      require.config(kModuleConf);

      if (window.ISXSCONSOLE) {
        if (document.referrer !== '' && !/^https:\/\/console(-|\.)vcp\.baidu(-int)?\.com/.test(document.referrer)) {
          sessionStorage.setItem('XS_DOCUMENT_REFERRER', document.referrer);
        }
        window.XSREFERRER = sessionStorage.getItem(`XS_DOCUMENT_REFERRER`);
      }
      // 加载器
      function loadCss(url) {
        var cssLink = document.createElement('link');
        cssLink.setAttribute('rel', 'stylesheet');
        cssLink.setAttribute('href', url);
        document.head.appendChild(cssLink);
      }
      function loadJS(url) {
        var scriptNode = document.createElement('script');
        scriptNode.setAttribute('type', 'text/javascript');
        scriptNode.setAttribute('src', url);
        scriptNode.onload = function () {
          this._loadStatus = '1';
        };
        document.head.appendChild(scriptNode);
        return scriptNode;
      }
      // 工具函数
      function getCookie(name) {
        var matches = String(document.cookie).match(new RegExp('(?:^|)' + name + '(?:(?:=([^;]*))|;|$)'));
        if (matches && matches[1]) {
          return matches[1];
        }
        return '';
      }
      function getRegion() {
        // 创建一个XMLHttpRequest对象
        if (XMLHttpRequest && Promise) {
          return new Promise((resolve, reject) => {
            var token = getCookie('bce-user-info') || '';
            var xhr = new XMLHttpRequest();
            xhr.timeout = 10 * 1000;
            xhr.open('POST', '/api/region/get', true);
            xhr.setRequestHeader('Content-Type', 'application/json; charset=UTF-8');
            xhr.setRequestHeader('csrftoken', token.replace(/"/g, ''));
            xhr.setRequestHeader('X-Region', 'bj');
            xhr.send();
            // 监听请求完成的事件
            xhr.onreadystatechange = function () {
              // 请求完成且响应已就绪
              if (xhr.readyState === 4 && xhr.status === 200) {
                var responseData = {};
                try {
                  responseData = JSON.parse(xhr.responseText);
                } finally {
                  if (responseData.success === 'true' || responseData.success === true) {
                    if (responseData.result && responseData.result.regionId) {
                      resolve(responseData.result.regionId);
                    }
                  } else {
                    resolve('');
                  }
                }
              }
            };
            xhr.onerror = () => resolve('');
            xhr.ontimeout = () => resolve('');
          });
        } else {
          return '';
        }
      }

      // 等待资源加载完成
      function waitLoadJS(scriptNodeList, callback) {
        var countOfAction = scriptNodeList.length;
        scriptNodeList.forEach(function (scriptNode) {
          if (scriptNode._loadStatus === '1') {
            if (--countOfAction === 0) {
              callback();
            }
          } else {
            scriptNode.onload = function () {
              if (--countOfAction === 0) {
                callback();
              }
            };
          }
        });
      }

      // 开始加载资源
      function loadRes(version) {
        if (kEnv === 'development') {
          return;
        }
        // 判断是否启用覆盖率打包沙盒测试
        const isCoverage = localStorage.getItem('_USE_ISTANBUL_COV_') === kAppName;
        const coveragePrefix = isQasandbox && isCoverage ? 'coverage/' : '';
        const publicPath = `${baseUrl}static/${kAppName}/${version}/${coveragePrefix}`;
        window.appPublicPath = publicPath;
        cssList.forEach(function (filePath) {
          loadCss(publicPath + filePath);
        });
        JsResScriptNodeList = jsList.map(function (filePath) {
          return loadJS(publicPath + filePath);
        });
      }

      // 配置esl模块加载器
      function configPublicDep(versions, framework) {
        window.$framework = framework;
        const paths = {};
        const map = {};
        const enableAlphaVersion = useAlphaRelease === 'true' && versions[kAppName].alpha_version;
        const defaultKeys = ['version', 'xs_version', 'path', enableAlphaVersion ? 'alpha_version' : undefined].filter(
          Boolean
        );
        let releaseVersion = '';
        let specifyVersion = false;
        let grayVersion = '';
        // 遍历配置文件，获取产品版本、SDP公共依赖版本等信息
        for (const modName in versions) {
          // eslint-disable-line guard-for-in,no-restricted-syntax
          let {path, version} = versions[modName];

          if (path) {
            paths[modName] = path;
            version = version || path.match(/([\d]+\.){3}[\d]+/g);
            version && (map[`${modName}@${version}`] = modName);
          }
          grayVersion = sessionStorage.getItem(`_${kAppName}${window.ISXSCONSOLE ? '_xs' : ''}_version_`);
          let targetVersion = '';
          if (modName === kAppName) {
            const moduleKeys = Object.keys(versions[modName]);
            // 灰度版本最高优先级
            releaseVersion =
              versions[kAppName] &&
              (window.ISXSCONSOLE
                ? versions[kAppName].xs_version
                : enableAlphaVersion
                  ? versions[kAppName].alpha_version
                  : versions[kAppName].version);
            specifyVersion = !grayVersion && !!moduleKeys.some(key => defaultKeys.indexOf(key) === -1);
          }
          if (grayVersion) {
            grayVersion = grayVersion.trim();
            console.warn(
              'The browser is currently in a gray environment, and the gray version number is %c' + grayVersion,
              'color: red; font-weight: bold;'
            );
          }
        }
        let customConfig = JSON.parse(KCustomDeps || '{}');
        if (isLocal) {
          customConfig[kModuleName] = `${location.origin}${location.pathname}static/js/main`;
        }
        require.config({
          paths: {
            ...paths,
            ...customConfig
          },
          // 解决模块中自定义版本与bce-version中配置的版本一致，但两种加载modname同时存在时加载失败的问题
          // 例如bcc中安装a与b模块，但b模块依赖a，则加载bcc时会同时涉及到加载a@version 和 a两个模块，此时后加载的模块esl加载流程中止
          map: {
            [kModuleName]: map
          }
        });
        // 如果为不同region指定了版本，则先获取当前region，再根据region获取版本并加载资源
        if (specifyVersion) {
          const regionDefer = getRegion();
          if (regionDefer && regionDefer.then) {
            return regionDefer
              .then(regionId => {
                const appConfig = versions[kAppName];
                let versionKey = 'version';
                if (window.ISXSCONSOLE) {
                  versionKey = 'xs_version';
                }
                // 尝试获取region指定版本，如果未指定则使用默认版本
                // alpha_version仅用于测试环境，这里先不考虑alpha_version
                releaseVersion =
                  !!regionId && appConfig[regionId] && appConfig[regionId][versionKey]
                    ? appConfig[regionId][versionKey]
                    : enableAlphaVersion
                      ? appConfig['alpha_version']
                      : appConfig[versionKey];
                loadRes(releaseVersion);
                window.addEventListener('after_region_changed', eventData => {
                  if (eventData && eventData.detail && eventData.detail.newRegion) {
                    const data = eventData.detail;
                    const newRegion = data.newRegion || '';
                    const newVersion =
                      !!newRegion && appConfig[newRegion] && appConfig[newRegion].version
                        ? appConfig[newRegion].version
                        : appConfig.version;
                    if (newRegion === regionId || newVersion === releaseVersion) {
                      return;
                    }
                    location.reload();
                  }
                });
              })
              .catch(() => loadRes(releaseVersion));
          } else {
            return loadRes(grayVersion || releaseVersion);
          }
        } else {
          return loadRes(grayVersion || releaseVersion);
        }
      }
      // 启动产品模块
      function appBoot(defer, runtime, framework) {
        if (!JsResScriptNodeList.length) {
          window.require([kModuleName], app => {
            const $context = runtime.ServiceFactory.resolve('$context');
            if ($context) {
              defer.then($context.init);
            }

            return runtime.initialize
              ? defer.then(runtime.initialize).then(app.bootstrap).then(framework.componentActive)
              : defer.then(app.bootstrap).then(framework.componentActive);
          });
        } else {
          waitLoadJS(JsResScriptNodeList, function () {
            window.require([kModuleName], app => {
              const $context = runtime.ServiceFactory.resolve('$context');
              if ($context) {
                defer.then($context.init);
              }

              return runtime.initialize
                ? defer.then(runtime.initialize).then(app.bootstrap).then(framework.componentActive)
                : defer.then(app.bootstrap).then(framework.componentActive);
            });
          });
        }
      }
      // 产品启动依赖项准备
      function preBootstrap(i18n, framework) {
        // 国际化初始化
        i18n.init({
          sourceType: 'amd',
          url: i18nAddress
        });
        const locale = framework.i18n.getCurrentLanguage();

        const frameDefer = framework.boot({isActiveComponent: false});
        const i18nDefer = i18n.activate(locale);
        return i18nDefer.then(() => frameDefer);
      }
      //开始加载流程
      function reactMixSanBootstrap() {
        window.require(['fe-version', 'framework'], function (versions, framework) {
          const configRes = configPublicDep(versions, framework);

          window.require(['@baiducloud/i18n', '@baiducloud/runtime'], (i18n, runtime) => {
            const defer = preBootstrap(i18n, framework);
            if (configRes && configRes.then) {
              configRes.then(res => appBoot(defer, runtime, framework));
            } else {
              appBoot(defer, runtime, framework);
            }
          });
        });
      }
      reactMixSanBootstrap();
    </script>
  </body>
</html>
