import {deleteFolderOrFile, logger} from '@baidu/cba-utils';
import {copySync, readdirSync, renameSync, existsSync, ensureDirSync} from 'fs-extra';
import path from 'path';
import omit from 'lodash/omit';

import renderTemplate from './utils/renderTemplate';

interface BaseGeneratorArgs extends GenerateArgs {
  templatePath: string;
  targetPath: string;
  customTargetPath?: string;
}

export default class BaseGenerator {
  options: BaseGeneratorArgs;

  constructor(args: BaseGeneratorArgs) {
    this.options = args;
  }

  setTemplatePath(path: string) {
    this.options.templatePath = path;
  }

  setTargetPath(path: string) {
    this.options.targetPath = path;
  }

  setCustomTargetPath(path: string) {
    this.options.customTargetPath = path;
  }

  tryEject() {
    const {eject, templatePath = '', customTargetPath = ''} = this.options;
    if (!eject) {
      return;
    }
    ensureDirSync(customTargetPath);
    if (!existsSync(templatePath)) {
      logger.error(`${templatePath} not found`);
      process.exit(1);
    }
    copySync(templatePath, customTargetPath);
  }

  async generateFiles() {
    const {templatePath, targetPath, fileName, fileMode} = this.options;

    const targetFolder = `${targetPath}/${fileName}`;

    await renderTemplate(
      templatePath,
      targetFolder,
      omit(this.options, ['customTargetPath', 'templatePath', 'targetPath'])
    );

    if (fileMode) {
      const files = readdirSync(targetFolder);
      files.forEach(filePath => {
        const file = path.parse(filePath);
        renameSync(`${targetFolder}/${file.base}`, `${targetFolder}${file.ext}`);
      });
      deleteFolderOrFile(targetFolder);
    }
  }

  run() {
    this.tryEject();
    this.generateFiles();
  }
}
