{
  "parser": "@typescript-eslint/parser",
  "env": {
    "node": true,
    "browser": true,
    "commonjs": true,
    "es2021": true
  },
  "extends": [
    "plugin:@typescript-eslint/recommended",
    "plugin:react/recommended",
    "plugin:react-hooks/recommended",
    "eslint:recommended",
    "plugin:prettier/recommended"
  ],
  "plugins": [
    "@typescript-eslint",
    "react",
    "react-hooks",
    "prettier",
    "simple-import-sort"
  ],
  "rules": {
    "object-curly-spacing": ["error", "never"],
    "comma-dangle": ["error", "never"],
    "@typescript-eslint/no-explicit-any": "off",
    "no-unused-vars": "off",
    "@typescript-eslint/no-unused-vars": "error",
    "simple-import-sort/imports": [
      "error",
      {
          "groups": [
              // react放在首行
              ["^react", "^@?\\w"],
              // 内部导入
              ["^(@|components)(/.*|$)"],
              // 父级导入. 把 `..` 放在最后.
              ["^\\.\\.(?!/?$)", "^\\.\\./?$"],
              // 同级导入. 把同一个文件夹.放在最后
              ["^\\./(?=.*/)(?!/?$)", "^\\.(?!/?$)", "^\\./?$"],
              // 样式导入.
              ["^.+\\.?(css)$"],
              // 带有副作用导入，比如import 'a.css'这种.
              ["^\\u0000"]
          ]
      }
    ]
  },
  "settings": {
    "react": {
      "createClass": "createReactClass",
      "pragma": "React",
      "fragment": "Fragment",
      "version": "detect",
      "flowVersion": "0.53"
    }
  }
}
