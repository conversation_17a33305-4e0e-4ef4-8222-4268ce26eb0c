/**
 * @file 获取 webpack 配置
 * 如果需要扩展webpack配置，请在插件中通过chainWebpack或modifyWebpackConfig方法进行扩展
 * <AUTHOR> (<EMAIL>)
 */

import webpack, {Configuration} from 'webpack';
import Config from 'webpack-chain';
import merge from 'webpack-merge';
import {Env} from '../../types';
import {getBaseConfig} from './base';
import {addProgressPlugin} from './progressPlugin';
import {addDefinePlugin} from './definePlugin';
import {addDevServer} from './devServer';

const isInPipeline = process.env.BUILD_ENV === 'pipeline';

export interface IOpts {
  env: Env;
  cwd: string;
  userConfig: Record<string, any>;
  chainWebpack?: Function;
  modifyWebpackConfig?: Function;
}

export async function getConfig(opts: IOpts): Promise<Configuration> {
  const config = new Config();

  const applyOpts = {
    env: opts.env,
    userConfig: opts.userConfig,
    config
  };

  // mode
  config.name('preset:WebpackConfig').mode(opts.env);

  /* --- plugins --- */
  // 进度条
  if (opts.userConfig?.enableWebpackProgressPlugin && !isInPipeline) {
    await addProgressPlugin(applyOpts);
  }

  // 定义变量
  addDefinePlugin(applyOpts);
  /* --- plugins end --- */

  if (opts.env === Env.development) {
    addDevServer(applyOpts);
  }

  // 收集插件对chainWebpack的调用
  if (opts.chainWebpack) {
    await opts.chainWebpack(config, {
      env: opts.env,
      webpack
    });
  }

  let webpackConfig = config.toConfig() as Configuration;
  // 合并基础配置
  webpackConfig = merge(
    getBaseConfig({
      cwd: opts.cwd
    }),
    webpackConfig
  );

  // 收集插件对webpack配置的修改
  if (opts.modifyWebpackConfig) {
    webpackConfig = await opts.modifyWebpackConfig(webpackConfig, {
      env: opts.env,
      webpack,
      merge
    });
  }

  return webpackConfig;
}
