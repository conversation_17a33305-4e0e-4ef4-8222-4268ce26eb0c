import '@/pages/Activation';
import '@/pages/AuditReport';
import '@baidu/bce-react-toolkit/es/styles/_overwrite_acud.css';
import '@/styles/global.less';
import '@/styles/dbsc.less';
import '@/styles/embed.entry.less';

import React, {StrictMode} from 'react';
// eslint-disable-next-line react/no-deprecated
import {render} from 'react-dom';
import {HashRouter} from 'react-router-dom';

import {
  AppProvider,
  FrameworkProvider,
  moduleRegistry
} from '@baidu/bce-react-toolkit';

interface MountOptions {
  /** 挂载节点 */
  selector: string | Node;
  /** framework 数据 */
  frameworkData: any;
  /** 渲染的模块名称 */
  moduleName: string;
  /** 渲染的参数 */
  props: {
    [key: string]: any;
  };
}

const EmbedWrapper: React.FC<Partial<MountOptions>> = (options) => {
  const Component = moduleRegistry.get(options.moduleName!) as any;
  return (
    <div className="embedded-wrapper">
      <Component {...options.props} />
    </div>
  );
};

export function mount(options: MountOptions) {
  const {selector, frameworkData, moduleName} = options;

  const targetNode =
    selector instanceof Node ? selector : document.querySelector(selector);

  if (!targetNode || !moduleName) {
    return;
  }

  render(
    <StrictMode>
      <FrameworkProvider frameworkData={frameworkData}>
        <AppProvider isEmbed>
          <HashRouter>
            <EmbedWrapper
              moduleName={options.moduleName}
              props={options.props}
            ></EmbedWrapper>
          </HashRouter>
        </AppProvider>
      </FrameworkProvider>
    </StrictMode>,
    targetNode as Element
  );
}
