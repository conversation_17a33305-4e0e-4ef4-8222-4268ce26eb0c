/**
 * @file 检查最新版本
 * <AUTHOR> (<EMAIL>)
 */

import {chalk} from '@baidu/cba-utils';
import {exec} from 'child_process';
import {IApi} from './types';

const {name, version} = require('../package.json');

/**
 * 检查版本号
 */
const asyncCheckVersion = () => {
  exec(`npm view ${name} version --registry=http://registry.npm.baidu-int.com`, (err, data) => {
    if (!err && data.trim() !== version) {
      console.log(chalk.gray(`\n\t${'-'.repeat(88)}`));
      console.log(chalk.greenBright(`\n\t\t\t\t\t 发现CLI新版本 ${data.trim()}`));
      console.log(chalk.greenBright(`\n\t\t npm update ${name} -g --registry=http://registry.npm.baidu-int.com`));
      console.log(chalk.gray(`\n\t${'-'.repeat(88)}`));
    }
  });
};

export default (api: IApi) => {
  api.describe({
    enableBy: () => {
      return api.name !== 'init';
    }
  });

  api.onStart(() => {
    asyncCheckVersion();
  });
};
