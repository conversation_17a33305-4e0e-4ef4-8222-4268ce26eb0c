/**
 * 使用本地 amis 作为 sdk 使用
 * 这个插件无法让 本地 和 pagemaker 使用不同版本的 amis, 因为生产环境是使用当前环境的amis： amisRequire('amis') amisRequire('react')
 * 主要用于纯本地应用
 */

import fs from 'fs';
// eslint-disable-next-line import/default
import CopyPlugin from 'copy-webpack-plugin';
import WriteFilePlugin from 'write-file-webpack-plugin';
import {IApi, AddAssetsToHtml} from '@baidu/cba-preset';
import {getLocalAmisVersion} from '@baidu/cba-utils';

const useLocalAmisSdk = (api: any) => {
  const amisSdkPathPrefix = fs.existsSync('node_modules/amis/sdk/sdk.js') ? '' : '../../';
  const version = getLocalAmisVersion();
  return [
    new CopyPlugin({
      patterns: [
        {from: amisSdkPathPrefix + 'node_modules/amis/lib/helper.css', to: `amis@${version}/lib/helper.css`},
        // to do 是否会有选择其他主题的可能
        {
          from: amisSdkPathPrefix + 'node_modules/amis/lib/themes/cxd.css',
          to: `amis@${version}/lib/themes/cxd.css`
        },
        {from: amisSdkPathPrefix + 'node_modules/amis/sdk/sdk.js', to: `amis@${version}/sdk/sdk.js`}
      ]
    }),
    new WriteFilePlugin({
      test: /amis\/.+\.(css|js)$/
    }),
    new AddAssetsToHtml({
      css: [`amis@${version}/lib/helper.css`, `amis@${version}/lib/themes/cxd.css`],
      js: [`amis@${version}/sdk/sdk.js`]
    })
  ];
};

/**
 * 使用远程sdk
 */
export interface AmisSdkConfig {
  cdnHost?: string;
  version?: string;
  theme?: string;
  sdkJsUrl?: string;
  helperCssUrl?: string;
  themeCssUrl?: string;
}

const useRemoteAmisSdk = (api: any) => {
  const sdkConfig: AmisSdkConfig = api.userConfig.remoteAmisSdk;
  let {cdnHost, version, theme, sdkJsUrl, helperCssUrl, themeCssUrl} = Object.assign(
    {
      cdnHost: 'https://unpkg.com',
      version: '3.4.2',
      theme: 'cxd'
    },
    sdkConfig
  );
  const _sdkJsUrl = sdkJsUrl || `${cdnHost}/amis@${version}/sdk/sdk.js`;
  const _helperCssUrl = helperCssUrl || `${cdnHost}/amis@${version}/sdk/helper.css`;
  const _themeCssUrl = themeCssUrl || `${cdnHost}/amis@${version}/lib/themes/${theme}.css`;
  return [
    new AddAssetsToHtml({
      css: [_helperCssUrl, _themeCssUrl],
      js: [_sdkJsUrl]
    })
  ];
};

export default (api: IApi) => {
  api.describe({
    key: 'preset-pagemaker-npm:useAmisSDK',
    enableBy: () => !api?.userConfig?.__noAmisRequire
  });

  api.modifyWebpackConfig((config, {merge}) => {
    return merge(config, {
      plugins: [...(api?.userConfig?.remoteAmisSdk ? useRemoteAmisSdk(api) : useLocalAmisSdk(api))]
    });
  });
};
