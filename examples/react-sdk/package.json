{"name": "@baidu/bce-react-toolkit", "version": "0.0.13-beta.6", "description": "百度智能云控制台 React 运行时依赖", "main": "es/index.js", "scripts": {"build": "npm run restore && npm run compile", "compile": "cba-cli compile --type=es", "dev": "cba-cli dev", "restore": "cba-cli restore", "release": "npm run build && cba-cli release"}, "keywords": ["bce", "react", "toolkit"], "files": ["dist", "lib", "es"], "module": "es/index.js", "author": "<EMAIL>", "license": "ISC", "devDependencies": {"@babel/core": "^7.23.9", "@babel/preset-env": "^7.23.9", "@babel/preset-react": "^7.23.3", "@baidu/cba-cli": "workspace:*", "@baidu/cba-preset-sdk": "workspace:*", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.14.202", "@types/react": "^18.2.48", "@types/react-copy-to-clipboard": "^5.0.7", "@typescript-eslint/eslint-plugin": "^6.20.0", "@typescript-eslint/parser": "^6.20.0", "autoprefixer": "^10.4.17", "babel-plugin-import": "^1.13.8", "enquirer": "^2.4.1", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-simple-import-sort": "^10.0.0", "gulp": "^4.0.2", "gulp-babel": "^8.0.0", "gulp-concat": "^2.6.1", "gulp-strip-code": "^0.1.4", "gulp-typescript": "^6.0.0-alpha.1", "less": "^4.2.0", "less-plugin-npm-import": "^2.1.0", "merge2": "^1.4.1", "postcss": "^8.4.33", "postcss-preset-env": "^9.3.0", "prettier": "^3.2.4", "rimraf": "^5.0.5", "semver": "^7.5.4", "through2": "^4.0.2", "typescript": "^5.3.3", "yargs": "^17.7.2"}, "dependencies": {"acud": "^1.4.41", "axios": "^1.6.7", "classnames": "^2.5.1", "i18next": "^23.8.1", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "react": "^17.0.2", "react-copy-to-clipboard": "^5.1.0", "react-dom": "^17.0.2", "react-i18next": "^14.0.1", "react-router-dom": "^6.21.3", "@baidu/cba-preset-sdk": "workspace:*"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}, "typings": "es/index.d.ts"}