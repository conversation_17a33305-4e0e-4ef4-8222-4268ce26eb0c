# cba-preset 脚手架基础插件集

## webpack公共配置

```javascript
module.exports = {
  entry: './src/index',
  mode: 'development', // or production
  output: {
    path: path.resolve(opts.cwd, './dist'),
    filename: '[name].[chunkhash:8].js'
  },
  resolve: {
    // 按顺序解析后缀名
    extensions: ['.tsx', '.ts', '.jsx', '.js', '.json'],
    // 解析别名
    alias: {
      '@': path.resolve(opts.cwd, './src')
    }
  },
  devServer: {
    port: '8899',
    host: 'localhost',
    hot: true,
    open: true,
    historyApiFallback: true
  },
  module: {
    rules: []
  },
  plugins: [
    new CleanWebpackPlugin(),
    new webpack.DefinePlugin({
      'APP_NAME': bceConfig.appName,
      'process.env': {} // 环境变量
    }),
    new webpack.ProgressPlugin({
      // handler // 自定义进度样式
      activeModules: false,
      entries: true,
      modules: true,
      modulesCount: 5000,
      profile: false,
      dependencies: true,
      dependenciesCount: 10000,
      percentBy: null
    })
  ],
  optimization: {
    minimizer: [terserPlugin]
  }
};
```
