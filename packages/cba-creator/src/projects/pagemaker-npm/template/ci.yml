Global:
    version: "2.0"
    group_email: <EMAIL>
Default:
    profile:
        - buildProduction
Profiles:
    - profile:
      name: buildProduction
      mode: AGENT
      environment:
        image: DECK_CENTOS6U3_K3
        tools:
            - nodejs: 16.16.0
      build:
        command: sh scripts/build.sh
      artifacts:
        release: true
    - profile:
      name: exportDemo
      mode: AGENT
      environment:
        image: DECK_CENTOS6U3_K3
        tools:
            - nodejs: 16.16.0
      build:
        command: sh scripts/export-demo.sh
      artifacts:
        release: true
