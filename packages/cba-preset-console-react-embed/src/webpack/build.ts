import path from 'path';
import {getFlags} from '@baidu/cba-utils';
import {IApi} from '@baidu/cba-preset';
import CssMinimizerPlugin from 'css-minimizer-webpack-plugin';

export default (api: IApi) => {
  api.describe({
    key: 'preset-console-react-embed:webpack-build',
    enableBy: () => api.env === 'production'
  });

  api.modifyWebpackConfig((webpackConfig: any, {merge}: any) => {
    const userConfig = api.userConfig;
    const moduleName = `${userConfig.appName}AppModule`;
    const output = {
      filename: 'static/js/module.js',
      libraryTarget: 'amd',
      library: moduleName,
      clean: true
    };

    webpackConfig = merge(webpackConfig, {
      mode: 'production',
      output,
      plugins: [new CssMinimizerPlugin()]
    });
    return webpackConfig;
  });

  api.onBeforeBundlerStart(async webpackConfig => {
    const flagPath = path.join(api.cwd, './src/flags.ts');
    await getFlags(
      {
        templateId: api.userConfig.templateId,
        flags: api.userConfig.flags
      },
      flagPath
    );
  });
};
