/**
 * @file webpack公共配置
 * <AUTHOR> (<EMAIL>)
 *
 */
import {IApi} from '@baidu/cba-preset';

const getPostCssLoader = () => {
  return {
    loader: 'postcss-loader',
    options: {
      postcssOptions: {
        plugins: ['postcss-preset-env']
      }
    }
  };
};

export default (api: IApi) => {
  api.describe({
    key: 'preset-pagemaker-npm:webpack-comman'
  });

  api.modifyUserConfig(config => {
    // to do pagemaker 获取pagemaker 平台使用的 amis 版本号
    config.remoteAmisSdk = config.remoteAmisSdk ?? {
      cdnHost: 'https://unpkg.com',
      version: '3.4.2',
      theme: 'cxd'
    };
    // 内部调试用的配置，不对外公开
    config.__noAmisRequire = config.__noAmisRequire || false;
    return config;
  });

  api.modifyWebpackConfig((webpackConfig, {merge}) => {
    return merge(webpackConfig, {
      module: {
        rules: [
          // 若使用了远程 amis sdk，忽略项目中 import 的 amis/*.css;
          ...(!api?.userConfig?.remoteAmisSdk || api?.userConfig?.__noAmisRequire
            ? []
            : [
                {
                  test: /amis\/.+\.css$/,
                  use: 'null-loader'
                }
              ]),
          {
            test: /(\.tsx?|js|ts|\.jsx?)$/,
            use: {
              loader: 'babel-loader'
            },
            exclude: /node_modules/
          },
          {
            test: /\.(png|jpe?g|svg|gif)$/,
            type: 'asset/resource',
            generator: {
              filename: 'assets/images/[name].[hash][ext]'
            }
          },
          {
            test: /\.(eot|ttf|woff|woff2)$/,
            type: 'asset/resource',
            generator: {
              filename: 'assets/fonts/[name].[hash][ext]'
            }
          },

          {
            oneOf: [
              {
                test: /\.module\.(css|less)$/,
                use: [
                  'style-loader',
                  {
                    loader: 'css-loader',
                    options: {
                      modules: true,
                      localIdentName: '[name]_[local]_[hash:base64]'
                    }
                  },
                  'less-loader'
                ]
              },
              {
                test: /\.css$/,
                use: ['style-loader', 'css-loader']
              },
              {
                test: /\.less$/,
                use: ['style-loader', 'css-loader', 'less-loader']
              }
            ]
          }
        ]
      },
      infrastructureLogging: {
        level: 'warn'
      },
      stats: 'errors-warnings',
      watchOptions: {
        ignored: '**/node_modules'
      }
    });
  });

  api.registerPlugins([
    require.resolve('./definedPlugin'), // 定义 运行时 app 变量
    require.resolve('./externals'), // 使用 amisRequire 加载 amis、react
    require.resolve('./useAmisSDK'), // 使用 amis sdk
    require.resolve('./virtualFramework') // 虚拟框架，对开发者不可见
  ]);
};
