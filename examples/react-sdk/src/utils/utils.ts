import React, {ComponentType, FunctionComponent} from 'react';

export interface MenuItem {
  /** 菜单名称 */
  menuName: string;
  /** 菜单 key */
  key: string;
  /** 路由到该页面时高亮显示的菜单的 key */
  activeMenuKey?: string;
  /** 是否默认展开 */
  isDefaultOpened?: boolean;
  /** 是否为导航菜单 */
  isNavMenu?: boolean;
  /** 是否为顶部导航栏类型  */
  isHeaderNav?: boolean;
  /** 子菜单列表 */
  children?: MenuItem[];
  /** 子节点菜单是否包含顶部导航类菜单 */
  hasHeaderMenu?: boolean;
  /** 顶部组件 */
  Header?: React.FC<any>;
  /** 对应的页面组件 */
  Component?: ComponentType<any>;
  /** 子标题 */
  renderSubtitle?: FunctionComponent<any>;
  /** 是否使用自定义的页面框架，满足右侧页面区域存在特殊场景需求 */
  isPageLayoutCustomized?: boolean;
  /** 是否使用页面容器 */
  isPageWrapperNotRequired?: boolean;
}

/** 获取 hash 路径 */
export const getHashPathWithoutParams = (hash: string = location.hash) => {
  // 判断是否包含参数
  if (hash.includes('?')) {
    // 获取不包含参数的路径
    const hashPath = hash.split('?')[0];
    // 去除开头的 '#'
    return hashPath.substring(1);
  }

  // 去除开头的 '#'
  return hash.substring(1);
};

/** 递归处理菜单 */
export function recursiveMenus(menus: MenuItem[]): MenuItem[] {
  return menus.reduce((result: MenuItem[], item: MenuItem) => {
    const {children, ...rest} = item;
    result.push({...rest});

    if (children && children.length) {
      const processedChildren = recursiveMenus(children);
      result.push(...processedChildren);
    }

    return result;
  }, []);
}
