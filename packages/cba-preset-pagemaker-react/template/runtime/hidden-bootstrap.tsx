import React from 'react';
import ReactDom from 'react-dom';
import {setRequest} from './hidden-request';
import HotApp from './hot-app';
import GoPageMaker from './go-pagemaker';

let root: any = null;
function mountExternalPage(options: any = {}) {
  const {renderNode, pagePerms, routerPrefix = '', apicenterPrefix, app, page, user, fetcher} = options;
  root = renderNode;
  setRequest(fetcher);
  ReactDom.render(
    <HotApp routerPrefix={routerPrefix} pagePerms={pagePerms} apicenterPrefix={apicenterPrefix} app={app} />,
    renderNode || document.getElementById('main')
  );
}
function unMountExternalPage(options: any = {}) {
  ReactDom.unmountComponentAtNode(root || document.getElementById('main'));
}

const registerSource = (window as any).amis || (window as any).pageMaker;
if (!registerSource || !registerSource.registerExternalApp) {
  if (process.env.BCE_APP_FORCE_LOCAL === 'true') {
    mountExternalPage();
  } else {
    ReactDom.render(<GoPageMaker />, document.getElementById('main'));
  }
} else {
  const {registerExternalApp} = registerSource;
  registerExternalApp({
    mountExternalPage,
    unMountExternalPage
  });
}
