import {
  AppContextActionType,
  useAppContext,
  useFrameworkContext
} from '@baidu/bce-react-toolkit';
import {Button, Checkbox, Tooltip} from 'acud';
import {useBoolean} from 'ahooks';
import React, {useCallback, useState} from 'react';

import {activateIamStsRole} from '@/apis/auth';

import styles from './banner.module.less';

const Banner: React.FC = () => {
  const [agreeTipVisible, setAgreeTipVisible] = useState(false);
  const [protocolChecked, setProtocolChecked] = useState(false);

  const {appState, appDispatch} = useAppContext();
  const {userId} = useFrameworkContext();

  const [
    isActivateBtnLocked,
    {setTrue: lockActivateBtn, setFalse: unlockActivateBtn}
  ] = useBoolean(false);

  const onProtocolChange = useCallback(
    (e: any) => {
      setProtocolChecked(e.target.checked);

      if (e.target.checked && agreeTipVisible) {
        setAgreeTipVisible(false);
      }
    },
    [agreeTipVisible]
  );

  const onClickActivateBtn = useCallback(async () => {
    if (!protocolChecked) {
      setAgreeTipVisible(true);
    }

    if (isActivateBtnLocked) {
      return;
    }

    lockActivateBtn();

    const promises = appState.serviceParams
      .filter((item) => !item.isActivated)
      .map((item) => {
        return activateIamStsRole({
          roleName: item.roleName,
          accountId: userId!,
          policyId: item.policyId,
          serviceId: item.serviceId
        });
      });

    const result = await Promise.all(promises);

    if (result.every((item) => item.success)) {
      appDispatch({
        type: AppContextActionType.ACTIVATE_PRODUCT
      });
    }

    unlockActivateBtn();
  }, [
    appDispatch,
    appState.serviceParams,
    isActivateBtnLocked,
    lockActivateBtn,
    protocolChecked,
    unlockActivateBtn,
    userId
  ]);

  return (
    <div className={styles.container}>
      <div className={styles.contentContainer}>
        <div className={styles.title}>
          数据库智能驾驶舱 DBSC —— 集开发、运维、审计一体的一站式数据库自治服务
        </div>
        <div className={styles.desc}>
          数据库智能驾驶舱 （Database
          SmartCockpit，DBSC）是一款为用户提供安全审计、智能诊断与数据库管理的数据库自治服务。DBSC利用
          Al
          大模型能力和专家经验实现数据库的智能化洞察、评估和优化。有效保证数据库服务的安全、稳定及高效。
        </div>

        <div className={styles.operateContainer}>
          <div className={styles.protocolContainer}>
            <Tooltip
              title={
                <span className={styles.agreeTip}>
                  请先阅读并同意服务协议信息
                </span>
              }
              visible={agreeTipVisible}
            >
              <Checkbox
                onChange={onProtocolChange}
                value={protocolChecked}
              ></Checkbox>
            </Tooltip>
            <span className={styles.agreeText}>同意使用</span>
            <a
              href="https://console.bce.baidu.com/iam/agreement-v2.html"
              target="_blank"
              rel="noreferrer"
            >
              《百度智能云线上订购协议》
            </a>
          </div>
          <Button
            type="primary"
            className={styles.activateBtn}
            onClick={onClickActivateBtn}
          >
            立即开通
          </Button>
        </div>

        <div className={styles.banner}></div>
      </div>
    </div>
  );
};

export default Banner;
