/**
 * 给数字增加千位分割符
 */
export function addThousandSeparator(num: number | string) {
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
}

/** 生成随机报表的名称 */
export function genRandomReportName() {
  let result = '';
  const characters =
    'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  const charactersLength = characters.length;
  for (let i = 0; i < 6; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
  }
  return `ar-${result}`;
}

/** 根据文件路径获取文件名称 */
export function getFileNameFromPath(filePath: string) {
  const pathArray = filePath.split('/');
  const fileName = pathArray[pathArray.length - 1];
  return fileName;
}
