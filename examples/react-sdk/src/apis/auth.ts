import {request} from '../utils';

/** 判断产品角色是否已经激活 */
export function queryIamStsRole(params: {roleName: string}): Promise<{
  success: boolean;
  status: number;
  result: {
    id: string;
    name: string;
    type: string;
    grantType: string;
    description: string;
    domain_id: string;
    create_time: string;
  };
}> {
  return request({
    url: '/api/iam/sts/role/query',
    method: 'POST',
    data: params
  });
}
