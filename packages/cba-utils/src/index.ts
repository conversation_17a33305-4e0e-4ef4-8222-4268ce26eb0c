import chalk from 'chalk';
import logger from './logger';
import type {ILogger} from './logger';
import {getLocalAmisVersion} from './tools/amis';
import getFlags from './flags';
import mockHelper from './mock';

export * from './utils';
export * from './types';

export {updatePackageJSON, updatePackageCliVersion} from './updatePackageJSON';

export {downloadTemplate} from './template';
export * from './writeTpl2File';
export * from './fsTool';

export {logger, chalk, ILogger, getLocalAmisVersion, getFlags, mockHelper};
