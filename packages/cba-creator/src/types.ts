import type {<PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>, IUserConfig} from '@baidu/cba-core';
import {Question} from 'inquirer';

export interface IApi extends ICoreApi {
  addProject: (fn: () => IProject) => void;
  addPrompts: (fn: () => Question[]) => void;
  addFileFromTemplate: (fn: (answers: IPromptAnswers) => void) => void;
  modifyFile: (fn: (answers: IPromptAnswers) => void) => void;

  renderTemplate: (source: string, target: string, appData: Record<string, any>) => void;
  removeTemplate: (target: string) => void;
  modifyConfig: (fn: (userConfig: IUserConfig) => IUserConfig) => IUserConfig;
  modifyPkgJson: (fn: (pkg: Record<string, any>) => Record<string, any>) => Record<string, any>;

  onGenerateFilesEnd: (fn: (generateArgs: Record<string, any>) => void) => void;
}

export interface IProject {
  name: string;
  value: ProjectType;
  children?: IProject[];
}

export interface IPromptAnswers {
  [key: string]: any;
}

export enum ProjectType {
  pagemaker = 'pagemaker',
  pagemakerNpm = 'pagemaker-npm',
  pagemakerReact = 'pagemaker-react',
  consoleReact = 'console-react',
  reactProject = 'react-project',
  consoleReactEmbed = 'console-react-embed'
}

export enum BuildTool {
  WEBPACK = 'webpack',
  VITE = 'vite'
}

export enum RouterMode {
  HISTORY = 'history',
  HASH = 'hash'
}

export enum StateName {
  MOBX = 'mobx',
  REDUX = 'redux'
}
