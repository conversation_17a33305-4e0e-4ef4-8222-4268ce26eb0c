import {transformSync as babelTransform} from '@babel/core';
import walk from 'fs-walk';
import path from 'path';
import fs from 'fs';

export default (targetPath: string, outputPath: string) => {
  if (!targetPath || !fs.existsSync(targetPath)) {
    return;
  }
  walk.walkSync(targetPath, (basedir: string, filename: string, stat: any) => {
    const abSolutePath = path.resolve(basedir, filename);
    if (stat.isDirectory()) {
      return;
    }
    let ext = path.extname(abSolutePath || '');
    if (ext) {
      ext = ext.slice(1);
    }
    if (ext !== 'js' && ext !== 'ts') {
      return;
    }
    const fileData = fs.readFileSync(abSolutePath, {encoding: 'utf8'});
    babelTransform(fileData, {
      presets: ['@babel/preset-env', '@babel/preset-typescript'],
      plugins: [
        '@babel/plugin-syntax-dynamic-import',
        '@babel/plugin-proposal-optional-chaining',
        ['@babel/plugin-proposal-decorators', {legacy: true}],
        ['@babel/plugin-proposal-class-properties', {loose: true}],
        [
          // eslint-disable-next-line
          require('@baiducloud/i18n/transform-plugin'),
          {library: '@baiducloud/i18n', output: outputPath ? outputPath : './public/locales'}
        ]
      ],
      generatorOpts: {
        jsescOption: {
          minimal: true
        }
      },
      filename: abSolutePath,
      sourceType: 'module'
    });
  });
};
