export default class InfoCardPlugin {
  // 关联渲染器名字
  rendererName = 'info-card';
  $schema = '/schemas/UnkownSchema.json';

  // 组件面板相关
  // 组件名称（组件面板显示的Title）
  name = '信息卡片';
  // 组件描述（组件面板显示的描述内容）
  description = '信息展示卡片';
  // 组件预览schema （组件面板预览用到的schema）
  previewSchema = {
    type: 'info-card',
    label: 'info-card',
    body: '自定义容器区'
  };
  // 组件所属分类
  tags = ['自定义'];

  // 组件图标（组件面板显示的图标）
  icon = 'fa fa-file-code-o';
  // 排序
  order = 99;

  // 默认 schema，点击组件面板的组件时，添加到页面里的初始 schema
  scaffold = {
    type: 'info-card',
    label: 'info-card',
    name: 'info-card',
    body: [
      {
        type: 'tpl',
        tpl: '自定义容器区',
        inline: false
      }
    ]
  };


  // 容器类组件必需字段
  regions = [
    {
      key: 'body',
      label: '内容区'
    }
  ];

  panelTitle = '配置';

  panelBodyCreator = () => {
    return [
      {
        type: 'textarea',
        name: 'title',
        label: '卡片title',
        value:
          'amis 是一个低代码前端框架，它使用 JSON 配置来生成页面，可以减少页面开发工作量，极大提升效率。'
      },
      {
        type: 'text',
        name: 'backgroundImage',
        label: '展示图片',
        value:
          'https://search-operate.cdn.bcebos.com/64c279f23794a831f9a8e7a4e0b722dd.jpg'
      },
      {
        type: 'input-number',
        name: 'img_count',
        label: '图片数量',
        value: 3
      },
      {
        type: 'input-number',
        name: 'comment_count',
        label: '评论数',
        value: 2021
      }
    ];
  };
}
