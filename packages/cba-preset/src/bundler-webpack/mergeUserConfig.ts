/**
 * @file 合并用户的 webpack 配置
 * <AUTHOR> (<EMAIL>)
 */
import {Configuration as WebpackConfig} from 'webpack';
import {logger} from '@baidu/cba-utils';
import merge from 'webpack-merge';

export function mergeUserConfig(config: WebpackConfig = {}, userConfig: any) {
  // 将userConfig.proxy配置添加到webpack中
  if (userConfig.proxy) {
    config = merge(config, {devServer: {proxy: userConfig.proxy}});
  }

  const userWebpackConfig:
    | WebpackConfig
    | ((systemConfig: WebpackConfig, mergeFn: (webpackConfig: WebpackConfig) => WebpackConfig) => WebpackConfig) =
    userConfig?.webpack || {};
  const finalConfig =
    typeof userWebpackConfig === 'function' ? userWebpackConfig(config, merge) : merge(config, userWebpackConfig);
  if (!finalConfig) {
    if (typeof userWebpackConfig === 'function') {
      logger.error('webpack配置函数必须返回 WebpackConfig');
    } else {
      logger.error('webpack配置不符合要求，请修改webpack配置');
    }
    process.exit(1);
  }

  return finalConfig;
}
