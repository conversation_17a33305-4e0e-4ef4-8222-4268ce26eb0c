# 百度智能云React CLI工具 开发文档

[icode仓库地址](https://console.cloud.baidu-int.com/devops/icode/repos/baidu/baiducloud/create-bce-app/tree/master)

## 1. 本地研发

### 1.1 环境准备

- 安装pnpm，执行`npm install -g pnpm`
- 确保已经安装VSCode插件：
  - EditorConfig for VS Code
  - ESLint
  - Prettier ESLint
  - Prettier Code formatter

### 1.2 安装依赖

```shell
pnpm install
```

### 1.3 本地服务启动

```shell
pnpm run dev
```

启动后，会对packages中的所有工具包进行监听，当有文件改动后会进行重新打包构建，**如果存在依赖包引入类型错误，可以重启VSCode解决**

### 1.4 研发测试

examples文件夹中包含了基本的项目模板，可以用来调制脚手架命令，如：

```shell
cd examples/console-react

pnpm run dev
```

也可以直接在终端中通过`pnpm cba-cli xxx` 来调试脚手架的命令，如：

```shell
pnpm cba-cli --version
```

**【说明】** 由于在根目录中已经安装了@baidu/cba-cli，因此可以在项目中直接使用，建议调试在examples文件目录中进行

### 1.5 发布版本

##### 修改版本号

```shell
pnpm run version [--pkg-type] <pkg-type>
```

##### 执行发布版本命令

```shell
pnpm run release [--pkg-type] <pkg-type>
```

### 1.5.1 发布预设包

```shell
pnpm run version 
pnpm run release
```
### 1.5.2 发布项目创建包

```shell
pnpm run version:creator
pnpm run release:creator
```

release命令中包含的步骤：

1. 检查本地是否有未提交的代码，需保证代码已经commit
2. 清除各个包中的产物，并通过build命令重新构建
3. 非alpha版本需要填写版本信息，会自动同步到changelog文件中
4. 发布各个包的版本，正式包会发布latest，预发包会发布next版本（预发包包含：alpha、beta、rc版本）

## 2. 其他功能

### 2.1 scripts命令说明

- **build** 构建各个包
- **dev** 启动本地服务，监听各个包的变化，重新构建
- **release** 发布版本
    - 通过--pkg-type可设置需要发包的集合，值对应config.json下的键值。
- **version** 修改版本号
    - 通过--pkg-type可设置需要发包的集合，值对应config.json下的键值。
- **clean:lib** 清除各个包中的产物
- **clean:node_modules** 清除各个包中的node_modules

### 2.2 config.json配置说明
  ```javascript
      "creator": { // creator对应 --pkg-type的值
        "packages": ["cba-creator"], // 该类型下的所有包
        "changelogPath": "changelog-creator.md" // 发版变更日志，路径相对于根目录
      }
  ```

## 3. 相关概念

### 3.1 插件（plugin）

在微内核架构中插件扮演者重要的角色，扩展了CLI的能力，当前项目中每个插件都是一个回调函数，用于实现某种功能

**插件示例：**

```javascript
import {IApi} from '@baidu/cba-preset';
import {chalk} from '@baidu/cba-utils';

export default (api: IApi) => {
  api.registerCommand({
    name: 'version',
    alias: 'v',
    description: 'show create-react-app version',
    fn() {
      const version = require('../package.json').version;
      console.log(chalk.greenBright(`BaiduCloud FE CLI For React v${version}`));
      return version;
    }
  });
}
```

回调函数的参数api中包含了常用的方法和属性：
**IApi 属性：**
| 属性 | 类型 | 说明 |
| ------------ | ------- | ------------------- |
| cwd | String | 当前路径 |
| name | String | 命令名称 |
| args | Object | 命令行参数 |
| pkg | Object | 项目package.json包信息 |
| pkgPath | String | 项目package.json包路径 |
| env | String | 当前环境，枚举值： 'development' \| 'production' |
| stage | String | 命令行执行阶段 |
| userConfig | Object | 用户自定义配置，默认读取位置bce-config.js |
| rawEnv | Object | 环境变量 |

**IApi 方法：**
| 方法 | 说明 |
| -------------------- | ------------------- |
| describe | 插件描述 |
| register | 注册钩子函数，可通过applyPlugins统一调用 |
| registerCommand | 注册命令 |
| registerMethod | 注册插件公共方法，可以被其他插件调用 |
| registerPresets | 注册预设，只能在初始化预设阶段被调用 |
| registerPlugins | 注册插件，只能在初始化插件阶段被调用 |
| applyPlugins | 执行插件通过register() 注册的 hooks |
| modifyUserConfig | 修改用户配置 |
| chainWebpack | 修改webpack信息，使用方式参照[webpack-chain](https://github.com/Yatoo2018/webpack-chain/tree/zh-cmn-Hans) |
| modifyWebpackConfig | 修改webpack信息 |
| onBeforeServerStart | dev启动前的事件回调 |
| onServerStarted | dev启动后的事件回调 |
| onBeforeBundlerStart | 打包开始前的事件回调 |
| onBundlerFinished | 打包完成的事件回调 |
| getFlags | 更新项目功能清单 |

### 3.2 插件集（preset，也称为预设）

- 预设是一个插件集合，用于实现某类功能，类似于babel中的预设。例如实现React项目相关的初始化、启动、打包等插件构成了一整个React预设
- 预设实际上也是一种插件，可以在回调函数中return一个对象，用于加载其他的预设或插件

**预设示例：**

```javascript
import {IApi} from '@baidu/cba-preset';

export default (api: IApi) => {
  return {
    presets: [
      // 加载其他预设
      require.resolve('./otherPreset')
    ],
    plugins: [
      // 加载其他插件
      require.resolve('./otherPlugin')
    ]
  }
}
```

### 3.3 命令执行生命周期

- uninitialized: 未初始化
- init: 初始化
- initPresets: 注册预设
- initPlugins: 注册插件
- onStart: 运行命令前的事件钩子
- collectData: 收集数据
- runCommand: 运行命令
