# cba-preset-console-react 控制台React应用插件集

## webpack配置

### common配置

配置中使用的userConfig 为 bce-config.js中的配置

```js
module.exports = {
  entry: './src/index.tsx',
  resolve: {
    // 按顺序解析后缀名
    extensions: ['.tsx', '.ts', '.jsx', '.js', '.json'],
    // 解析别名
    alias: {
      '@': path.resolve(opts.cwd, './src')
    }
  },
  module: {
    rules: [
      {
        oneOf: [
          {
            test: /\.css$/,
            use: [
              isEmbed ? 'style-loader' : MiniCssExtractPlugin.loader,
              'css-loader',
              {
                loader: 'postcss-loader',
                options: {
                  postcssOptions: {
                    plugins: ['postcss-preset-env']
                  }
                }
              }
            ]
          },
          {
            test: /\.module\.less$/,
            use: [
              isEmbed ? 'style-loader' : MiniCssExtractPlugin.loader,
              {
                loader: 'css-loader',
                options: {
                  modules: {
                    localIdentName: '[local]_[hash:base64:5]'
                  }
                }
              },
              {
                loader: 'postcss-loader',
                options: {
                  postcssOptions: {
                    plugins: ['postcss-preset-env']
                  }
                }
              },
              'less-loader'
            ]
          },
          {
            test: /\.less$/,
            use: [
              isEmbed ? 'style-loader' : MiniCssExtractPlugin.loader,
              'css-loader',
              {
                loader: 'postcss-loader',
                options: {
                  postcssOptions: {
                    plugins: ['postcss-preset-env']
                  }
                }
              },
              'less-loader'
            ]
          },
          {
            test: /\.(png|jpe?g|gif)$/,
            type: 'asset',
            parser: {
              dataUrlCondition: {
                maxSize: 4 * 1024 // 4kb
              }
            }
          },
          {
            test: /\.svg$/,
            use: ['@svgr/webpack']
          },
          {
            test: /\.(jsx|js)$/,
            use: [
              {
                loader: 'babel-loader',
                options: getBabelConfig(userConfig)
              }
            ],
            exclude: /node_modules/
          },
          {
            test: /\.tsx?$/,
            use: [
              {
                loader: 'babel-loader',
                options: getBabelConfig(userConfig)
              }
            ],
            exclude: /node_modules/
          }
        ]
      }
    ]
  },
  plugins: [
    new MiniCssExtractPlugin({
      filename: 'static/css/main.css',
      ignoreOrder: true
    }),
    new webpack.DefinePlugin({
      APP_TITLE: userConfig.appTitle,
      APP_ENABLE_I18N: userConfig?.i18n?.enabled,
      APP_ENABLE_INDEPENDENT_I18N: userConfig?.i18n?.independent,
      APP_ALLOWED_LANGUAGE_TYPES: userConfig?.i18n?.supportedLanguages
    }),
    new CleanWebpackPlugin(),
    new webpack.DefinePlugin({
      'APP_NAME': userConfig.appName,
      'process.env': {} // 环境变量
    }),
    new webpack.ProgressPlugin({
      // handler // 自定义进度样式
      activeModules: false,
      entries: true,
      modules: true,
      modulesCount: 5000,
      profile: false,
      dependencies: true,
      dependenciesCount: 10000,
      percentBy: null
    })
  ],
  optimization: {
    minimizer: [terserPlugin]
  }
};

function getBabelConfig(userConfig) {
  const isProduction = process.env.NODE_ENV === 'production';
  const {babelOptions: {presets = [], plugins = []} = {}} = userConfig;
  // npm link查找模块安装包使用
  module.paths.unshift(path.join(process.cwd(), 'node_modules'));
  return {
    cacheDirectory: !isProduction,
    presets: [
      [
        kPresetEnv,
        {targets: '> 0.5%, last 2 versions, Firefox ESR, not dead'} // https://browserl.ist/
      ],
      kTSPreset,
      kTSPresetReact,
      ...presets
    ],
    plugins: [
      ['import', {libraryName: 'acud', style: true, libraryDirectory: 'es'}, 'acud'],
      !isProduction && 'react-refresh/babel',
      [KTransform, {help: true}],
      kDynamicImportPlugin,
      [kProposalDecoratorPlugin, {legacy: true}],
      [kClassPropertiesPlugin, {loose: true}],
      ...plugins
    ].filter(Boolean)
  };
}
```

### dev环境

```js
module.exports = {
  mode: 'development',
  output: {
    filename: 'static/js/[name].js',
    chunkFilename: 'static/js/[name].chunk.js',
    assetModuleFilename: 'static/js/[hash:10][ext][query]',
    publicPath: pathname,
    libraryTarget: 'amd',
    library: `${userConfig.appName}AppModule`
  },
  devtool: 'eval-source-map',
  devServer: {
    port: '8899',
    host: 'localhost',
    allowedHosts: 'all',
    open: true,
    historyApiFallback: true,
    hot: true,
    compress: true,
    https: true,
    client: {
      progress: false,
      reconnect: 5
    },
    proxy: {
      '/api': {
        target: 'http://qasandbox.bcetest.baidu.com:80/', // 默认代理地址
        changeOrigin: true,
        secure: false,
        onProxyReq(proxyReq) {
          // 某些接口会验证origin
          proxyReq.setHeader('origin', 'https://qasandbox.bcetest.baidu.com');
        }
      }
  },
  plugins: [
    new ReactRefreshWebpackPlugin(),
    new HtmlWebpackPlugin({
      moduleName: moduleName, // 模块名
      template: templatePath, // 内置html模板路径
      env: 'development'
    })
  ]
};
```

### build环境

```js
module.exports = {
  mode: 'production',
  performance: false,
  output: {
    filename: 'static/js/[name].js',
    chunkFilename: 'static/js/[name].chunk.js',
    assetModuleFilename: 'static/images/[hash][ext][query]',
    libraryTarget: 'amd',
    library: moduleName,
    publicPath: '',
    clean: true
  },
  plugins: [
    new CssMinimizerPlugin(),
    new HtmlWebpackPlugin({
      moduleName,
      appName: userConfig.appName,
      template: templatePath, // 内置html模板路径
      inject: false,
      env: 'production'
    })
  ]
};
```
