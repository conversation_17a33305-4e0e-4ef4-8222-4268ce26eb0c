import './index.less';

import React, {useMemo} from 'react';

interface GlobalNotifyMessageProps {
  /** 请求的接口路径 */
  url: string;
  /** 请求 ID */
  requestId: string;
  /** 错误信息 */
  message: string;
}

export const GlobalNotifyMessage: React.FC<GlobalNotifyMessageProps> = ({
  url,
  requestId,
  message
}) => {
  const moduleName = useMemo(() => {
    return url?.split?.('/')?.[2]?.toUpperCase?.();
  }, [url]);

  const ticketUrl = useMemo(() => {
    return `https://console.bce.baidu.com/ticket/#/ticket/create?module=${moduleName}&requestId=${requestId}`;
  }, [moduleName, requestId]);

  return (
    <>
      <span className="global-notify-message">{message}</span>
      <a
        className="global-notify-ticket-link"
        target="_blank"
        href={ticketUrl}
        rel="noreferrer"
      >
        提交工单
      </a>
    </>
  );
};
