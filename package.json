{"name": "create-bce-app", "private": "true", "description": "BaiduCloud Console-FE CLI", "workspaces": ["packages/*"], "scripts": {"build": "cba-scripts turbo build", "dev": "cba-scripts turbo dev --parallel", "release": "cba-scripts release --pkg-type cli", "version": "cba-scripts version --pkg-type cli", "link": "cba-scripts link", "clean:lib": "rimraf packages/*/lib", "clean:node_modules": "lerna clean", "version:creator": "cba-scripts version --pkg-type creator", "release:creator": "cba-scripts release --pkg-type creator"}, "license": "MIT", "dependencies": {"cba-scripts": "workspace:*", "@baidu/cba-cli": "workspace:*", "@baidu/cba-creator": "workspace:*", "@baidu/cba-preset-pagemaker-npm": "workspace:*", "@baidu/cba-preset-pagemaker-react": "workspace:*", "@baidu/cba-preset-console-react": "workspace:*", "@baidu/cba-preset-console-react-embed": "workspace:*", "@baidu/cba-preset-react": "workspace:*", "@baidu/cba-preset-sdk": "workspace:*", "@babel/core": "^7.23.9", "@babel/runtime": "^7.23.9", "@babel/preset-env": "^7.23.9", "@babel/preset-react": "^7.23.3", "@babel/preset-typescript": "^7.23.3", "@babel/plugin-transform-runtime": "^7.23.9", "@babel/runtime-corejs3": "^7.23.9", "babel-plugin-import": "^1.13.8", "babel-plugin-transform-decorators-legacy": "^1.3.5", "@svgr/webpack": "^8.1.0", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.11", "@typescript-eslint/eslint-plugin": "^6.20.0", "@typescript-eslint/parser": "^6.20.0", "react": "^17.0.2", "shelljs": "^0.8.4", "axios": "^1.6.7", "babel-loader": "^9.1.3", "css-loader": "^6.10.0", "less": "^4.2.0", "less-loader": "^11.1.4", "null-loader": "^4.0.1", "postcss": "^8.4.33", "postcss-loader": "^7.3.4", "url-loader": "^4.1.1", "style-loader": "^3.3.4", "react-hot-loader": "^4.13.1", "ejs": "^3.1.9", "eslint": "^8.56.0", "chalk": "^2.4.2", "clean-webpack-plugin": "^4.0.0", "copy-webpack-plugin": "^11.0.0", "css-minimizer-webpack-plugin": "^5.0.1", "write-file-webpack-plugin": "^4.5.1", "html-webpack-plugin": "^5.6.0", "mini-css-extract-plugin": "2.7.7", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-import": "^2.29.1", "eslint-plugin-simple-import-sort": "^10.0.0", "fs-extra": "^11.2.0", "husky": "^9.0.10", "i18next-parser": "^8.12.0", "json2po": "^1.0.5", "lerna": "^6.6.2", "postcss-preset-env": "^9.3.0", "prettier": "^3.2.5", "prettier-eslint": "^16.3.0", "react-router-dom": "^6.22.0", "rimraf": "^5.0.5", "tmp": "^0.2.1", "tslib": "^2.6.2", "tsx": "^4.7.0", "turbo": "^1.12.3", "typescript": "^5.3.3", "webpack": "^5.90.1", "webpack-cli": "^5.1.4", "webpack-dev-server": "^4.15.1", "webpack-merge": "^5.10.0", "webpack-node-externals": "^3.0.0", "webpack-virtual-modules": "^0.5.0"}, "devDependencies": {"@types/mustache": "^4.2.5", "@types/node": "^18.19.14"}}