import chalk from 'chalk';

export const prefixes = {
  wait: chalk.cyan('[wait]') + ' -',
  error: chalk.red('[error]') + ' -',
  fatal: chalk.red('[fatal]') + ' -',
  warn: chalk.yellow('[warn]') + ' -',
  ready: chalk.green('[ready]') + ' -',
  info: chalk.cyan('[info]') + ' -',
  event: chalk.magenta('[event]') + ' -',
  debug: chalk.gray('[debug]') + ' -',
  profile: chalk.blue('[profile]') + ' -'
};

export default {
  wait(...message: any[]) {
    console.log(prefixes.wait, ...message);
  },
  error(...message: any[]) {
    console.log(prefixes.error, ...message);
  },
  fatal(...message: any[]) {
    console.log(prefixes.fatal, ...message);
  },
  warn(...message: any[]) {
    console.log(prefixes.warn, ...message);
  },
  ready(...message: any[]) {
    console.log(prefixes.ready, ...message);
  },
  info(...message: any[]) {
    console.log(prefixes.info, ...message);
  },
  event(...message: any[]) {
    console.log(prefixes.event, ...message);
  },
  debug(...message: any[]) {
    console.log(prefixes.debug, ...message);
  },
  profile(...message: any[]) {
    console.log(prefixes.profile, ...message);
  }
};
