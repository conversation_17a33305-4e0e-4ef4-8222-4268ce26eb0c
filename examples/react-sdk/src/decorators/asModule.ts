import React from 'react';

import {toolkitConfig} from '../config';

interface AsModuleParams {
  /** 模块注册名称 */
  name?: string;
}

class ModuleRegistry {
  registry: {
    [key: string]: React.Component;
  };

  constructor() {
    this.registry = {};
  }

  register(name: string, Component: React.Component) {
    if (this.registry[name]) {
      throw new Error(`this module <${name}> is already registered`);
    }

    this.registry[name] = Component;
  }

  get(name: string): React.Component {
    return this.registry[name];
  }
}

export const moduleRegistry = new ModuleRegistry();

export function asModule(params?: AsModuleParams) {
  const {name} = params || {};
  return function <T extends {new (...args: any[]): object}>(constructor: T) {
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    const registerName = name || constructor.name;
    const target = class extends constructor {};

    if (!toolkitConfig.getConfig('isEmbed')) {
      return target;
    }

    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-expect-error
    moduleRegistry.register(registerName, target);

    return target;
  };
}
