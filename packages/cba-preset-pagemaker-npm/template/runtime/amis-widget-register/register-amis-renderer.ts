import {Renderer, FormItem, OptionsControl} from 'amis';

export const consoleTag = '[amis-widget]';
const npmCustomWidgetTag = ''; // npm自定义组件前缀 npm-custom

export function isString(str: any): boolean {
  return Object.prototype.toString.call(str).slice(8, -1) === 'String';
}

export function isObject(obj: any): boolean {
  return Object.prototype.toString.call(obj).slice(8, -1) === 'Object';
}

export enum Usage {
  renderer = 'renderer',
  formitem = 'formitem',
  options = 'options'
}

export function getUsage(_usage?: string): string {
  let defaultUsage = Usage.renderer;
  if (!_usage) {
    return defaultUsage;
  }
  let curUsage = _usage.toLowerCase().trim();
  switch (curUsage) {
    case 'renderer':
    case 'renderers':
      curUsage = Usage.renderer;
      break;
    case 'formitem':
    case 'form-item':
    case 'form item':
      curUsage = Usage.formitem;
      break;
    case 'options':
    case 'option':
    case 'formoption':
    case 'form-option':
    case 'form option':
      curUsage = Usage.options;
      break;
    default:
      curUsage = Usage.renderer;
  }
  return curUsage;
}

/**
 * 自定义editor插件配置项
 */
export interface AmisRendererOption {
  /**
   * 渲染器名称
   * 备注：渲染过程中用于查找对应的渲染器
   */
  type: string;

  /**
   * 要注册的amis渲染器类型
   * amis普通渲染器、amis表单渲染器、amis表单控件渲染器
   * 备注：默认为amis普通渲染器
   */
  usage?: string;

  /**
   * 自定义组件权重
   * 备注：值越低越优先命中
   */
  weight?: number;

  /**
   * 自定义组件技术栈类型
   * 备注：默认为react
   */
  framework?: string;

  storeType?: any;
}

declare const window: Window & {
  postMessage: any;
  AmisCustomRenderers: any;
};

function addCustomPrefixType(_type) {
  let curType = _type;
  if (!_type) {
    return curType;
  }
  if (_type && _type.indexOf(npmCustomWidgetTag) < 0) {
    curType = `${npmCustomWidgetTag}-${_type}`;
  }
  return curType;
}

/**
 * registerRendererByType: 根据type类型注册amis普通渲染器、amis表单渲染器、amis表单控件渲染器
 *【方法参数说明】
 * newRenderer: 新的渲染器,
 * rendererOption: {
 *   type: 渲染器的type类型，比如：input、text-area、select-user等
 *   usage?: amis普通渲染器、amis表单渲染器、amis表单控件渲染器
 *   weight?: 自定义组件权重
 *   framework?: 技术栈类型，默认为 react 技术栈，可选技术栈：vue2、react、jquery
 * }
 * 备注：暂不支持 vue3.0 技术栈
 */
function registerRendererByType(newRenderer: any, rendererOption: string | AmisRendererOption) {
  if (!newRenderer) {
    return;
  }
  // 1.默认注册配置参数
  const curRendererOption: AmisRendererOption = {
    type: '',
    usage: Usage.renderer, // 默认为 amis普通渲染器
    weight: 0,
    framework: 'react' // 默认为 react 技术栈
  };
  // 2.获取相关配置参数
  if (rendererOption && isString(rendererOption)) {
    // rendererOption为字符串则将其设置为type
    Object.assign(curRendererOption, {
      type: rendererOption
    });
  } else {
    Object.assign(curRendererOption, rendererOption);
  }

  if (curRendererOption && !curRendererOption.type) {
    console.error(`${consoleTag}amis渲染器注册失败，渲染器类型（type）不能为空。`);
  } else {
    // 增加NpmCustom前缀
    curRendererOption.type = addCustomPrefixType(curRendererOption.type);
    // 修正framework数值
    curRendererOption.framework = 'react';
    // 修正usage数值
    curRendererOption.usage = getUsage(curRendererOption.usage);
    // 当前支持注册的渲染器类型
    const registerMap: any = {
      renderer: Renderer,
      formitem: FormItem,
      options: OptionsControl
    };

    // 当前支持的技术栈类型
    const resolverMap: any = {
      react: (i: any) => i
    };
    // 支持多技术栈
    const curRendererComponent = resolverMap[curRendererOption.framework](newRenderer);
    // 注册amis渲染器
    if (!registerMap[curRendererOption.usage]) {
      console.error(`${consoleTag}自定义组件注册失败，不存在${curRendererOption.usage}自定义组件类型。`);
    } else {
      registerMap[curRendererOption.usage]({
        type: curRendererOption.type,
        weight: curRendererOption.weight,
        storeType: curRendererOption.storeType
      })(curRendererComponent);
      // 记录当前创建的amis自定义组件
      console.info('注册了一个自定义amis组件:', {
        type: curRendererOption.type,
        weight: curRendererOption.weight,
        component: curRendererComponent,
        framework: curRendererOption.framework,
        usage: curRendererOption.usage
      });
    }
  }
}

function AddAmisCustomRenderer(componentType: string, rendererData: any) {
  if (window && !window.AmisCustomRenderers) {
    window.AmisCustomRenderers = {};
  }
  if (!window.AmisCustomRenderers[componentType]) {
    window.AmisCustomRenderers[componentType] = rendererData;
    return componentType;
  }
  console.error(`${consoleTag}注册amis渲染器失败，已存在重名渲染器(${componentType})。`);

  return null;
}

export default registerRendererByType;
