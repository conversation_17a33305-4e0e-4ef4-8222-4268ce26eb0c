import {toast} from 'acud';
import axios, {
  AxiosInstance,
  AxiosResponse,
  InternalAxiosRequestConfig
} from 'axios';
import Cookies from 'js-cookie';
import React from 'react';

import {GlobalNotifyDescription} from '../components/GlobalNotifyDescription';
import {GlobalNotifyMessage} from '../components/GlobalNotifyMessage';

toast.config({
  top: 60
});

declare module 'axios' {
  interface AxiosRequestConfig {
    /** 开启静默请求 */
    silent?: boolean;
  }
}

const request: AxiosInstance = axios.create({
  baseURL: '',
  timeout: 0
});

request.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    config.headers['csrftoken'] = Cookies.get('bce-user-info');
    return config;
  },
  (err) => Promise.reject(err)
);

request.interceptors.response.use(
  (response: AxiosResponse) => {
    const {status, data, headers, config} = response;

    if (config.silent) {
      return data;
    }

    if (status === 200 && [false, 'false'].includes(data?.success)) {
      const requestId = headers?.['x-bce-request-id'];
      let message = data?.message?.global || data?.message?.noSession;

      if (!message && !data?.message?.field) {
        message = '系统提示：请求失败（未知错误）';
      }

      if (message) {
        toast.error({
          message: (
            <GlobalNotifyMessage
              url={config.url!}
              requestId={requestId!}
              message={data.message.global}
            ></GlobalNotifyMessage>
          ),
          description: (
            <GlobalNotifyDescription
              requestId={requestId}
            ></GlobalNotifyDescription>
          ),
          key: headers?.['x-bce-request-id'],
          duration: 5,
          className: 'global-toast-error-container'
        });
      }
    }

    return data;
  },
  (err) => Promise.reject(err)
);

export type BaseResponseType<T> = Promise<{
  success: boolean;
  status: number;
  result: T;
}>;

export {request};
