/**
 * @file webpack dev
 * <AUTHOR> (<EMAIL>)
 */
import merge from 'webpack-merge';
import WebpackDevServer from 'webpack-dev-server';
import webpack, {Configuration} from 'webpack';
import {Env} from '@baidu/cba-core';
import {findFreePort} from '@baidu/cba-utils';
import {DEFAULT_PORT, MAX_PORT} from '../constant';
import {getConfig} from './config/config';
import {mergeUserConfig} from './mergeUserConfig';

interface IOpts {
  env: Env;
  cwd: string;
  userConfig: Record<string, any>;
  chainWebpack?: Function;
  modifyWebpackConfig?: Function;
  onBeforeServerStart?: Function;
  onServerStarted?: Function;
}

function getOriginFromServer(server: WebpackDevServer) {
  return `${
    server.options.https || server.options.http2 ? 'https' : 'http'
  }://${server.options.host}:${server.options.port}`;
}

function getOriginFromWebpackConfig(webpackConfig: any) {
  return `${webpackConfig?.devServer?.https || webpackConfig?.devServer?.https ? 'https' : 'http'}://${
    webpackConfig?.devServer?.host || 'localhost'
  }:${webpackConfig?.devServer?.port || '8899'}`;
}

function updateDevServerPortAndHost(...configs: Configuration[]) {
  let config = merge({}, ...configs);
  const port = (config.devServer && config.devServer.port) || DEFAULT_PORT;
  const host = (config.devServer && config.devServer.host) || 'localhost';
  config = merge(config, {
    devServer: {
      port,
      host,
      client: {
        webSocketURL: `auto://${host}:${port}/ws`
      }
    }
  });
  config = merge(config, {
    plugins: [
      new webpack.DefinePlugin({
        SERVER_ORIGIN: JSON.stringify(getOriginFromWebpackConfig(config))
      })
    ]
  });
  return config;
}

export async function dev(opts: IOpts) {
  let config = await getConfig({
    env: opts.env,
    cwd: opts.cwd,
    userConfig: opts.userConfig,
    chainWebpack: opts.chainWebpack,
    modifyWebpackConfig: opts.modifyWebpackConfig
  });

  // 端口检测
  let avaliablePort = await findFreePort(config.devServer?.port || DEFAULT_PORT, MAX_PORT);
  if (config.devServer?.port !== avaliablePort) {
    config.devServer ||= {};
    config.devServer.port = avaliablePort;
  }

  // 合并用户配置
  config = mergeUserConfig(config, opts.userConfig);

  // 更新devServer host、port 相关配置
  config = updateDevServerPortAndHost(config);

  // 启动 server
  const compiler = webpack(config);
  const server = new WebpackDevServer(
    {
      ...config.devServer
    },
    compiler
  );

  const origin = getOriginFromServer(server);

  if (opts.onBeforeServerStart) {
    await opts.onBeforeServerStart({config, server, origin});
  }

  process.on('SIGINT', () => {
    console.log('结束进程');
    server.stop();
    process.exit(0);
  });

  await server.start();

  if (opts.onServerStarted) {
    await opts.onServerStarted({config, server, origin});
  }
}
