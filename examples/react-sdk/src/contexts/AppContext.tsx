import React, {createContext, ReactNode, useMemo, useReducer} from 'react';

/** 产品开通参数 */
export interface ServiceParam {
  roleName: string;
  policyId: string;
  serviceId: string;
  /** 是否激活 */
  isActivated?: boolean;
}

interface State {
  /** 产品是否激活 */
  isActivated: boolean;
  /** 接口是否加载完成 */
  isPreRequestCompleted: boolean;
  /** 服务参数, 用于产品激活 */
  serviceParams: ServiceParam[];
  /** 菜单是否收起 */
  isMenuCollapsed?: boolean;
  /** 是否作为模块进行嵌入 */
  isEmbed: boolean;
}

export enum AppContextActionType {
  /** 激活产品 */
  ACTIVATE_PRODUCT = 'ACTIVATE_PRODUCT',
  /** 完成产品前置请求 */
  COMPLETE_PRE_REQUEST = 'COMPLETE_PRE_REQUEST',
  /** 设置产品开通参数 */
  SET_SERVICE_PARAMS = 'SET_SERVICE_PARAMS',
  /** 收起展开菜单  */
  TOGGLE_MENU = 'TOGGLE_MENU'
}

interface Action {
  type: AppContextActionType;
  payload?: Partial<State>;
}

const initialState: State = {
  isActivated: false,
  isPreRequestCompleted: false,
  serviceParams: [],
  isMenuCollapsed: false,
  /** 是否作为模块进行嵌入 */
  isEmbed: false
};

const reducer = (state: State, action: Action) => {
  switch (action.type) {
    case AppContextActionType.ACTIVATE_PRODUCT: {
      if (sessionStorage.getItem('_bce_activation_')) {
        return {
          ...state,
          isActivated: false
        };
      }
      return {
        ...state,
        isActivated: true
      };
    }
    case AppContextActionType.COMPLETE_PRE_REQUEST: {
      return {
        ...state,
        isPreRequestCompleted: true
      };
    }
    case AppContextActionType.SET_SERVICE_PARAMS: {
      return {
        ...state,
        serviceParams: action.payload!.serviceParams!
      };
    }
    case AppContextActionType.TOGGLE_MENU: {
      return {
        ...state,
        isMenuCollapsed: !state.isMenuCollapsed
      };
    }
    default: {
      return state;
    }
  }
};

const AppContext = createContext<{
  appState: State;
  appDispatch: React.Dispatch<Action>;
}>({
  appState: initialState,
  appDispatch: () => initialState
});

interface AppProviderProps {
  children: ReactNode;
  isEmbed?: boolean;
}

const AppProvider: React.FC<AppProviderProps> = ({
  isEmbed = false,
  children
}) => {
  const [state, dispatch] = useReducer(reducer, {
    ...initialState,
    isEmbed
  });

  const context = useMemo(() => {
    return {appState: state, appDispatch: dispatch};
  }, [state, dispatch]);

  return <AppContext.Provider value={context}>{children}</AppContext.Provider>;
};

function useAppContext() {
  const context = React.useContext(AppContext);
  if (!context) {
    throw new Error('useAppContext must be used within a AppProvider');
  }
  return context;
}

export {AppContext, AppProvider, useAppContext};
