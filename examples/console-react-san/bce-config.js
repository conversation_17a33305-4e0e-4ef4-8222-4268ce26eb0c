/**
 * @file 项目配置
 */
const {defineConfig} = require('@baidu/cba-cli');

module.exports = defineConfig({
  appName: 'demo-mix-san',
  appTitle: 'console-demo-mix-san',
  presets: ['@baidu/cba-preset-console-react'],
  mixSdp: true,
  babelOptions: {
    plugins: ['@babel/plugin-proposal-optional-chaining']
  },
  proxyTarget: 'https://qasandbox.bcetest.baidu.com',
  i18n: {
    enabled: true,
    independent: false
  },
  sdpDependencies: {
    '@baidu/bce-opt-checker':
      'https://bce.bdstatic.com/lib/@baiducloud/bce-opt-checker/1.0.3.1/checker.js'
  },
  webpack: {
    externals: [
      'san-router',
      '@baiducloud/bce-ui/san',
      '@baiducloud/httpclient',
      '@baiducloud/runtime',
      '@baidu/sui-biz',
      '@baidu/sui',
      'san',
      '@baiducloud/bce-opt-checker'
    ]
  }
});
