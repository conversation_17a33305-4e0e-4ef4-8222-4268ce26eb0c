/*
 * @Date: 2021-01-15 11:19:08
 * @LastEditTime: 2021-01-18 11:27:10
 * @FilePath: /newlss/.eslintrc.js
 * @Author: <EMAIL>
 */

module.exports = {
  extends: [
    '@ecomfe/eslint-config',
    '@ecomfe/eslint-config/import',
    '@ecomfe/eslint-config/typescript',
    'plugin:prettier/recommended'
  ],
  env: {
    node: true,
    browser: true,
    commonjs: true,
    es2021: true
  },
  settings: {
    'import/resolver': {
      // typescript: {
      //     alwaysTryTypes: true
      // },
      node: {
        extensions: ['.js', '.jsx', '.ts', '.tsx']
      }
    }
  },
  rules: {
    'comma-dangle': 'off',
    'import/unambiguous': 'off',
    '@typescript-eslint/no-unused-vars': 'off',
    '@typescript-eslint/no-empty-function': 'off',
    '@typescript-eslint/no-this-alias': 'off',
    '@typescript-eslint/restrict-plus-operands': 'off',
    '@typescript-eslint/no-require-imports': 'off',
    '@typescript-eslint/no-var-requires': 'off',
    '@typescript-eslint/init-declarations': 'off',
    '@typescript-eslint/ban-types': 'off',
    'import/no-absolute-path': 'off',
    'import/no-dynamic-require': 'off',
    'import/no-unresolved': 'off',
    'no-console': 'off',
    'no-loop-func': 'off',
    'guard-for-in': 'off',
    'max-len': 'off'
  },
  overrides: [
    {
      files: ['scripts/**/*.js'],
      rules: {
        'import/unambiguous': 'off',
        'import/no-commonjs': 'off',
        'no-console': 'off'
      }
    }
  ]
};
