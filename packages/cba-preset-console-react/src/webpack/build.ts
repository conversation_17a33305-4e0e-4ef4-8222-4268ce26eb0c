import path from 'path';
import {getFlags} from '@baidu/cba-utils';
import {IApi} from '@baidu/cba-preset';

import HtmlWebpackPlugin from 'html-webpack-plugin';
import CssMinimizerPlugin from 'css-minimizer-webpack-plugin';
import SdpEslConfig from './sdpEslConfig';

const CONSOLE_TYPE = process.env.CONSOLE_TYPE;
const isPrivate = process.env.BUILD_TYPE === 'private';

export default (api: IApi) => {
  api.describe({
    key: 'preset-console-react:webpack-build',
    enableBy: () => api.env === 'production'
  });

  api.modifyWebpackConfig((webpackConfig, {merge}) => {
    const moduleName = `${api.userConfig.appName}AppModule`;
    const mixSdp = api.userConfig.mixSdp;
    const indexName = mixSdp ? (isPrivate ? 'private_react_sdp' : 'react_sdp') : isPrivate ? 'private_react' : 'react';
    const templatePath = api.userConfig.htmlTemplate
      ? path.isAbsolute(api.userConfig.htmlTemplate)
        ? api.userConfig.htmlTemplate
        : path.join(api.cwd, api.userConfig.htmlTemplate)
      : path.resolve(__dirname, `../../template/${indexName}.html`);
    return merge(webpackConfig, {
      performance: false,
      entry: undefined,
      output: {
        filename: 'static/js/[name].js',
        chunkFilename: 'static/js/[name].chunk.js',
        assetModuleFilename: 'static/images/[hash][ext][query]',
        libraryTarget: 'amd',
        library: moduleName,
        publicPath: '',
        clean: true
      },
      plugins: [
        new CssMinimizerPlugin(),
        new HtmlWebpackPlugin({
          moduleName,
          onlineConfigFile: api.userConfig.onlineConfigName || 'online-config',
          appName: api.userConfig.appName,
          sdpDeps: JSON.stringify(api.userConfig.sdpDependencies || {}),
          sdpMaps: JSON.stringify(api.userConfig.sdpEslConfigAddMap || {}),
          template: templatePath,
          consoleType: CONSOLE_TYPE === 'xs-console' ? CONSOLE_TYPE : 'console',
          useAlphaRelease: process.env.ALPHA_RELEASE === 'true',
          inject: false,
          env: 'production',
          isPrivateStandard330: api.userConfig.isPrivateStandard330,
          flags: api.userConfig.flags
        }),
        ...(mixSdp
          ? [
              new SdpEslConfig({
                sdpDeps: api.userConfig.sdpDependencies || {},
                sdpMaps: api.userConfig.sdpEslConfigAddMap || {},
                isPrivate
              })
            ]
          : [])
      ]
    });
  });

  api.onBeforeBundlerStart(async webpackConfig => {
    const flagPath = path.join(api.cwd, './src/flags.ts');
    await getFlags(
      {
        templateId: api.userConfig.templateId,
        flags: api.userConfig.flags
      },
      flagPath
    );
  });
};
