/**
 * @file 项目配置
 */
const {defineConfig} = require('@baidu/cba-cli');

module.exports = defineConfig({
  appName: 'dbsc',
  appTitle: '数据库智能驾驶舱 DBSC',
  presets: ['@baidu/cba-preset-console-react'],
  babelOptions: {
    plugins: ['@babel/plugin-proposal-optional-chaining']
  },
  proxyTarget: 'https://qasandbox.bcetest.baidu.com',
  i18n: {
    enabled: true,
    independent: false
  },
  mockup: {
    caching: true,
    rules: ['/api/dbsc/(.*)'],
    root: '.mockrc',
  }
});
