import {resolve} from 'path';
import deepmerge from 'deepmerge';
import {readFileSync, writeFileSync} from 'fs-extra';
import {isCliPresetPackage, getPackageLatestVersion} from './utils';
import logger from './logger';

/**
 * 更新package.json
 * @param
 */
export function updatePackageJSON({data, cwd = process.cwd()}: {data: object; cwd?: string}) {
  const packageJsonPath = resolve(cwd, 'package.json');
  const pkg = require(packageJsonPath);
  const projectPkg = deepmerge(pkg, data) as object;
  writeFileSync(packageJsonPath, `${JSON.stringify(projectPkg, null, 2)}\n`, 'utf-8');
}

/**
 * 更新指定package.json中脚手架的依赖版本
 * @param pkgPath
 * @returns
 */
export function updatePackageCliVersion(pkgPath: string) {
  try {
    // 各包统一版本发布的，取cba-cli的版本作为最新版本。
    const pkg = JSON.parse(readFileSync(pkgPath, 'utf-8'));
    Object.keys(pkg.dependencies || {}).forEach(key => {
      if (isCliPresetPackage(key)) {
        const version = getPackageLatestVersion(key);
        pkg.dependencies[key] = `${version}`;
      }
    });
    Object.keys(pkg.devDependencies || {}).forEach(key => {
      if (isCliPresetPackage(key)) {
        const version = getPackageLatestVersion(key);
        pkg.devDependencies[key] = `${version}`;
      }
    });
    writeFileSync(pkgPath, JSON.stringify(pkg, null, 2), 'utf-8');
  } catch (error) {
    logger.error(`为「${pkgPath}」更新脚手架版本失败`, error);
  }
}
