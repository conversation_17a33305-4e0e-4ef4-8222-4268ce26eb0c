module.exports = function getData(appData) {
  const useStore = appData.stateName === 'mobx';
  // eslint-disable-next-line
  const imports = useStore ? `import {inject, observer} from 'mobx-react';`
  // eslint-disable-next-line
    : `import {useState} from 'react';`;
  const stateScript = useStore ? '' : 'const [count, setCount] = useState(0);';

  const stateDemo = useStore ? `<div
    className="touch-me"
    onClick={() => {
      props?.demoStore?.setNumber();
    }}
  >
    <div className="label">Touch Me</div>
    <div className="count">{props?.demoStore.number}</div>
  </div>`
    : `
    <div
      className="touch-me"
      onClick={() => setCount(count + 1)}
    >
      <div className="label">Touch Me</div>
      <div className="count">{count}</div>
    </div>
    `;
  // eslint-disable-next-line
  const exportComponent = useStore ? `export default inject('demoStore')(observer(PageHome));`
    : 'export default PageHome;';

  return {
    imports,
    stateScript,
    stateDemo,
    exportComponent
  };
};
