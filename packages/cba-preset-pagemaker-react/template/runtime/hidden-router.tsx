import React from 'react';
import {useRoutes, Browser<PERSON>outer, HashRouter} from 'react-router-dom';

export const createRoutes = (routerPrefix: string, routes: any[] = []) => {
  return routes.map(item => ({
    ...item,
    path: `${item.path === '*' ? '' : routerPrefix}${item.path}`
  }));
};

export enum RouterMode {
  HISTORY = 'history',
  HASH = 'hash'
}

export interface IRoutesProps {
  routerPrefix: string;
  routes: any[];
}

export interface IRouterProps extends IRoutesProps {
  routerMode: RouterMode;
}

const Router = ({routerPrefix = '', routes = []}: IRoutesProps) => {
  const res = useRoutes(createRoutes(routerPrefix, routes));
  return res;
};

export default ({routerPrefix = '', routerMode = RouterMode.HASH, routes = []}: IRouterProps) => {
  const RouterWrapper = routerMode === RouterMode.HISTORY ? BrowserRouter : HashRouter;

  return (
    <RouterWrapper>
      <Router routerPrefix={routerPrefix} routes={routes} />
    </RouterWrapper>
  );
};
