/**
 * @file 配置文件
 * <AUTHOR> (<EMAIL>)
 */

import {existsSync} from 'fs';
import {join} from 'path';
import yargsParser from 'yargs-parser';
import {DEFAULT_CONFIG} from '../utils/constants';
import {IUserConfig} from '../types';

interface IConfigOpts {
  /**
   * 当前工作目录
   */
  cwd: string;
  /**
   * 命令行参数
   */
  args: yargsParser.Arguments;

  /**
   * package.json信息
   */
  pkg: Record<string, string | Record<string, any>>;

  /**
   * 默认配置文件路径（相对路径）
   */
  defaultConfigFile?: string;
}

export class Config {
  cwd: string;
  args: yargsParser.Arguments;
  pkg: Record<string, string | Record<string, any>>;
  defaultConfigFile?: string;

  constructor(opts: IConfigOpts) {
    this.cwd = opts.cwd;
    this.args = opts.args;
    this.pkg = opts.pkg;
    this.defaultConfigFile = opts.defaultConfigFile;
  }

  getUserConfig(): IUserConfig {
    let userConfig = {};
    if (!this.args.config && !this.defaultConfigFile) {
      return userConfig;
    }
    const path: string = join(this.cwd, this.args.config || this.defaultConfigFile);
    if (existsSync(path)) {
      userConfig = this.resolveDefaultConfig(require(path));
    }
    return userConfig;
  }

  resolveDefaultConfig(config: any = {}) {
    config = {
      ...DEFAULT_CONFIG,
      ...config
    };

    if (this.args.template) {
      config.template = this.args.template;
    }

    config.name = config.name || this.pkg.name;

    return config;
  }
}
