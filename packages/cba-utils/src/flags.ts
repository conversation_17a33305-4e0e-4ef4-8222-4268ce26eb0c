import {writeFileSync} from 'fs-extra';
import https from 'https';
import logger from './logger';
import {formatFile} from './utils';

interface IFlag {
  service: string;
  templateId: string;
}

interface IFlagConfig {
  templateId?: string;
  flags?: string[];
}

const getFlagsUrl = (service: string, templateId: string) => {
  return `https://console-center.bj.bcebos.com/type-definition/${service.toLowerCase()}/type-definition@${templateId}.json`;
};

export default async (flagConfig: IFlagConfig, flagPath: string) => {
  try {
    const textCodes = ['// 此文件为功能清单文件，请勿删除'];
    const {flags = [], templateId = ''} = flagConfig || {};
    let flagObj = {};
    if (templateId && flags.length) {
      const flagList = flags.map((service: string) => ({service, templateId}));
      const urls = flagList.map(({service, templateId}: IFlag) => getFlagsUrl(service, templateId));
      const flagListPromises = urls.map(
        (url: string) =>
          new Promise((resolve, reject) => {
            https.get(url, res => {
              if (res.statusCode !== 200) {
                return reject(new Error(`BOS Server Error = ${res.statusCode}`));
              }
              let buffer = '';
              res.on('data', chunk => {
                buffer += chunk;
              });
              res.on('end', () => {
                try {
                  resolve(JSON.parse(buffer));
                } catch (e: any) {
                  reject(new Error(`Parser Data Error = ${e.message}`));
                }
              });
            });
          })
      );
      const res = await Promise.all(flagListPromises);
      flagObj = res.reduce((pre: Record<string, boolean>, cur: any) => {
        cur.forEach((flag: string) => (pre[flag] = true));
        return pre;
      }, {});
      textCodes.push(`export default sessionStorage.getItem('_flags')
        ? JSON.parse(sessionStorage.getItem('_flags') || '{}')
        : ${JSON.stringify(flagObj, null, 2)};`);
    } else {
      textCodes.push(`export default sessionStorage.getItem('_flags')
        ? JSON.parse(sessionStorage.getItem('_flags') || '{}')
        : {};`);
    }
    writeFileSync(flagPath, textCodes.join('\n'), 'utf-8');
    await formatFile(flagPath);
    logger.info('功能清单获取成功！', JSON.stringify(flagObj));
  } catch (error) {
    logger.error('get flags failed!!!', error);
  }
};
