import {Button} from 'acud';
import React from 'react';

import RefreshIcon from '@/assets/svg/refresh.svg';

import styles from './index.module.less';

interface RefreshButtonProps {
  onClick?: () => void;
}

const RefreshButton: React.FC<RefreshButtonProps> = ({onClick}) => {
  return (
    <Button
      icon={<RefreshIcon className={styles.refreshIcon} />}
      onClick={onClick}
    ></Button>
  );
};

export default RefreshButton;
