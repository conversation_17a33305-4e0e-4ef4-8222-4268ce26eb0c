{"compilerOptions": {"moduleResolution": "Node", "baseUrl": "./", "paths": {"@/*": ["src/*"], "react": ["./node_modules/@types/react"]}, "types": ["@baidu/bce-react-toolkit/es/types/global.d.ts"], "target": "ES5", "lib": ["DOM", "ES6", "ES2017", "ESNext"], "module": "ESNext", "jsx": "react", "strict": true, "allowJs": true, "esModuleInterop": true, "allowUmdGlobalAccess": true, "noImplicitAny": false, "experimentalDecorators": true}}