import {execSync} from 'child_process';
import inquirer from 'inquirer';
import yargsParser from 'yargs-parser';
import {join} from 'path';
import 'zx/globals';

import {formatDate, getPkgs, getCurrentVersion} from '../utils';
import {CHANGELOG_PREFIX, PATHS, ROOT} from '../utils/constants';
import logger from '../utils/logger';

/**
 * 生成版本信息
 * @param releaseNotes
 * @param version
 */
function generateChangelog(logPath: string, releaseNotes: string, version: string) {
  const CHANGELOG_PATH = join(ROOT, logPath);
  const hasFile = fs.existsSync(CHANGELOG_PATH);
  let newStr = '';
  releaseNotes = `## ${version} (${formatDate(new Date(), 'YYYY-MM-dd')})\n\n${releaseNotes}`;

  if (hasFile) {
    const content = fs.readFileSync(CHANGELOG_PATH, 'utf-8');
    const arr = content.split(CHANGELOG_PREFIX);

    newStr = `${CHANGELOG_PREFIX}\n\n${releaseNotes}${arr[1]}`;
  } else {
    newStr = `${CHANGELOG_PREFIX}\n\n${releaseNotes}`;
  }
  fs.writeFileSync(CHANGELOG_PATH, newStr);
}

(async () => {
  let args = yargsParser(process.argv.slice(2));
  // 检查代码
  // const isGitClean = !(await $`git status --porcelain`).stdout.trim().length;
  // assert(isGitClean, '存在未提交的代码，请先提交');

  // 检查版本号
  const pkgTypeConfig = require(PATHS.CONFIG)[args.pkgType];

  const version = getCurrentVersion(args.pkgType);
  const pkgs = getPkgs(PATHS.PACKAGES, args.pkgType);

  // 确定tag标签
  let defaultTag = 'latest';
  if (version.includes('-alpha.') || version.includes('-beta.') || version.includes('-rc.')) {
    defaultTag = 'beta';
  }

  const {tag} = await inquirer.prompt([
    {
      type: 'list',
      message: `请选择发布版本的tag标签`,
      choices: [
        {
          name: 'latest (最新版本)',
          value: 'latest'
        },
        {
          name: 'beta (测试版本)',
          value: 'beta'
        },
        {
          name: 'next（破坏性升级版本）',
          value: 'next'
        }
      ],
      name: 'tag',
      default: defaultTag
    }
  ]);

  logger.info('项目构建');
  await $`pnpm run clean:lib`;
  await $`pnpm run build`;

  logger.info(`发布应用包`);


  await Promise.all(
    pkgs.map(async pkg => {
      await $`cd packages/${pkg} && pnpm publish --no-git-checks --tag ${tag}`;
    })
  );

  execSync(`git tag -a ${version} -m "Release version ${version}"`, {
    stdio: 'inherit'
  });

  logger.info(`版本发布成功 (version: ${version}， tag: ${tag})`);

  if (!version.includes('-alpha.')) {
    // 请输入发布内容
    const {releaseNotes} = await inquirer.prompt([
      {
        type: 'editor',
        message: '请输入版本信息',
        name: 'releaseNotes',
        default: `
**【新增功能】**

-

**【问题修复】**

-
      `
      }
    ]);
    generateChangelog(pkgTypeConfig.changelogPath, releaseNotes, version);
  }
})();
