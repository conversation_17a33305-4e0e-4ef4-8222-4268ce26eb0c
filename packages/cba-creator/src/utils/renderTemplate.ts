import assert from 'assert';
import {
  statSync,
  mkdirSync,
  readdirSync,
  copyFileSync,
  existsSync
} from 'fs-extra';
import path from 'path';

import {
  writeTpl2File,
  formatFile
} from '@baidu/cba-utils';


/**
 * 将模板文件生成工程文件
 * @param tplRoot 模板文件根目录
 * @param source  模板文件路径
 * @param target  文件在工程中路径
 */
export default async function renderTemplate(source: string, target: string, appData: Record<string, any> = {}) {
  assert((existsSync(source) || existsSync(target)), '文件不存在');

  const stats = statSync(source);

  if (stats.isDirectory()) {
    if (path.basename(source) === 'node_modules') {
      return;
    }
    mkdirSync(target, { recursive: true });
    for (const file of readdirSync(source)) {
      await renderTemplate(path.resolve(source, file), path.resolve(target, file), appData);
    }
    return;
  }
  const filename = path.basename(source);

  switch (true) {
    case filename.endsWith('.tpl'): {
      const mjsFile = source.replace('.tpl', '.data.js');
      let fileData = {};
      if (existsSync(mjsFile)) {
        const getData = (await import(mjsFile)).default;
        fileData = await getData(appData);
      }
      target = target.replace('.tpl', '');
      writeTpl2File({
        tplPath: source,
        targetPath: target,
        data: {
          ...appData,
          ...fileData
        }
      });
      await formatNormalFile(target);
      return;
    }
    case filename.endsWith('.data.js'): {
      return;
    }
    // npm 发包 .npmrc和.gitignore会丢，需要处理下
    case ['_npmrc', '_gitignore'].includes(filename): {
      target = target.replace(filename, '.' + filename.slice(1));
      copyFileSync(source, target);
    }
    default: {
      copyFileSync(source, target);
      await formatNormalFile(target);
    }
  }
}

async function formatNormalFile(filePath: string = '') {
  const whiteFiles = ['.js','.jsx', '.ts', '.tsx', '.less', '.scss'];
  const extname = path.extname(filePath);
  if (whiteFiles.includes(extname)) {
    try {
      existsSync(filePath) && await formatFile(filePath);
    } catch (error) {
    }
  }
}
