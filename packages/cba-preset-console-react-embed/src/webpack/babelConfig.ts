/**
 * 获取babel配置
 */
import path from 'path';
import kPresetEnv from '@babel/preset-env';
import kTSPreset from '@babel/preset-typescript';
import kDynamicImportPlugin from '@babel/plugin-syntax-dynamic-import';
import kTSPresetReact from '@babel/preset-react';
import kProposalDecoratorPlugin from '@babel/plugin-proposal-decorators';
import kClassPropertiesPlugin from '@babel/plugin-proposal-class-properties';
import KTransform from '@babel/plugin-transform-runtime';
import {IUserConfig} from '@baidu/cba-preset';

export function getBabelConfig(userConfig: IUserConfig) {
  const isProduction = process.env.NODE_ENV === 'production';
  const {babelOptions: {presets = [], plugins = []} = {}, resolveOptions} = userConfig;
  // npm link查找模块安装包使用
  module.paths.unshift(path.join(process.cwd(), 'node_modules'));
  const babelConfig = {
    cacheDirectory: !isProduction,
    presets: [
      [
        kPresetEnv,
        {targets: '> 0.5%, last 2 versions, Firefox ESR, not dead'} // https://browserl.ist/
      ],
      kTSPreset,
      kTSPresetReact,
      ...presets
    ],
    plugins: [
      !isProduction && 'react-refresh/babel',
      [KTransform, {help: true}],
      kDynamicImportPlugin,
      [kProposalDecoratorPlugin, {legacy: true}],
      [kClassPropertiesPlugin, {loose: true}],
      ...plugins
    ].filter(Boolean)
  };

  // 通过函数回调重新配置babel
  return resolveOptions ? resolveOptions(babelConfig) : babelConfig;
}
