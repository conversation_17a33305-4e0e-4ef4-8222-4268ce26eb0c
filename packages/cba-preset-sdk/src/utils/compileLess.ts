import fs from 'fs';
import path from 'path';
import less from 'less';
import postcss from 'postcss';
import autoprefixer from 'autoprefixer';

export default function compileLess(lessFile: string) {
  let data = fs.readFileSync(lessFile, 'utf-8');
  data = data.replace(/^\uFEFF/, '');

  const lessOptions = {
    paths: [path.dirname(lessFile)],
    filename: lessFile,
    javascriptEnabled: true
  };

  return less
    .render(data, lessOptions)
    .then(ret => postcss([autoprefixer]).process(ret.css, {from: undefined}))
    .then(ret => ret.css);
}
