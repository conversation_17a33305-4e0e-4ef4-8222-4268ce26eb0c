.app-layout-container {
  background-color: #f7f7f9;
  bottom: 0;
  display: flex;
  height: 100%;
  left: 0;
  position: absolute;
  right: 0;

  .app-menu-container {
    position: relative;
    z-index: 99;
    flex-shrink: 0;

    .app-menu {
      width: 205px;
      height: 100%;
      z-index: 1;
      overflow-y: auto;

      :global {
        .acud-menu-inline-header-item {
          color: #84868c !important;
        }

        .acud-menu-item {
          user-select: none;
        }
      }
    }

    .hidden-app-menu {
      width: 0;
    }

    .menu-aside-hide-bar {
      align-items: center;
      background-color: initial;
      box-sizing: border-box;
      cursor: pointer;
      display: flex;
      flex-direction: column;
      height: 50px;
      justify-content: center;
      position: absolute;
      right: -15px;
      top: 50%;
      transform: translateY(-50%);
      width: 16px;
      z-index: 10;

      &::before {
        background: #fff;
        border-left: 0;
        border-radius: 0 10px 10px 0;
        bottom: 0;
        box-sizing: border-box;
        content: '';
        left: 0;
        position: absolute;
        right: 0;
        top: 0;
        transform: perspective(50px) rotateY(30deg);
        transition: all 0.15s;
        z-index: -1;
      }

      &::after {
        background-color: #a1a6b3;
        content: '';
        height: 8px;
        -webkit-mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='6' height='8' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M5.223.518.624 3.584a.5.5 0 0 0 0 .832l4.599 3.066A.5.5 0 0 0 6 7.066V.934a.5.5 0 0 0-.777-.416Z' fill='%23A1A6B3' fill-rule='nonzero'/%3E%3C/svg%3E");
        -webkit-mask-repeat: no-repeat;
        mask-repeat: no-repeat;
        -webkit-mask-size: 100% 100%;
        mask-size: 100% 100%;
        position: absolute;
        top: 50%;
        transform: translate(-50%, -50%) rotate(0deg);
        transition: all 0.15s;
        width: 6px;
      }
    }

    .menu-aside-hide-bar-is-hidden {
      &::after {
        transform: translate(-50%, -50%) rotate(180deg);
      }
    }
  }

  .page-wrapper {
    width: 0;
    flex: 1;
    overflow: auto;
    height: 100%;
    display: flex;
    flex-direction: column;

    .page-header-container {
      background-color: #fff;
      padding: 8px 0;

      .header-content-container {
        padding: 8px 16px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .menu-name {
          color: #151a26;
          font-weight: 500;
          font-size: 16px;
          margin: 0;

          .menu-name-text {
            vertical-align: middle;
          }
        }
      }

      .header-menu {
        height: 40px;
        line-height: 40px;
        box-shadow: none !important;

        :global {
          .acud-menu-item {
            padding-right: 16px;
            padding-left: 16px;
          }
        }
      }
    }

    .nav-page-content-container {
      background-color: #fff;
      border-radius: 4px;
      margin: 16px;
      padding: 24px;
      flex-grow: 1;
    }

    .nav-page-content-without-padding-container {
      padding: 0;
      background-color: transparent;
    }
  }

  .non-nav-page-content-container {
    width: 100%;
  }

  .modifySubtitle {
    margin-left: 8px;
    display: inline-block;
    font-size: 12px;
    transform: scale(0.75);
    transform-origin: left;
    border: 1px solid #ef7101;
    padding: 4px;
    border-radius: 4px;
    color: #ef7101;
    line-height: 12px;
  }
}

.suspenseContainer {
  width: 100%;
  height: 100%;
  background-color: #fff;
}
