const pagemakerProxyPrefix = '/api/pagemaker';

interface IRequestOptions {
  url: string; // 接口地址
  method?: 'get' | 'post' | 'put' | 'delete'; // 请求方法
  data?: object; // 请求数据
  responseType?: string;
  config?: object; // 其他配置
  headers?: object; // 请求头
}
let requestFetcher: any = null;

function setRequest(fetcher: any) {
  requestFetcher = fetcher;
}

async function amisRequest(
  options: IRequestOptions,
  proxy?: {
    path: string;
    target: string;
  }
) {
  if (!requestFetcher) {
    throw new Error('fetcher is required');
  }
  // 只有在pagemaker预览环境才需要走代理
  if (!!(window as any).amis && (window as any).amis.registerExternalApp) {
    if (!proxy) {
      // 默认都代理到 console-hub
      // to do 这里有问题
      options.url = options.url.replace('/api/', `${pagemakerProxyPrefix}/api/`);
    } else if (proxy.path && proxy.target) {
      // 如果配置了代理，自动修改 path，并添加headers
      const {pathPrefix, headers} = pagemakerProxy(proxy.path, proxy.target);
      options.url = options.url.replace(proxy.path, pathPrefix);
      options.headers = {
        ...options.headers,
        ...headers
      };
    }
  }
  if (!options.method) {
    options.method = 'get';
  }
  const res = await requestFetcher(options);
  return res;
}

/**
 * 用于处理 本地应用中访问接口是/api/v2时
 * 会变成 https://pagemaker.baidu-int.com/api/v2
 * 需要进行代理的情况
 * 仅在pagemaker 中预览需要
 * @param pathPrefix 匹配前缀，如 /api/v2
 * @param target 需要代理到的 origin 如 https://********:8000
 * @returns {Object} result
 * @returns {string} result.pathPrefix 可以被匹配到的代理前缀
 * @returns {Object} result.headers 需要代理的信息，需要添加到 header中，才可以生效
 */
function pagemakerProxy(
  pathPrefix: string,
  target: string
): {
  pathPrefix: string;
  headers: {
    'pagemaker-api-proxy-host'?: string;
  };
} {
  if (target && (window as any).amis && (window as any).amis.registerExternalApp) {
    return {
      pathPrefix: `${pagemakerProxyPrefix}${pathPrefix}`,
      headers: {
        'pagemaker-api-proxy-host': target
      }
    };
  }
  return {
    pathPrefix,
    headers: {}
  };
}

export {setRequest, amisRequest, pagemakerProxy};
