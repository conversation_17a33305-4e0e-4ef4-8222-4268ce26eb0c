.container {
  border-radius: 6px;
  padding: 40px;
  background-image: url(https://db-console-fe.bj.bcebos.com/dsc/20231214/activation/1920bg_2x.png);
  background-size: cover;
  position: relative;
  background-repeat: no-repeat;
  height: 360px;

  .title,
  .desc {
    position: relative;
    max-width: 880px;
    word-break: break-all;
    z-index: 10;
  }

  .title {
    font-size: 24px;
    color: #151b26;
    line-height: 32px;
    font-weight: 500;
    margin-bottom: 32px;
    margin-top: 32px;
  }

  .desc {
    font-size: 12px;
    color: #151b26;
    line-height: 22px;
    font-weight: 400;
  }

  .operateContainer {
    position: absolute;
    bottom: 40px;
  }

  .protocolContainer {
    margin-top: 12px;
    margin-bottom: 16px;
    display: flex;
    align-items: center;

    .agreeText {
      margin-left: 8px;
    }
  }

  .activateBtn {
    width: 160px;
    height: 40px;
    line-height: 40px;
    border-radius: 4px;
  }

  .banner {
    width: 320px;
    height: 262px;
    background-image: url(https://db-console-fe.bj.bcebos.com/dbsc/20231227/activation/dbsc_2x.png);
    background-size: cover;
    background-repeat: no-repeat;
    position: absolute;
    right: 80px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 0;
  }
}

.agreeTip {
  font-size: 12px;
  color: #f33e3e;
  text-align: center;
  line-height: 20px;
  font-weight: 400;
}

@media (max-width: 1920px) {
  .container {
    height: 300px;

    .desc {
      max-width: 800px;
    }
  }
}
