import {join} from 'path';
import {
  downloadTemplate,
  logger
} from '@baidu/cba-utils';
import {writeTpl2File} from '../utils';
import {addProject} from '.';

import type {IPrompt} from '.';

function generateUserConfig(prompt?: IPrompt, projectName?: string) {
  const {projectType} = prompt || {};
  const tplPath = join(__dirname, '../../template/bce-config.tpl');
  const targetPath = join(process.cwd(), `${projectName ? projectName : ''}/bce-config.js`);
  writeTpl2File({
    tplPath,
    targetPath,
    data: {
      projectType,
      presets: JSON.stringify([`@baidu/cba-preset-${projectType}`])
    }
  });
}

async function initFn(prompt: IPrompt) {
  const {projectName} = prompt;
  logger.info('初始化Pagemaker 组件扩展包');
  await downloadTemplate(projectName, 'pagemaker-npm');
  generateUserConfig(prompt, projectName);
}

export default (api: any) => {
  addProject({
    name: 'Pagemaker 组件扩展包',
    key: 'pagemaker-npm',
    initFn
  });
};
